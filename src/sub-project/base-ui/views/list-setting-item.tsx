/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/6/11
 *
 * @description
 */
import { BaseComponent } from "../base-component";
import { TextStyle } from "../../theme/text-styles";
import { Style, Text, View } from "@hippy/react";
import React from "react";
import { ABCStyles, Color, Colors, flattenStyles, Sizes, TextStyles } from "../../theme";
import { RadioButtonGroup } from "./radio";
import _ from "lodash";
import { RightArrowView } from "../iconfont/iconfont-view";
import { AddressPickerAddressInfo, showAddressPicker } from "../picker/address-picker";
import { AbcTextInput, KeyboardType, TextFormatter } from "./abc-text-input";
import { NumberKeyboardBuilder } from "./keyboards/number-keyboard";
import { TimeUtils } from "../../common-base-module/utils";
import { TimePicker } from "../picker/time-picker";
import { showOptionsBottomSheet } from "../dialog/bottom_sheet";
import { ignore } from "../../common-base-module/global";
import { AbcView } from "./abc-view";
import { DeviceUtils } from "../utils/device-utils";
import { AbcText } from "./abc-text";

export enum ListSettingItemStyle {
    normal,
    expandIcon, ////在右侧会显示一个>图标
}

export interface ListSettingItemProps {
    key?: string | number;
    title?: string;
    minTitleWidth?: number;
    titleStyle?: TextStyle; //TextStyles.t16NT2
    starTitle?: string;
    starTitleStyle?: Style | Style[];
    startIcon?: () => JSX.Element;
    content?: string;
    contentHint?: string;
    endIcon?: () => JSX.Element;
    itemStyle?: ListSettingItemStyle; //default normal
    onClick?: () => void;
    contentBuilder?: () => JSX.Element;
    bottomLine?: boolean;
    height?: number;
    showErrorBorder?: boolean;
    style?: Style | Style[];

    contentStyle?: Style | Style[];
    contentTextStyle?: Style | Style[];
    contentHintTextStyle?: Style | Style[];
    contentNumberOfLine?: number;
    rightArrowColor?: Color;
    leftAlignment?: Style; //左边标题对齐方式
    arrowStyle?: Style; //箭头对齐方式
}

const kMinTitleWidth = Sizes.dp80;

export class ListSettingItem extends BaseComponent<ListSettingItemProps> {
    static defaultProps = {
        height: Sizes.listItemHeight,
        bottomLine: false,
        showErrorBorder: false,
        itemStyle: ListSettingItemStyle.normal,
    };

    constructor(props: ListSettingItemProps) {
        super(props);
    }

    render(): JSX.Element {
        const {
            style,
            height,
            title,
            titleStyle,
            starTitle,
            starTitleStyle,
            startIcon,
            endIcon,
            itemStyle,
            showErrorBorder,
            contentBuilder,
            content,
            contentHint,
            onClick,
            bottomLine,
            contentStyle,
            contentTextStyle,
            contentHintTextStyle,
            minTitleWidth = kMinTitleWidth,
            key,
            contentNumberOfLine,
            rightArrowColor,
            leftAlignment,
            arrowStyle,
        } = this.props;

        /**
         * 阻止bottomLine与errorLine显示重合
         * bottomLine && !showErrorBorder ? ABCStyles.bottomLine : {}
         */
        return (
            <AbcView
                key={key}
                style={[
                    bottomLine && !showErrorBorder ? ABCStyles.bottomLine : {},
                    !!showErrorBorder
                        ? { ...ABCStyles.bottomLine, borderColor: Colors.errorBorder, backgroundColor: Colors.errorBorderBg }
                        : {},
                    {
                        alignItems: "center",
                        flexDirection: "row",
                        minHeight: height,
                        ...flattenStyles(style),
                    },
                ]}
                onClick={onClick}
            >
                {!!showErrorBorder && (
                    <View
                        style={[
                            {
                                position: "absolute",
                                top: 0,
                                left: 0,
                                right: 0,
                                height: Sizes.dp12,
                                backgroundColor: style?.backgroundColor ?? Colors.white,
                                zIndex: 1,
                            },
                        ]}
                    />
                )}
                <View
                    style={{
                        ...ABCStyles.rowAlignCenter,
                        minWidth: minTitleWidth,
                        minHeight: height,
                        alignSelf: "flex-start",
                        ...flattenStyles(leftAlignment),
                    }}
                >
                    <Text style={[titleStyle ?? TextStyles.t16NT2.copyWith({ color: Colors.t2 }), {}]}>{title ?? ""}</Text>
                    {!_.isEmpty(starTitle) && (
                        <Text style={[TextStyles.t16NY2.copyWith({ color: Colors.Y1 }), flattenStyles(starTitleStyle)]}>
                            {starTitle ?? ""}
                        </Text>
                    )}
                    {startIcon && startIcon()}
                </View>

                <View style={[{ flex: 1 }, flattenStyles(contentStyle)]}>
                    {contentBuilder && contentBuilder()}
                    {!contentBuilder && (
                        <AbcText
                            style={_.isEmpty(content) ? contentHintTextStyle ?? TextStyles.t16NT4 : contentTextStyle ?? TextStyles.t16NT1}
                            numberOfLines={contentNumberOfLine}
                        >
                            {(_.isEmpty(content) ? contentHint : content) ?? ""}
                        </AbcText>
                    )}
                </View>
                {endIcon && endIcon()}
                {itemStyle == ListSettingItemStyle.expandIcon && (
                    <View style={[{ justifyContent: "center", marginTop: Sizes.dp3 }, flattenStyles(arrowStyle)]}>
                        <RightArrowView color={rightArrowColor} />
                    </View>
                )}
            </AbcView>
        );
    }
}

interface ListSettingEditItemProps extends ListSettingItemProps {
    maxLength?: number;
    onChanged?: (value: string) => void;
    onBlur?: () => void;
    onEndEditing?: (value: string) => void;
    textInputStyle?: Style;
    editable?: boolean;
    autoFocus?: boolean;
    keyboardType?: KeyboardType;
    keyboardDistanceFromTextField?: number;
    customPanelBuilder?: () => JSX.Element;

    formatter?: TextFormatter | TextFormatter[];

    contentLeftAlign?: boolean; //内容左对齐,默认true
    enableDefaultToolBar?: boolean; //默认工具栏
}

export class ListSettingEditItem extends BaseComponent<ListSettingEditItemProps> {
    static defaultProps = {
        height: Sizes.listItemHeight,
        bottomLine: false,
        showErrorBorder: false,
        itemStyle: ListSettingItemStyle.normal,
        contentLeftAlign: true,
    };

    constructor(props: ListSettingItemProps) {
        super(props);
    }

    render(): JSX.Element {
        const {
            textInputStyle,
            keyboardType,
            formatter,
            editable = true,
            content,
            contentHint,
            onChanged,
            onEndEditing,
            onBlur,
            autoFocus,
            contentLeftAlign = true,
            keyboardDistanceFromTextField,
            customPanelBuilder,
            maxLength,
            enableDefaultToolBar,
            contentHintTextStyle,
            ...otherProps
        } = this.props;
        let _keyboardPanel: NumberKeyboardBuilder | undefined = undefined;
        if (keyboardType == "numeric") {
            _keyboardPanel = new NumberKeyboardBuilder({});
        }
        let textInput: AbcTextInput | null = null;
        return (
            <ListSettingItem
                {...otherProps}
                onClick={() => editable && textInput?.focus()}
                contentBuilder={() => {
                    return (
                        <AbcTextInput
                            ref={(ref) => (textInput = ref)}
                            style={flattenStyles([
                                {
                                    textAlign: contentLeftAlign ? "left" : "right",
                                    ...TextStyles.t16NT1,
                                    height: Sizes.dp30,
                                    paddingHorizontal: DeviceUtils.isAndroid() ? -6 : undefined,
                                },
                                textInputStyle,
                            ])}
                            maxLength={maxLength}
                            formatter={formatter}
                            defaultValue={content}
                            syncTextOnBlur={true}
                            placeholder={contentHint}
                            placeholderTextColor={contentHintTextStyle}
                            multiline={false}
                            autoFocus={autoFocus}
                            editable={editable}
                            onChangeText={onChanged}
                            keyboardType={keyboardType}
                            customKeyboardBuilder={_keyboardPanel}
                            customPanelBuilder={customPanelBuilder}
                            keyboardDistanceFromTextField={keyboardDistanceFromTextField}
                            onEndEditing={onEndEditing}
                            onBlur={onBlur}
                            enableDefaultToolBar={enableDefaultToolBar}
                        />
                    );
                }}
            />
        );
    }
}

interface ListSettingDateEditItemProps extends ListSettingItemProps {
    onChanged?: (date: Date) => void;
    editable?: boolean;
    date?: Date;

    contentLeftAlign?: boolean; //内容左对齐,默认true
}

export class ListSettingDateEditItem extends BaseComponent<ListSettingDateEditItemProps> {
    static defaultProps = {
        height: Sizes.listItemHeight,
        bottomLine: false,
        showErrorBorder: false,
        itemStyle: ListSettingItemStyle.normal,
        contentLeftAlign: true,
    };

    private _date?: Date;

    constructor(props: ListSettingItemProps) {
        super(props);
        this._date = this.props.date;
    }

    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
    componentWillReceiveProps(nextProps: Readonly<ListSettingDateEditItemProps>) {
        this._date = nextProps.date;
    }

    render(): JSX.Element {
        const { editable = true, content, onChanged, contentLeftAlign = true, ...otherProps } = this.props;
        ignore(content, contentLeftAlign);
        return (
            <ListSettingItem
                {...otherProps}
                onClick={() =>
                    editable &&
                    TimePicker.show(this._date).then((date) => {
                        if (date != undefined) {
                            onChanged?.(date);
                        }
                    })
                }
                content={TimeUtils.formatDate(this._date)}
            />
        );
    }
}

interface ListSettingRadiosItemProps {
    title?: string;
    showTitle?: boolean;
    check?: string;
    style?: Style | Style[];
    titleStyle?: TextStyle;
    starTitle?: string;
    options: string[];
    marginBetweenItem?: number;
    enable?: boolean;
    height?: number;
    bottomLine?: boolean;
    onChanged?: (option: string, index: number) => void;
    disabledOption?: string[]; //不可编辑的列表
    labelStyle?: TextStyle;
    groupStyle?: Style | Style[];
    leftAlignment?: Style; //左侧标题对齐样式
    contentStyle?: Style;
    upperLowerSpace?: number;
}

export class ListSettingRadiosItem extends BaseComponent<ListSettingRadiosItemProps> {
    static defaultProps = {
        height: Sizes.listItemHeight,
        showTitle: true,
    };

    constructor(props: ListSettingRadiosItemProps) {
        super(props);
    }

    render(): JSX.Element {
        const {
            options,
            check,
            enable,
            title,
            titleStyle,
            starTitle,
            height,
            marginBetweenItem,
            bottomLine,
            showTitle,
            style,
            disabledOption,
            labelStyle,
            groupStyle,
            leftAlignment,
            contentStyle,
            upperLowerSpace,
        } = this.props;
        return (
            <View
                style={[
                    bottomLine ? ABCStyles.bottomLine : {},
                    {
                        height: height,
                        flexDirection: "row",
                        alignItems: "center",
                    },
                    flattenStyles(style),
                ]}
            >
                <View
                    style={[
                        ABCStyles.rowAlignCenter,
                        options?.length > 2 ? {} : { minWidth: title || starTitle || showTitle ? kMinTitleWidth : 0 },
                        {
                            height: Sizes.listItemHeight,
                            alignSelf: "flex-start",
                        },
                        flattenStyles(leftAlignment),
                    ]}
                >
                    {title && <Text style={titleStyle ?? TextStyles.t16NT2.copyWith({ color: Colors.t2 })}>{title}</Text>}
                    {starTitle && <Text style={TextStyles.t16NT2.copyWith({ color: Colors.Y2 })}>{starTitle}</Text>}
                </View>
                <View style={{ ...flattenStyles(contentStyle) }}>
                    <RadioButtonGroup
                        labels={options}
                        picked={check ?? ""}
                        disabled={enable ? disabledOption : options}
                        marginBetweenItem={marginBetweenItem}
                        labelStyle={labelStyle ?? TextStyles.t16NT1}
                        onChanged={(label, index) => this.props.onChanged?.(label, index)}
                        groupStyle={groupStyle}
                        upperLowerSpace={upperLowerSpace}
                    />
                </View>
            </View>
        );
    }
}

/// 地址编辑控件
interface ListSettingAddressEditItemProps {
    title?: string;
    titleStyle?: TextStyle; //TextStyles.t16NT2
    minTitleWidth?: number;
    addressInfo?: AddressPickerAddressInfo;
    hint?: string;
    contentHintTextStyle?: Style | Style[];
    bottomLine?: boolean;
    showErrorBorder?: boolean;
    editable?: boolean;
    defaultAddressInfo?: AddressPickerAddressInfo; //门店默认设置的地址
    starTitle?: string;
    onChanged?: (info: AddressPickerAddressInfo) => void;
}

export class ListSettingAddressEditItem extends BaseComponent<ListSettingAddressEditItemProps> {
    async _handleClick(): Promise<void> {
        const { editable = true } = this.props;
        if (!editable) return;

        const { addressInfo, defaultAddressInfo } = this.props;
        const _addressInfo = await showAddressPicker(
            (!!addressInfo?.isUseStoreAddress ? defaultAddressInfo : addressInfo) ?? defaultAddressInfo
        ); //年龄选择初始定位到40年前
        if (_addressInfo == null) return;
        if (this.props.onChanged != null) {
            this.props.onChanged(_addressInfo);
        }
    }

    render(): JSX.Element {
        const {
            title,
            titleStyle,
            addressInfo,
            showErrorBorder,
            bottomLine,
            hint,
            contentHintTextStyle,
            editable = true,
            minTitleWidth,
            starTitle,
        } = this.props;

        return (
            <ListSettingItem
                itemStyle={editable ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal}
                title={title}
                titleStyle={titleStyle}
                contentHint={hint}
                contentHintTextStyle={contentHintTextStyle}
                content={addressInfo?.displayString != "" ? addressInfo?.displayString : undefined}
                showErrorBorder={showErrorBorder}
                minTitleWidth={minTitleWidth}
                bottomLine={bottomLine}
                starTitle={starTitle}
                onClick={async () => {
                    await this._handleClick();
                }}
            />
        );
    }
}

interface ListSettingOptionsItemProps extends ListSettingItemProps {
    options: string[];

    onChanged?(index: number, option: string): void;
}

export class ListSettingOptionsItem extends BaseComponent<ListSettingOptionsItemProps> {
    static defaultProps = {
        height: Sizes.listItemHeight,
        bottomLine: false,
        showErrorBorder: false,
        itemStyle: ListSettingItemStyle.normal,
    };

    constructor(props: ListSettingOptionsItemProps) {
        super(props);
    }

    render(): JSX.Element {
        const {
            style,
            height,
            title,
            titleStyle,
            starTitle,
            itemStyle,
            showErrorBorder,
            contentBuilder,
            content,
            contentHint,
            bottomLine,
            onChanged,
        } = this.props;
        return (
            <View
                accessibilityLabel={title}
                style={[
                    bottomLine ? ABCStyles.bottomLine : {},
                    showErrorBorder ? ABCStyles.borderOneRed : {},
                    {
                        alignItems: "center",
                        flexDirection: "row",
                        minHeight: height,
                        ...flattenStyles(style),
                    },
                ]}
            >
                <View
                    style={{
                        ...ABCStyles.rowAlignCenter,
                        minWidth: kMinTitleWidth,
                        height: Sizes.listItemHeight,
                        alignSelf: "flex-start",
                    }}
                >
                    <Text style={[titleStyle ?? TextStyles.t16NT2, {}]}>{title ?? ""}</Text>
                    {!_.isEmpty(starTitle) && <Text style={TextStyles.t16NY2}>{starTitle ?? ""}</Text>}
                </View>

                <View
                    style={{ flex: 1 }}
                    onClick={async () => {
                        const selects = await showOptionsBottomSheet({
                            title: contentHint ?? "",
                            options: this.props.options,
                            initialSelectIndexes: new Set([this.props.options.indexOf(this.props.content ?? "")]),
                        });

                        if (_.isEmpty(selects)) return;

                        const index = _.first(selects)!;
                        onChanged?.(index, this.props.options[index]);
                    }}
                >
                    {contentBuilder && contentBuilder()}
                    {!contentBuilder && (
                        <Text style={_.isEmpty(content) ? TextStyles.t16NT4 : TextStyles.t16NT1}>
                            {(_.isEmpty(content) ? contentHint : content) ?? ""}
                        </Text>
                    )}
                </View>

                {itemStyle == ListSettingItemStyle.expandIcon && <RightArrowView />}
            </View>
        );
    }
}
