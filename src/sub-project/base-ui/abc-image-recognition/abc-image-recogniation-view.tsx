import React from "react";
import { AbcUIManagerModule } from "../abc-ui-manager-module";
import { HippyTypes } from "@hippy/react";

export interface ImageRecognitionResult {
    message?: string;
    success?: boolean;
    // 截图
    image?: string;
    imagePath?: string;
    height?: number;
    width?: number;
    // 原图
    originImage?: string;
    originalImagePath?: string;
    originHeight?: number;
    originWidth?: number;
}

interface AbcImageRecogniationViewProps {
    style?: HippyTypes.Style | HippyTypes.Style[];
}

export class AbcImageRecogniationView extends React.Component<AbcImageRecogniationViewProps, {}> {
    instance: HTMLDivElement | null = null;

    finishRecognition(path: string): Promise<ImageRecognitionResult> {
        return new Promise((resolve) => {
            AbcUIManagerModule.callUIFunction(this.instance, "finishRecognition", [path], resolve);
        });
    }

    switchCamera(): Promise<void> {
        return new Promise((resolve) => {
            AbcUIManagerModule.callUIFunction(this.instance, "switchCamera", resolve);
        });
    }

    setFlashlight(enable: boolean): Promise<{
        success: boolean
    }> {
        return new Promise((resolve) => {
            AbcUIManagerModule.callUIFunction(this.instance, "setFlashlight", [enable], resolve);
        });
    }

    setModelPath(path: String): Promise<boolean> {
        return new Promise((resolve) => {
            AbcUIManagerModule.callUIFunction(this.instance, "setModelPath", [path], resolve);
        });
    }

    setDirectionModelPath(path: String): Promise<boolean> {
        return new Promise((resolve) => {
            AbcUIManagerModule.callUIFunction(this.instance, "setDirectionModelPath", [path], resolve);
        });
    }

    setModelProcessSize(target_size: number, direction_origin_size: number, direction_crop_size: number): Promise<boolean> {
        return new Promise((resolve) => {
            AbcUIManagerModule.callUIFunction(
                this.instance,
                "setModelProcessSize",
                [target_size, direction_origin_size, direction_crop_size],
                resolve
            );
        });
    }

    render(): JSX.Element {
        const { ...nativeProps } = this.props;
        return (
            <div
                // @ts-ignore
                nativeName="ImageRecognition"
                ref={(ref: HTMLDivElement) => {
                    this.instance = ref;
                }}
                {...nativeProps}
            />
        );
    }
}
