/**
 * AbcListView 后台渲染功能测试
 */
import React from 'react';
import { render, act } from '@testing-library/react-native';
import { AbcListView } from '../abc-list-view';

// Mock setTimeout and clearTimeout
jest.useFakeTimers();

describe('AbcListView Background Rendering', () => {
    const mockRenderRow = jest.fn((item) => <div>{item.text}</div>);
    
    const createTestData = (count: number) => {
        return Array.from({ length: count }, (_, index) => ({
            id: index,
            text: `Item ${index}`
        }));
    };

    beforeEach(() => {
        jest.clearAllMocks();
        jest.clearAllTimers();
    });

    afterEach(() => {
        jest.runOnlyPendingTimers();
        jest.useRealTimers();
        jest.useFakeTimers();
    });

    test('should start background rendering after initial render', () => {
        const testData = createTestData(500); // 500条数据
        
        const { rerender } = render(
            <AbcListView
                initialListSize={100}
                dataSource={testData}
                renderRow={mockRenderRow}
                numberOfRows={testData.length}
            />
        );

        // 初始应该只渲染 initialListSize * 3 = 300 条
        expect(mockRenderRow).toHaveBeenCalledTimes(300);

        // 模拟时间流逝，触发后台渲染
        act(() => {
            jest.advanceTimersByTime(100); // 第一次后台渲染
        });

        // 应该渲染额外的 10 条（backgroundRenderBatchSize）
        expect(mockRenderRow).toHaveBeenCalledTimes(310);

        // 继续模拟时间流逝
        act(() => {
            jest.advanceTimersByTime(100); // 第二次后台渲染
        });

        expect(mockRenderRow).toHaveBeenCalledTimes(320);
    });

    test('should stop background rendering when all data is rendered', () => {
        const testData = createTestData(50); // 只有50条数据
        
        render(
            <AbcListView
                initialListSize={100}
                dataSource={testData}
                renderRow={mockRenderRow}
                numberOfRows={testData.length}
            />
        );

        // 初始渲染应该包含所有数据，因为数据量小于 initialListSize * 3
        expect(mockRenderRow).toHaveBeenCalledTimes(50);

        // 模拟时间流逝，不应该有额外的渲染
        act(() => {
            jest.advanceTimersByTime(1000);
        });

        expect(mockRenderRow).toHaveBeenCalledTimes(50);
    });

    test('should restart background rendering when dataSource changes', () => {
        const initialData = createTestData(200);
        const newData = createTestData(400);
        
        const { rerender } = render(
            <AbcListView
                initialListSize={50}
                dataSource={initialData}
                renderRow={mockRenderRow}
                numberOfRows={initialData.length}
            />
        );

        // 初始渲染 150 条 (50 * 3)
        expect(mockRenderRow).toHaveBeenCalledTimes(150);

        // 模拟后台渲染
        act(() => {
            jest.advanceTimersByTime(100);
        });
        expect(mockRenderRow).toHaveBeenCalledTimes(160);

        // 更新数据源
        rerender(
            <AbcListView
                initialListSize={50}
                dataSource={newData}
                renderRow={mockRenderRow}
                numberOfRows={newData.length}
            />
        );

        // 应该重新开始后台渲染
        act(() => {
            jest.advanceTimersByTime(100);
        });

        // 验证新的后台渲染正在进行
        expect(mockRenderRow).toHaveBeenCalledTimes(170);
    });

    test('should stop background rendering when component unmounts', () => {
        const testData = createTestData(500);
        
        const { unmount } = render(
            <AbcListView
                initialListSize={100}
                dataSource={testData}
                renderRow={mockRenderRow}
                numberOfRows={testData.length}
            />
        );

        // 卸载组件
        unmount();

        // 模拟时间流逝，不应该有额外的渲染
        const renderCallsBeforeUnmount = mockRenderRow.mock.calls.length;
        
        act(() => {
            jest.advanceTimersByTime(1000);
        });

        expect(mockRenderRow).toHaveBeenCalledTimes(renderCallsBeforeUnmount);
    });

    test('should stop background rendering when user scrolls to specific index', () => {
        const testData = createTestData(500);
        let listViewRef: AbcListView | null = null;
        
        render(
            <AbcListView
                ref={(ref) => { listViewRef = ref; }}
                initialListSize={100}
                dataSource={testData}
                renderRow={mockRenderRow}
                numberOfRows={testData.length}
            />
        );

        const initialRenderCount = mockRenderRow.mock.calls.length;

        // 用户滚动到指定位置
        act(() => {
            listViewRef?.scrollToIndex(0, 400, false);
        });

        // 模拟时间流逝，后台渲染应该已停止
        act(() => {
            jest.advanceTimersByTime(1000);
        });

        // 渲染数量应该只增加到用户滚动的位置 + 10
        const expectedRenderCount = 400 + 10;
        expect(mockRenderRow).toHaveBeenCalledTimes(expectedRenderCount);
    });
});
