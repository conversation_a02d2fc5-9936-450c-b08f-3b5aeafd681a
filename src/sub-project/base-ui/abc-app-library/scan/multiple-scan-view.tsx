import React from "react";
import { AbcUIManagerModule } from "../../abc-ui-manager-module";

export interface MultipleScanDetail {
    corners: { x: string; y: string }[];
    bounds: { x: string; y: string; width: string; height: string };
    code: string;
}

interface MultipleScanViewProps {
    style?: React.CSSProperties;
    onChange?: (code: { barcodes: MultipleScanDetail[] }) => void;
    content?: string;
}

class MultipleScanView extends React.Component<MultipleScanViewProps> {
    instance: HTMLDivElement | null = null;
    toggleTorchStatus: boolean;

    constructor(props: MultipleScanViewProps) {
        super(props);
        this.toggleTorchStatus = false;
    }

    toggleTorch(): void {
        AbcUIManagerModule.callUIFunction(this.instance, "toggleTorch", [!this.toggleTorchStatus]);
    }

    async startCamera(): Promise<void> {
        AbcUIManagerModule.callUIFunction(this.instance, "startCamera", []);
    }

    stopCamera(): void {
        AbcUIManagerModule.callUIFunction(this.instance, "stopCamera", []);
    }

    startVibrate(): void {
        AbcUIManagerModule.callUIFunction(this.instance, "startVibrate", []);
    }

    toggleFlash(): void {
        AbcUIManagerModule.callUIFunction(this.instance, "toggleTorch", []);
    }

    continueAnalysis(): void {
        AbcUIManagerModule.callUIFunction(this.instance, "continueAnalysis", []);
    }

    componentDidMount(): void {
        this.startCamera();
    }

    componentWillUnmount(): void {
        this.stopCamera();
    }

    render(): JSX.Element {
        const { ...nativeProps } = this.props;
        return (
            <div
                // @ts-ignore
                nativeName="MultipleScanView"
                ref={(ref: HTMLDivElement) => {
                    this.instance = ref;
                }}
                {...nativeProps}
            />
        );
    }
}

export default MultipleScanView;
