import _, { range } from "lodash";
import { <PERSON><PERSON><PERSON><PERSON>rro<PERSON>, ABCApiNetwork } from "../net";
import { JsonMapper, JsonProperty } from "../common-base-module/json-mapper/json-mapper";
import { ClinicAgent } from "../base-business/data/clinic-agent";
import { Toast } from "../base-ui/dialog/toast";
import { WxApi } from "../base-business/wxapi/wx-api";
import { errorSummary, errorToStr, UrlUtils } from "../common-base-module/utils";
import { sharedPreferences } from "../base-business/preferences/shared-preferences";
import { environment, ServerEnvType } from "../base-business/config/environment";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import React from "react";
import { URLProtocols } from "../url-dispatcher";
import { LoginPage } from "../login/login-page";
import { Subject } from "rxjs";
import { LoadingDialog } from "../base-ui/dialog/loading-dialog";
import { clinicScopeMemoryCache } from "../common-base-module/cleanup/memory-cache";
import {
    chainSharedPreferences,
    clinicSharedPreferences,
    employeeSharedPreferences,
} from "../base-business/preferences/scoped-shared-preferences";
import { ClinicEdition, HisType, MallOrderDetailRsp } from "./data/bean";
import { InventoryClinicConfig } from "../inventory/data/inventory-bean";
import { DentistryConfig, RegistrationType } from "../registration/dentistry/data/bean";
import { DentistryAgent } from "../registration/dentistry/data/dentistry-agent";
import { ChainBasePropertyConfig, ClinicTraceCodeConfig } from "../data/online-property-config-provder-bean";
import { RuntimeConstantsManager, RuntimeConstantsType } from "../data/constants/runtime-constants-manager";
import abcI18Next from "../language/config";
import { LangTypeEnum } from "../language/bean";
import { ClinicAllEmployeesItem, ClinicDictionaryInfo } from "../base-business/data/clinic-data";
import { EcAuthBindListRsp } from "../order-cloud/data/order-cloud-bean";
import { OrderCloudAgent } from "../order-cloud/data/order-cloud-agent";
import { EncryptUtils } from "../base-business/utils/encrypt";
import { ApprovalSettingItem } from "../inventory/data/inventory-pharmacy-bean";
import { InventoryPharmacyAgent } from "../inventory/data/inventory-pharmacy-agent";
import { getDistributeConfig, initDistributeViewConfig } from "../views-distribute/utils";
import { ThemeManager, ThemeType } from "../theme/themes";
import { ClinicShebaoConfig, SheBaoRestrictConfig } from "../base-business/mix-agent/data/shebao-bean";
import { ProfitCategoryTypesItem } from "../base-business/data/beans";
import { AiPictureTaskData } from "../data/inventory/inventory-beans";

export enum ModuleIds {
    MODULE_ID_NONE,
    //-挂号
    MODULE_ID_REGISTER = 1, //挂号

    //-门诊
    MODULE_ID_OUTPATIENT = 2, //门诊

    //-收费
    MODULE_ID_CHARGE = 3, //收费

    //-药房
    MODULE_ID_PHARMACY = 4, //药房

    //-患者
    MODULE_ID_PATIENT = 5, //患者
    MODULE_ID_PATIENT_MEMBER = 11, //患者 - 会员
    MODULE_ID_PATIENT_REVISITED = 302, // 患者-患者随访

    //-统计
    MODULE_ID_STATISTICS = 6, //统计
    MODULE_ID_STATISTICS_BUSINESS = 201, // 统计-经营统计
    MODULE_ID_STATISTICS_OPERATIONAL_SITUATION = 202, // 统计-运营统计
    MODULE_ID_STATISTICS_ACHIEVEMENT = 206, // 统计-业绩统计
    MODULE_ID_STATISTICS_ACHIEVEMENT_BILLING = 6002, // 统计-业绩统计-开单业绩
    MODULE_ID_STATISTICS_BUSINESS_OVERVIEW = 1001, // 统计-经营统计-经营概况
    MODULE_ID_STATISTICS_CLINIC_REVENUE = 1002, // 统计-经营统计-门店收入

    // 医院统计
    MODULE_ID_HOSPITAL_REVENUE_DOCTOR_ACHIEVEMENT = 5250514, // 营收统计-医生业绩
    MODULE_ID_HOSPITAL_REVENUE_CHARGE_REPORT = 5250415, // 营收统计-收费报表
    MODULE_ID_HOSPITAL_MEDICAL_STATISTICS = 52513, // 医务统计
    MODULE_ID_HOSPITAL_OPERATION_OVERVIEW = 2001, // 医务统计-运营概况

    //-管理
    MODULE_ID_MANAGE = 7, // 管理

    //-库存
    MODULE_ID_INVENTORY, // 库存
    MODULE_ID_INVENTORY_MEDICINE = 51209, //库存 - 药品物资（原101）
    MODULE_ID_INVENTORY_IN = 51210, //      库存 - 入库（原103）
    MODULE_ID_INVENTORY_OUT = 104, //     库存 - 出库
    MODULE_ID_INVENTORY_EXCHANGE = 105, //库存 - 调拨
    MODULE_ID_INVENTORY_STOCK = 106, //   库存-盘点权限
    MODULE_ID_INVENTORY_PURCHASE = 107, //库存 - 供应商
    MODULE_ID_INVENTORY_PURCHASE_APPLY = 108, //库存 - 结算申请
    MODULE_ID_INVENTORY_PURCHASE_REVIEW = 109, //库存 - 结算审核
    MODULE_ID_INVENTORY_PURCHASE_IN = 51210, //库存 - 采购入库
    MODULE_ID_INVENTORY_APPLY_IN = 51211, //库存 - 领用
    MODULE_ID_INVENTORY_LOSS_OUT = 51213, //库存 - 报损
    MODULE_ID_INVENTORY_PRODUCE_OUT = 110, // 库存 - 生产出库

    //-检验
    MODULE_ID_EXAMINATION = 9, //检验

    //-执行站
    MODULE_ID_NURSE_STATION = 10, //执行站

    //-门诊代录
    MODULE_ID_OUTPATIENT_RECORD = 12, //门诊代录

    //-营销
    MODULE_ID_MARKETING = 13, //营销

    //微诊所
    MODULE_ID_WE_CLINIC = 14, //微诊所

    //儿保
    MODULE_ID_CHILDREN = 15, //儿保

    //采购
    MODULE_ID_CLINIC_PURCHASE = 102, //采购

    GUA_HAO_YU_YUE = 1,
    MEN_ZHEN = 2,
    SHOU_FEI = 3,
    YAO_FANG = 4,
    HUAN_ZHE = 5,
    TONG_JI = 6,
    GUAN_LI = 7,
    KU_CUN = 8,
    JIAN_YAN = 9,
    ZHI_XING_ZHAN = 10,
    HUI_YUAN_GUAN_LI = 11,
    MEN_ZHEN_DAI_LU = 12,
    YING_XIAO = 13,
    WEI_ZHEN_SUO = 14,
    DONG_BAO = 15,
    YAO_PIN_WU_ZI = 101,
    CAI_GOU = 102,
    RU_KU = 103,
    CHU_KU = 104,
    DIAO_BO = 105,
    PAN_DIAN = 106,
    GONG_YING_SHANG = 107,
    JIE_SUAN_SHEN_QING = 108,
    JIE_SUAN_SHEN_HE = 109,
    YING_SHOU_TONG_JI = 201,
    YUN_YING_FEN_XI = 202,
    JIN_XIAO_CUN_TONG_JI = 203,
    HUI_YUAN_TONG_JI = 205,
    YE_JI_TONG_JI = 206,
    MAN_BING_KANG_FU_TONG_JI = 207,
    YI_BAO_TONG_JI = 208,
    JIA_YI_TONG_JI = 209,
    YUN_YING_BAO_BIAO = 210,
    HUAN_ZHE_DANG_AN = 301,
    HUAN_ZHE_SUI_FANG = 302,
    ZAI_XIAN_GOU_TONG = 303,
    YING_SHOU_GAI_KUANG = 1001,
    SHOU_FEI_RI_BAO = 1002,
    REN_YUAN_SHOU_RU = 1003,
    YAO_PIN_SHOU_RU = 1004,
    JIAN_CHA_JIAN_YAN_SHOU_RU = 1005,
    ZHI_LIAO_LI_LIAO_SHOU_RU = 1006,
    SHOU_FEI_TAO_CAN_TONG_JI = 1007,
    GUA_HAO_JIE_SUAN = 1008,
    SHOU_FEI_YUAN_TONG_JI = 1009,
    SHOU_FEI_MING_XI = 1010,
    CAI_WU_BAO_BIAO = 1011,
    TI_CHENG_TONG_JI = 1012,
    SHOU_FEI_FEN_LEI_TONG_JI = 1013,
    SHOU_FEI_XIANG_MU_TONG_JI = 1014,
    KONG_ZHONG_YAO_FANG_TONG_JI = 1015,
    YUN_YING_GAI_KUANG = 2001,
    FU_ZHEN_FEN_XI = 2003,
    HUAN_ZHE_QING_DAN = 2004,
    YUN_YING_RI_BAO = 2005, // 统计-运营分析-运营日报
    MEN_ZHEN_RI_ZHI = 2006,
    ZHI_XING_TONG_JI = 2007,
    SUI_FANG_TONG_JI = 2008,
    BING_ZHONG_FEN_XI = 2009,
    YAO_PIN_GAI_KUANG = 3001,
    XIAO_SHOU_TONG_JI = 3002,
    CAI_GOU_TONG_JI = 3003,
    RU_KU_TONG_JI = 3004,
    GONG_YING_SHANG_TONG_JI = 3005,
    CHU_KU_TONG_JI = 3006,
    DIAO_BO_TONG_JI = 3007,
    PAN_YING_PAN_KUI = 3008,
    JIN_XIAO_CUN_QING_DAN = 3009,
    JIE_SUAN_TONG_JI = 3010,
    HUI_YUAN_MING_XI = 5001,
    JIAO_YI_LIU_SHUI = 5002,
    KAI_DAN_YE_JI = 6002,
    ZHI_XING_YE_JI = 6003,
    HUI_YUAN_CHONG_ZHI_YE_JI = 6004,
    JIAN_YAN_YE_JI = 6005,
    KUA_DIAN_ZHI_XING_JIE_SUAN = 6006,
    FA_YAO_YUAN_YE_JI = 6007,
    JIAN_DANG_HUAN_ZHE = 7001,
    SHOU_FEI_TONG_JI = 7002,
    LIAO_XIAO_TONG_JI = 7003,
    ZHENG_TI_GAI_LAN = 9001,
    QIAN_YUE_TONG_JI = 9002,
    XU_YUE_TONG_JI = 9003,
    YUN_YING_RI_BAO_S = 10001, // 统计-运营报表-运营日报
    YUN_YING_WAN_BAO = 10002,

    HOSPITAL_DOCTOR_STATION = 501, // 住院医生站
    HOSPITALDOCTORBED = 50101, // 住院医生站-床位
    HOSPITAL_DOCTOR_MEDICAL_PRESCRIPTION = 50102, // 住院医生站-医嘱病历

    HOSPITAL_NURSE_STATION = 502, // 住院护士站
    HOSPITAL_NURSE_BED = 50201, // 住院护士站-床位
    HOSPITAL_NURSE_PRESCRIPTION = 50202, // 住院护士站-医嘱
    HOSPITAL_NURSE_NURSING = 50203, // 住院护士站-护理
    HOSPITAL_NURSE_MEDICINE = 50204, // 住院护士站-药品
    HOSPITAL_NURSE_DAILY = 50206, // 住院护士站-日常
    HOSPITAL_NURSE_COST = 50207, // 住院护士站-费用
    HOSPITAL_NURSE_STATISTICS = 50208, // 住院护士站-统计

    hospitalChargeStation = 507, // 收费
    hospitalChargeHospital = 50702, // 收费-住院
    hospitalChargeRegister = 50703, // 收费-入院登记

    hospitalPharmacyStation = 508, // 药房
    hospitalPharmacyHospital = 50802, // 药房-住院

    hospitalExaminationSampleCollection = 50901, // 检验工作站-样本采集
    hospitalExaminationSampleVerify = 50902, // 检验工作站-样本核收
    hospitalExaminationSampleExamination = 50903, // 检验工作站-样本检验

    hospitalInspectRegistration = 51001, // 检查工作站-预约登记
    hospitalInspectDiagnosis = 51002, // 检查工作站-检查诊断
    hospitalInspectSetting = 51003, // 检查工作站-检查设置

    commonSetting = 51701, // 通用设置
    outpatientSetting = 51702, // 门诊设置
    hospitalSetting = 51703, // 住院设置

    hospitalOutpatientStation = 505, // 门诊医生站

    hospitalDashboard = 0,

    // 药店管家相关模块
    pharmacyRetailSales = 3, //零售
    pharmacyRetailSalesOrder = 70102, // 零售单
    pharmacySettlementOrder = 70103, // 对账

    pharmacyInventory = 8, //库存
    pharmacyGoods = 51209, //库存-商品
    pharmacyPurchase = 51210, //库存-购进
    pharmacyReportDamage = 51213, //库存-报损
    pharmacyStocktaking = 106, //库存-盘点
    pharmacySupplier = 107, //库存-供应商
    pharmacyAdjustPrice = 70206, //库存-调价

    pharmacyMembers = 11, //会员
    pharmacyMarketing = 13, //营销
    pharmacyMedicalInsurance = 16, //医保
    pharmacyGSP = 706, //GSP
    pharmacyFirstBattalion = 70601, //GSP-首营
    pharmacyCheckAccept = 70602, //GSP-验收
    pharmacyStorage = 70603, //GSP-储存
    pharmacyAfterSale = 70604, //GSP-售后
    pharmacyPersonnel = 70605, //GSP-人员
    pharmacySpecialMedicine = 70606, //GSP-特药

    pharmacyExaminationAndApproval = 707, //审批
    // 订单云
    pharmacyOrderCloud = 710, // 订单云
    // 订单云-外卖
    pharmacyTakeOut = 71008, // 外卖
    // 订单云-外卖-工作台
    pharmacyWorkbench = 7100801,
    // 订单云-外卖-订单出库
    pharmacyOrderCloudOut = 71007, // 订单出库
    // 订单云-外卖-商品管理
    pharmacyProductManagement = 7100806,
    // 订单云-外卖-外卖订单统计
    pharmacyTakeOutOrderStatistics = 7100807,

    // 订单云-电商
    pharmacyECommerce = 71009, // 电商
    // 订单云-电商-订单管理
    pharmacyOrderManagement = 71001,
    // 订单云-电商-出库记录
    pharmacyOrderCloudOutRecord = 71002,
    // 订单云-电商-营收统计
    pharmacyOrderRevenueStatistics = 71003,
    // 订单云-电商-商品管理
    pharmacyCommodityManagement = 71004,

    // 订单云设置
    pharmacyOrderCloudSetting = 71006, // 订单云设置

    pharmacyReportForm = 6, //报表
    pharmacyRevenueStatistics = 201, //报表-营收统计
    pharmacyPerformanceStatistics = 206, //报表-业绩统计
    pharmacyInventoryStatistics = 203, //报表-库存统计
    pharmacyMarketingStatistics = 211, //报表-营销统计
    pharmacyMedicalInsuranceStatistics = 208, //报表-医保统计
    pharmacyCommissionReport = 212, //报表-提成报表

    pharmacySetUp = 7, //设置
    pharmacyMechanismSetup = 51704, //设置-机构设置
    pharmacyScheduleSetup = 51709, //设置-排班设置
    pharmacyPricingAndTaxRates = 70903, //设置-定价和税率
    pharmacyRetailSetup = 51712, //设置-零售设置
    pharmacyInventorySetting = 51714, //设置-库存设置
    pharmacyMembershipSetup = 130201, // 设置-会员设置
    pharmacyApprovalSetting = 70907, //设置-审批设置
    pharmacyPrintSetting = 51713, //设置-打印设置
    pharmacyAggregatePayment = 70909, //设置-聚合支付
    pharmacyProductCenter = 51719, //设置-产品中心
}

export class ModuleIdUtils {
    /**
     * 所有库存相关权限
     */
    static All_INVENTORY_MODULE_LIST = [
        ModuleIds.MODULE_ID_INVENTORY,
        ModuleIds.MODULE_ID_INVENTORY_MEDICINE,
        ModuleIds.MODULE_ID_INVENTORY_IN,
        ModuleIds.MODULE_ID_INVENTORY_OUT,
        ModuleIds.MODULE_ID_INVENTORY_EXCHANGE,
        ModuleIds.MODULE_ID_INVENTORY_STOCK,
        ModuleIds.MODULE_ID_INVENTORY_PURCHASE,
        ModuleIds.MODULE_ID_INVENTORY_PURCHASE_APPLY,
        ModuleIds.MODULE_ID_INVENTORY_PURCHASE_REVIEW,
        ModuleIds.MODULE_ID_INVENTORY_PURCHASE_IN,
        ModuleIds.MODULE_ID_INVENTORY_APPLY_IN,
        ModuleIds.MODULE_ID_INVENTORY_LOSS_OUT,
        ModuleIds.pharmacyGoods,
        ModuleIds.pharmacyPurchase,
        ModuleIds.pharmacyReportDamage,
        ModuleIds.pharmacyStocktaking,
        ModuleIds.pharmacySupplier,
        ModuleIds.pharmacyAdjustPrice,
        ModuleIds.MODULE_ID_NONE,
        ModuleIds.MODULE_ID_PHARMACY,
        ModuleIds.MODULE_ID_INVENTORY_PRODUCE_OUT,
    ];
}

export enum RolesType {
    ADMINISTRATOR_ID = 0, //管理员
    ROLE_DOCTOR_ID = 1, //医生
    ROLE_NURSE_ID = 2, //护士
    ROLE_SURVEYOR_ID = 3, //检验师
    ROLE_PHYSIOTHERAPIST_ID = 4, //理疗师
    ROLE_DOCTOR_ASSIST_ID = 5, //医助
    ROLE_CLERK_ID = 6, //其它
    OPTOMETRIST_ID = 7, //视光师
    PROCUREMENT_ID = 8, //采购
    INSPECTION_TECHNICIAN_ID = 10, //检查技师
    CONSULTANT_ID = 11, //咨询师
    QUALITY_MANAGER = 200, //质量管理员
    QUALITY_PERSON_IN_CHARGE = 201, //质量负责人
    PURCHASING_PERSON_IN_CHARGE = 202, //采购负责人
    HEAD_OF_OPERATIONS = 203, //运营负责人
    OPERATIONS_ADMINISTRATOR = 204, //运营管理员
    BUYER = 205, //采购员
    RECEIVING_CLERK = 206, //收货员
    STORE_MANAGER = 208, //店长
    MAINTENANCE_STAFF = 209, //养护员
    PHARMACIST = 207, //药师
}

export class Roles {
    id?: RolesType;
    name?: string;
    moduleIds?: number[];
}

class Clinic {
    static ROLEID_MANAGER = 1;
    static ROLEID_EMPLOYEE = 2;

    //诊所类型
    ////诊所类型：0,独立门店；1,连锁总店；2,连锁子店
    static CLINIC_TYPE_NORMAL = 0; //普通独立门店
    static CLINIC_TYPE_CHAIN_ADMIN = 1; //连锁总店
    static CLINIC_TYPE_CHAIN_CHILD = 2; //连锁子店

    static CLINIC_VIEW_MODE_NORMAL = 0;
    static CLINIC_VIEW_MODE_CHAIN_CLINIC = 1;

    //当前应用支持的模块
    static supportModules = [
        ModuleIds.MODULE_ID_REGISTER,
        ModuleIds.MODULE_ID_OUTPATIENT,
        ModuleIds.MODULE_ID_CHARGE,
        ModuleIds.MODULE_ID_PHARMACY,
        ModuleIds.MODULE_ID_PATIENT,
        ModuleIds.MODULE_ID_STATISTICS,
        ModuleIds.MODULE_ID_MANAGE,
        ModuleIds.MODULE_ID_EXAMINATION,
        ModuleIds.MODULE_ID_NURSE_STATION,
        ModuleIds.MODULE_ID_PATIENT_MEMBER,
        ModuleIds.MODULE_ID_OUTPATIENT_RECORD,
        ModuleIds.MODULE_ID_STATISTICS_BUSINESS,
        ModuleIds.MODULE_ID_STATISTICS_BUSINESS_OVERVIEW,
        ModuleIds.MODULE_ID_STATISTICS_CLINIC_REVENUE,
    ];

    clinicType = null; //int
    nodeType?: number; //int
    chainId!: string; //String
    chainName!: string; //String
    clinicId!: string; //String
    name!: string; //String
    namePy?: string;
    namePyFirst?: string;
    shortName?: string; //String
    contactPhone?: string; //String
    roleId?: number; //int
    moduleIds?: string; //String
    viewMode?: number; //number

    hisType?: HisType;

    @JsonProperty({ type: Array, clazz: Roles })
    roles?: Roles[];

    //客户端使用，非服务端协议数据
    _modulesIdList = new Array<number>(); //array<int>

    //诊所所在地区
    addressCityId?: string;
    addressCityName?: string;
    addressDetail?: string;
    addressDistrictId?: string;
    addressDistrictName?: string;
    addressGeo?: string;
    addressProvinceId?: string;
    addressProvinceName?: string;

    /**
     * 是否牙科诊所
     */
    get isDentistryClinic(): boolean {
        return this.hisType == HisType.dentistry;
    }

    /**
     * 是否是眼科诊所
     */
    get isOphthalmologyClinic(): boolean {
        return this.hisType == HisType.ophthalmology;
    }

    /**
     * 是否是医院管家
     */
    get isNormalHospital(): boolean {
        return this.hisType == HisType.normalHospital;
    }

    /**
     * 是否是药店管家
     */
    get isDrugstoreButler(): boolean {
        return this.hisType == HisType.drugstoreButler;
    }

    /**
     * 是否是诊所管家
     */
    get isClinicHousekeeper(): boolean {
        return !this.isDentistryClinic && !this.isOphthalmologyClinic && !this.isNormalHospital && !this.isDrugstoreButler;
    }

    /**
     * 某种类型诊所----门诊病历主诉、诊断不必验证
     */
    get notNeedVerifyMedicalClinic(): boolean {
        return this.isDentistryClinic || this.isOphthalmologyClinic;
    }
    /**
     *
     * @returns {Array<int>}
     */
    public modulesIdList(): Array<number> {
        if (this._modulesIdList != null) return this._modulesIdList;

        if (this.roleId == Clinic.ROLEID_MANAGER) {
            //管理员包含所有模块
            return range(ModuleIds.MODULE_ID_REGISTER, ModuleIds.MODULE_ID_OUTPATIENT_RECORD);
        }

        if (this.moduleIds == null) {
            return [];
        }

        return this.moduleIds!.split(",").map((item) => parseInt(item));
    }

    /**
     *
     * @param moduleId //see MODULE_ID_XXX
     */
    public can(moduleId: number | number[]): boolean {
        if (_.isArray(moduleId)) {
            for (const item of moduleId) {
                if (this.modulesIdList().includes(item)) return true;
            }

            return false;
        }
        return this.modulesIdList().includes(moduleId);
    }

    /**
     *
     * @param roleId
     */
    public canOfRole(roleId: number | number[]): boolean {
        if (_.isArray(roleId)) {
            return !!this.roles?.some((item) => roleId.includes(item.id!));
        }
        return !!this.roles?.find((item) => item.id == roleId);
    }

    /**
     * 是否是管理员
     */
    public get isManager(): boolean {
        return this.roleId == Clinic.ROLEID_MANAGER;
    }

    public canDeliverMedicine(): boolean {
        return this.can(ModuleIds.MODULE_ID_PHARMACY);
    }
    get isChainAdminClinic(): boolean {
        return (this.clinicType == Clinic.CLINIC_TYPE_CHAIN_ADMIN || this.nodeType == Clinic.CLINIC_TYPE_CHAIN_ADMIN) && !this.viewMode;
    }

    get isNormalClinic(): boolean {
        return (
            this.clinicType == Clinic.CLINIC_TYPE_NORMAL ||
            this.nodeType == Clinic.CLINIC_TYPE_NORMAL ||
            (this.viewMode == Clinic.CLINIC_VIEW_MODE_CHAIN_CLINIC &&
                (this.clinicType == Clinic.CLINIC_TYPE_CHAIN_CHILD || this.nodeType == Clinic.CLINIC_TYPE_CHAIN_CHILD))
        );
    }

    get displayName(): string {
        if (this.isChainAdminClinic) return "总部";
        if (!!this.shortName) return this.shortName!;

        return this.name!;
    }

    get isChainSubClinic(): boolean {
        return (this.clinicType == Clinic.CLINIC_TYPE_CHAIN_CHILD || this.nodeType == Clinic.CLINIC_TYPE_CHAIN_CHILD) && !this.viewMode;
    }

    //医生
    get isDoctor(): boolean {
        return !!this.roles?.find((item) => item.id === RolesType.ROLE_DOCTOR_ID);
    }

    //护士
    get isNurse(): boolean {
        return !!this.roles?.find((item) => item.id === RolesType.ROLE_NURSE_ID);
    }

    //检验师
    get isSurveyor(): boolean {
        return !!this.roles?.find((item) => item.id === RolesType.ROLE_SURVEYOR_ID);
    }

    //理疗师
    get isTherapist(): boolean {
        return !!this.roles?.find((item) => item.id === RolesType.ROLE_PHYSIOTHERAPIST_ID);
    }

    //医助
    get isDoctorAssist(): boolean {
        return !!this.roles?.find((item) => item.id === RolesType.ROLE_DOCTOR_ASSIST_ID);
    }

    //其他
    get isClerk(): boolean {
        return !!this.roles?.find((item) => item.id === RolesType.ROLE_CLERK_ID);
    }

    //管理员
    get isAdministrator(): boolean {
        return !!this.roles?.find((item) => item.id === RolesType.ADMINISTRATOR_ID);
    }

    //收费员
    get isChargeEmployee(): boolean {
        const ids = this.moduleIds?.split(",").map((item) => parseInt(item));
        return !!ids?.find((item) => item == ModuleIds.MODULE_ID_CHARGE);
    }

    //发药员
    get isDispensingEmployee(): boolean {
        const ids = this.moduleIds?.split(",").map((item) => parseInt(item));
        return !!ids?.find((item) => item == ModuleIds.MODULE_ID_PHARMACY);
    }

    // 管理成员-拥有有管理权限
    get isManagerModule(): boolean {
        const ids = this.moduleIds?.split(",").map((item) => parseInt(item));
        if (ids?.includes(ModuleIds.MODULE_ID_NONE)) return true;
        return !!ids?.find((item) => item == ModuleIds.MODULE_ID_MANAGE);
    }
    //    所在区域为南京
    get isNanjingClinic(): boolean {
        return this.addressCityId == "320100";
    }
    //  采购权限
    get isPurchasing(): boolean {
        const ids = this.moduleIds?.split(",").map((item) => parseInt(item));
        if (ids?.includes(ModuleIds.MODULE_ID_NONE)) return true;
        return !!ids?.find((item) => item == ModuleIds.MODULE_ID_CLINIC_PURCHASE);
    }
    // 患者权限
    get isPatientRights(): boolean {
        const ids = this.moduleIds?.split(",").map((item) => parseInt(item));
        if (ids?.includes(ModuleIds.MODULE_ID_NONE)) return true;
        return !!ids?.find((item) => item == ModuleIds.MODULE_ID_PATIENT);
    }
    //   盘点权限
    get hasStockCheckPermission(): boolean {
        const ids = this.moduleIds?.split(",").map((item) => parseInt(item));
        if (ids?.includes(ModuleIds.MODULE_ID_NONE)) return true;
        return !!ids?.find((item) => item == ModuleIds.pharmacyInventory || item == ModuleIds.pharmacyStocktaking);
    }
    // 有零售权限（包括零售开单+零售单）
    get isRetail(): boolean {
        const ids = this.moduleIds?.split(",").map((item) => parseInt(item));
        if (ids?.includes(ModuleIds.MODULE_ID_NONE)) return true;
        return !!ids?.find((item) => item == ModuleIds.MODULE_ID_CHARGE);
    }
    //  是否只有零售单权限，没有零售开单权限
    get isOnlyRetail(): boolean {
        const ids = this.moduleIds?.split(",").map((item) => parseInt(item));
        if (ids?.includes(ModuleIds.MODULE_ID_NONE)) return false;
        if (!!ids?.find((item) => item == ModuleIds.MODULE_ID_CHARGE)) return false;
        return !!ids?.find((item) => item == ModuleIds.pharmacyRetailSalesOrder);
    }
}

class ClinicEmployee {
    id?: string;
    name?: string;
    mobile?: string;
    employeeId?: string;
    qrCodeJoin?: string;
    status?: number;
    headImgUrl?: string;
    createdBy?: string;
    created?: string;
    lastModifiedBy?: string;
    lastModified?: string;
}

class ClinicConfig {
    bargain?: boolean; //bool

    doctorBargain?: boolean; //bool

    singleBargain?: boolean; //bool

    doctorSingleBargain?: boolean; //bool

    outpatientHiddenPrice?: boolean; //bool 门诊开单处，是否隐藏价格，true:隐藏，false不隐藏

    shebaoEnable?: number; //int

    manageTaxRat?: boolean; //bool

    defaultRegUnitPrice?: number; // 诊所默认挂号费

    materialInTaxRat?: number; //int 物资进项税率
    materialOutTaxRat?: number; //int 物资销项税率

    maxPricePercent?: number; //
    minPricePercent?: number; //

    medicineInTaxRat?: number; //int 药品进项税率
    medicineOutTaxRat?: number; //int 药品销项税率

    disableNoStockGoods?: number; // 能否开出库存为0的药品

    regsRequiredSource?: boolean; //挂号时跟者来源必填项
    regsRequiredMobile?: boolean; //

    subSetPrice?: number; //自主定价相关
    subSetPriceAllClinics?: number; //自主定价相关
    subSetPriceClinics?: Clinic[]; //自主定价相关

    stockTransChainReview?: boolean; //调拨-总部是否审核
    stockTransDiffPrice?: boolean; //调拨-异价/同价调拨
    stockInChainReview?: boolean; //入库-总部是否审核
    stockOutChainReview?: boolean; //出库-总部是否审核
    stockCheckChainReview?: boolean; //盘点-总部是否审核
}

enum LoginStatus {
    failed,
    success,
    no_registered,
}

class UpdateUserInfo {
    @JsonProperty({ type: ClinicEmployee })
    clinicEmployee?: ClinicEmployee;

    @JsonProperty({ type: Clinic })
    clinic?: Clinic;
}

enum LoginType {
    wechat = 0, //微信登录
    verifyCode = 1, //证码登录
    password = 2, //密码登录
}

class SwitchClinicData {
    token?: string;

    @JsonProperty({ type: ClinicEmployee })
    employee?: ClinicEmployee;

    @JsonProperty({ type: Clinic })
    clinic?: Clinic;
}

class CustomUsage {
    after?: string;
    latin?: string;
    name?: string;
    type?: number;
}

class ClinicCustomUsagesConfig {
    @JsonProperty({ type: Array, clazz: CustomUsage })
    customUsages?: CustomUsage[];
    scope?: string;
    scopeId?: string;
}

const PREF_CURRENT_CLINIC_EMPLOYEE = "current_clinic_employee";
const PREF_X_APP_TOKEN = "x_app_token";
const PREF_X_APP_TOKEN_TYPE = "x_app_token_type";
const PREF_CURRENT_CLINIC = "current_clinic";
const kPreferencesGetTokenDate = "api_token_get_date"; //token刷新日期
const TEST_LOGIN_FLAG = "test_login_flag"; //test_login标志

export class LoginEvent {
    static loginIn = 0; //登入
    static loginOut = 1; //登出

    type: number;
    appToken?: string;

    constructor(type: number, appToken?: string) {
        this.type = type;
        this.appToken = appToken;
    }
}

interface UserCenterListener {
    onWillLogout(forInvalidateToken: boolean): Promise<void>;
}

class UserCenter {
    //后台服务端Api版本号
    serverApiVersion?: number;
    @JsonProperty({ type: ClinicEmployee })
    employee?: ClinicEmployee;
    @JsonProperty({ type: ClinicConfig })
    clinicConfig?: ClinicConfig; //ClinicConfig

    inventoryClinicConfig?: InventoryClinicConfig; // 库存模块诊所配置

    @JsonProperty({ type: ClinicEdition })
    clinicEdition?: ClinicEdition;

    @JsonProperty({ type: DentistryConfig })
    dentistryConfig?: DentistryConfig;

    clinicChainConfig?: ChainBasePropertyConfig;
    // 全局字典信息
    @JsonProperty({ type: ClinicDictionaryInfo })
    clinicDictionaryInfo?: ClinicDictionaryInfo;

    // 是否显示订单云模块
    @JsonProperty({ type: EcAuthBindListRsp })
    ecAuthBindList?: EcAuthBindListRsp;
    // 商城订单详情
    @JsonProperty({ type: MallOrderDetailRsp })
    mallOrderDetailRsp?: MallOrderDetailRsp;
    // 审批设置列表
    @JsonProperty({ type: Array, clazz: ApprovalSettingItem })
    approvalSettingList?: ApprovalSettingItem[];

    // 自定义用法配置
    @JsonProperty({ type: Array, clazz: CustomUsage })
    customUsages?: CustomUsage[];

    // 自定义用法配置
    @JsonProperty({ type: ClinicCustomUsagesConfig })
    customUsagesConfig?: ClinicCustomUsagesConfig;

    // 获取某个连锁下的利润
    @JsonProperty({ type: Array, clazz: ProfitCategoryTypesItem })
    profitClassificationList?: ProfitCategoryTypesItem[];

    @JsonProperty({ type: Array, clazz: ClinicAllEmployeesItem })
    clinicAllEmployees?: ClinicAllEmployeesItem[];

    coClinicCountInfo?: {
        bindTotal?: number;
        hasBindRecord?: number;
        total?: number;
    };

    /**
     * 预约升级的版本控制
     */
    get isAllowedRegUpgrade(): boolean {
        return !!this.clinic?.isDentistryClinic || !!this.clinicChainConfig?.chainBasic?.isEnableRegUpgrade;
    }

    /**
     * 颗粒饮片当量转换是否开启
     */
    get enableEqCoefficient(): boolean {
        return !!this.clinicChainConfig?.chainBasic?.enableEqCoefficient;
    }

    /**
     * 支持AI拍照入库
     */

    get enablePictureStockIn(): boolean {
        return !!this.clinicChainConfig?.chainBasic?.deepseek?.pictureStockIn || environment.serverEnvType === ServerEnvType.dev;
    }

    // 医保配置
    shebaoConfig?: ClinicShebaoConfig;
    // 医保合规配置
    shebaoRestrictConfig?: SheBaoRestrictConfig;
    // 追溯码采集场景配置
    clinicTraceCodeConfig?: ClinicTraceCodeConfig;

    aiPictureTaskData?: AiPictureTaskData;

    public isLogouting = false;

    public sClinicChangeObserver = new Subject<SwitchClinicData>();

    public sUpdatedUserCenterObserver = new Subject<number>();

    public loginObserver = new Subject<LoginEvent>();

    public aiPictureTaskDataObserver = new Subject<AiPictureTaskData>();

    clinicEditionObserver = new Subject<number>();

    private _appToken?: string;
    private _isGlobalToken?: boolean;
    private _clinic?: Clinic;
    private _testLogin?: boolean;

    private _listeners: UserCenterListener[] = [];

    /**
     * 当前是否处于登录状态
     * @returns boolean true已登录, false未登录
     */
    isLogin() {
        return !_.isEmpty(this._appToken);
    }

    get clinic(): Clinic | undefined {
        return this._clinic;
    }

    addListener(listener: UserCenterListener) {
        this._listeners.push(listener);
    }

    get isTestLogin(): boolean {
        return !!this._testLogin;
    }

    //初始化当前诊所部分模块全局配置
    async _initModuleConfig(): Promise<void> {
        await this.getClinicConfig().catchIgnore().then();
        await this.getClinicEdition().catchIgnore().then();
        this.getInventoryChainConfig().catchIgnore().then();
        this.getDentistryConfig(false).catchIgnore().then();
        this.getClinicChainBasicConfig(false).catchIgnore().then();
        this.getClinicDictionaryInfo(false).catchIgnore().then();
        this.getEcAuthBindList(false).catchIgnore().then();
        this.getLastOrderDetail(false).catchIgnore().then();
        if (this.clinic?.isDrugstoreButler) {
            this.getApprovalSettingList(false).catchIgnore().then();
            this.getProfitClassificationList(false).catchIgnore().then();
            this.getClinicAllEmployees(false).catchIgnore().then();
            this.getCoClinicCountInfo(false).catchIgnore().then();
            this.getSheBaoRestrictConfig(false).catchIgnore().then();
        }
        this.getSheBaoClinicConfig(false).catchIgnore().then();
        this.getClinicCustomUsagesConfig().catchIgnore().then();
        this.getClinicTraceCodeConfig(false).catchIgnore().then();
        this.getAiPictureTaskData(false).catchIgnore().then();

        const { NotificationListenView } = require("../views/global-notification-pages/notification-listen-view");
        new NotificationListenView();
    }
    async getClinicConfig(cache = true): Promise<ClinicConfig | undefined> {
        if (environment.isGlobalEnv) return undefined;
        if (this.clinicConfig && cache) return this.clinicConfig;

        this.clinicConfig = await ClinicAgent.getClinicConfig().catch(() => undefined);

        return this.clinicConfig;
    }

    //库存模块诊所配置
    async getInventoryChainConfig(cache = true): Promise<InventoryClinicConfig | undefined> {
        if (this.inventoryClinicConfig && cache) return this.inventoryClinicConfig;

        this.inventoryClinicConfig = await ClinicAgent.getInventoryChainConfig().catch(() => undefined);

        return this.inventoryClinicConfig;
    }
    // 诊所全局字典信息
    async getClinicDictionaryInfo(cache = true): Promise<ClinicDictionaryInfo | undefined> {
        if (this.clinicDictionaryInfo && cache) return this.clinicDictionaryInfo;
        this.clinicDictionaryInfo = await ClinicAgent.queryClinicDictionaryInfo().catch(() => undefined);
        return this.clinicDictionaryInfo;
    }

    async getClinicEdition(): Promise<any> {
        this.clinicEdition = await ClinicAgent.getClinicsEditionCurrent();
    }

    //口腔预约配置
    async getDentistryConfig(cache = true): Promise<DentistryConfig | undefined> {
        if (this.dentistryConfig && cache) return this.dentistryConfig;

        this.dentistryConfig = await DentistryAgent.queryDentistryRegistrationConfig(RegistrationType.outpatientRegistration).catch(
            () => undefined
        );

        return this.dentistryConfig;
    }
    // 授权绑定的电商平台列表
    async getEcAuthBindList(cache = true): Promise<EcAuthBindListRsp | undefined> {
        if (this.ecAuthBindList && cache) return this.ecAuthBindList;

        this.ecAuthBindList = await OrderCloudAgent.getEcAuthBindList(1).catchIgnore();

        return this.ecAuthBindList;
    }

    // 获取11月1日-11月30日期间，门店是否在直采商城下单，已下单则不弹双十一活动弹窗
    async getLastOrderDetail(cache = true): Promise<MallOrderDetailRsp | undefined> {
        if (this.mallOrderDetailRsp && cache) return this.mallOrderDetailRsp;
        this.mallOrderDetailRsp = await ABCApiNetwork.get(`https://${environment.mallHostname}/api/mall/orders/getLastOrderDetail`, {
            clazz: MallOrderDetailRsp,
        });
        return this.mallOrderDetailRsp;
    }

    async getClinicChainBasicConfig(cache = true): Promise<ChainBasePropertyConfig | undefined> {
        if (!!this.clinicChainConfig && cache) return this.clinicChainConfig;
        const { OnlinePropertyConfigProvider } = require("../data/online-property-config-provder");
        this.clinicChainConfig = await OnlinePropertyConfigProvider.instance.getChainBasicPropertyConfig().then().catchIgnore();
        // 根据连锁所在地区切换语言
        const currencyType = this.clinicChainConfig?.chainBasic?.currencyType ?? 0;
        abcI18Next.changeLangType(LangTypeEnum[currencyType]);
        return this.clinicChainConfig;
    }
    // 药房审批设置列表
    async getApprovalSettingList(cache = true): Promise<ApprovalSettingItem[] | undefined> {
        if (this.approvalSettingList && cache) return this.approvalSettingList;
        this.approvalSettingList = await InventoryPharmacyAgent.getApprovalSettingList().catchIgnore();
        return this.approvalSettingList;
    }

    async getSheBaoClinicConfig(cache = true): Promise<ClinicShebaoConfig | undefined> {
        if (!!this.shebaoConfig && cache) return this.shebaoConfig;
        const { OnlinePropertyConfigProvider } = require("../data/online-property-config-provder");
        const { ShebaoAgent } = require("../base-business/mix-agent/shebao-agent");
        this.shebaoConfig = await ShebaoAgent.getClinicShebaoConfig(false).catchIgnore();
        if (this.shebaoConfig?.isChengdu) {
            const rsp = await OnlinePropertyConfigProvider.instance.getSheBaoSettleConfig().catchIgnore();
            this.shebaoConfig._isEnableShebaoSettle = rsp;
        }
        return this.shebaoConfig;
    }

    /**
     * 获取诊所自定义用法配置
     */
    async getClinicCustomUsagesConfig(cache = true): Promise<ClinicCustomUsagesConfig | undefined> {
        if (this.customUsagesConfig?.customUsages && cache) return this.customUsagesConfig;
        const { OnlinePropertyConfigProvider } = require("../data/online-property-config-provder");
        this.customUsagesConfig = await OnlinePropertyConfigProvider.instance.getClinicCustomUsages().catchIgnore();
        return this.customUsagesConfig;
    }

    async getClinicTraceCodeConfig(cache = true): Promise<ClinicTraceCodeConfig | undefined> {
        if (this.clinicTraceCodeConfig && cache) return this.clinicTraceCodeConfig;
        const { OnlinePropertyConfigProvider } = require("../data/online-property-config-provder");
        this.clinicTraceCodeConfig = await OnlinePropertyConfigProvider.instance.getClinicTraceCodeConfig().catchIgnore();
        return this.clinicTraceCodeConfig;
    }

    /**
     * 医保合规相关配置
     * @param cache
     */
    async getSheBaoRestrictConfig(cache = true): Promise<SheBaoRestrictConfig | undefined> {
        if (!!this.shebaoRestrictConfig && cache) return this.shebaoRestrictConfig;
        const { ShebaoAgent } = require("../base-business/mix-agent/shebao-agent");
        this.shebaoRestrictConfig = await ShebaoAgent.getSheBaoRestrictConfig().catchIgnore();
        return this.shebaoRestrictConfig;
    }

    async getProfitClassificationList(cache = true): Promise<ProfitCategoryTypesItem[] | undefined> {
        if (this.profitClassificationList && cache) return this.profitClassificationList;
        this.profitClassificationList = await ClinicAgent.getGoodsProfitCategoryTypes().catchIgnore();
        return this.profitClassificationList;
    }

    // 获取诊所所有员工数据
    async getClinicAllEmployees(cache = true): Promise<ClinicAllEmployeesItem[] | undefined> {
        if (this.clinicAllEmployees && cache) return this.clinicAllEmployees;
        this.clinicAllEmployees = await ClinicAgent.getClinicAllEmployees({ limit: 9999, showDisable: 1 }).catchIgnore();
        return this.clinicAllEmployees;
    }

    async getCoClinicCountInfo(cache = true): Promise<{ bindTotal?: number; hasBindRecord?: number; total?: number } | undefined> {
        if (this.coClinicCountInfo && cache) return this.coClinicCountInfo;
        this.coClinicCountInfo = await ClinicAgent.getCoClinicCountInfo().catchIgnore();
        return this.coClinicCountInfo;
    }

    async getAiPictureTaskData(cache = true): Promise<AiPictureTaskData | undefined> {
        if (this.aiPictureTaskData && cache) return this.aiPictureTaskData;
        const { OnlinePropertyConfigProvider } = require("../data/online-property-config-provder");
        this.aiPictureTaskData = await OnlinePropertyConfigProvider.instance.getAiPictureEmployeeTask().catchIgnore();
        this.aiPictureTaskDataObserver.next(this.aiPictureTaskData);
        return this.aiPictureTaskData;
    }

    /**
     * 微信登录
     */
    async loginWithWeChat(jumpUrl?: string) {
        const installed = await WxApi.isWeChatInstalled();
        if (!installed) {
            Toast.show("未安装微信", { warning: true }).then();
            return;
        }

        const wxapiState = `abcyun-${Date.now()}`;

        let code = "";
        try {
            const rsp = await WxApi.sendAuth("snsapi_userinfo", wxapiState);
            code = rsp!.code ?? "";
        } catch (error) {
            Toast.show(`登录失败${errorToStr(error)}`, { warning: true }).then();
            return LoginStatus.failed;
        }

        const loginStatus = await this.doLogin({
            loginType: LoginType.wechat,
            wechatCode: code,
            jumpUrl: jumpUrl,
        });

        if (loginStatus == LoginStatus.no_registered) {
            Toast.show(
                `账号不存在, 请先登录电脑端创建账号及诊所\n网址为：${environment.serverHostScheme}://${environment.serverHostName}`
            ).then();
        }
    }

    loginWithPassword(mobile: string, password: string, jumpUrl?: string): Promise<LoginStatus> {
        return this.doLogin({
            loginType: LoginType.password,
            mobile: mobile,
            password: password,
            jumpUrl: jumpUrl,
        });
    }

    loginWithVerifyCode(mobile: string, verifyCode: string, jumpUrl?: string): Promise<LoginStatus> {
        return this.doLogin({
            loginType: LoginType.verifyCode,
            mobile: mobile,
            verifyCode: verifyCode,
            jumpUrl: jumpUrl,
        });
    }

    async init() {
        this._appToken = sharedPreferences.getString(PREF_X_APP_TOKEN);
        this._isGlobalToken = sharedPreferences.getBool(PREF_X_APP_TOKEN_TYPE);
        this._testLogin = sharedPreferences.getBool(TEST_LOGIN_FLAG);
        //读取员工信息
        const employeeJs = sharedPreferences.getString(PREF_CURRENT_CLINIC_EMPLOYEE);

        if (employeeJs) {
            try {
                this.employee = JsonMapper.deserialize(ClinicEmployee, JSON.parse(employeeJs));
            } catch (e) {}
        }

        const clinic = sharedPreferences.getString(PREF_CURRENT_CLINIC);
        if (clinic) {
            try {
                this._clinic = JsonMapper.deserialize(Clinic, JSON.parse(clinic));
            } catch (e) {}
        }
        if (this.isLogin() && !this._isGlobalToken) {
            await this._initModuleConfig();
        }

        this._initDistributeConfig(this._clinic?.hisType as HisType);
    }

    async postClinicSwitch(clinicId: string): Promise<SwitchClinicData> {
        const { abcNetDelegate } = require("../net/abc-net-delegate");
        let switchClinicData: SwitchClinicData | undefined = undefined;
        ///注册当前分区信息

        const _reginApi = environment.serverHostName;
        const partitionDetail = await ABCApiNetwork.post<{ redirectUrl: string; hisType: HisType }>(
            environment.isGlobalEnv ? "global-auth/switch-clinic/app" : "region-auth/switch-clinic",
            {
                body: { clinicId: clinicId, token: this.appToken(), rememberLogin: true, scene: 1 },
            }
        ).catchIgnore();

        if (!!partitionDetail) {
            const url = partitionDetail.redirectUrl;
            const _urlParseData = UrlUtils.parseUrl(url);
            if (!!_urlParseData) {
                await environment.setReginApiObj(_urlParseData.host as string);
                abcNetDelegate.init();
                switchClinicData = await ABCApiNetwork.post("region-auth/login-by-code/his/app", {
                    body: { clinicId: clinicId, code: _urlParseData.query.authCode, rememberLogin: true, scene: 1 },
                    clazz: SwitchClinicData,
                }).catch(async (e) => {
                    await environment.setReginApiObj(_reginApi);
                    throw e;
                });
            }
        } else {
            throw "分区信息为空";
            // await environment.clearReginApiObj();
            // abcNetDelegate.init();
        }

        this._initDistributeConfig(partitionDetail.hisType);

        employeeSharedPreferences.setInt("expire_payment_warning", 0);
        // 切换诊所或者从工作日报跳转，不需要弹ABC支付弹窗
        employeeSharedPreferences.setInt("k_abc_pay_ad", 1);
        !switchClinicData &&
            (switchClinicData = await ABCApiNetwork.post("usercenter/switch-clinic", {
                body: { clinicId: clinicId, token: this.appToken() },
                clazz: SwitchClinicData,
            }));

        this._sveAppToken(switchClinicData.token);
        this._saveClinic(switchClinicData.clinic);
        this._saveEmployee(switchClinicData.employee);

        await sharedPreferences.commit().catchIgnore();
        clinicScopeMemoryCache.clear();

        await clinicSharedPreferences.init().catchIgnore();
        await chainSharedPreferences.init().catchIgnore();
        abcNetDelegate.clearDeviceLoginStatusForClinicSwitch();

        this.clinicConfig = undefined;
        this.inventoryClinicConfig = undefined;
        this.shebaoConfig = undefined;
        await this._initModuleConfig();
        this.sClinicChangeObserver.next(switchClinicData);
        // await this.updateUserInfo().catchIgnore().then();

        return switchClinicData;
    }

    async updateUserInfo(): Promise<UpdateUserInfo> {
        const employeeId = userCenter.employee?.id;

        const updateUserInfo = await ABCApiNetwork.get(`usercenter/getuserinfo/${employeeId}`, {
            clazz: UpdateUserInfo,
        });
        this._saveClinic(updateUserInfo.clinic);
        this._saveEmployee(updateUserInfo.clinicEmployee);

        // 根据当前产品类型生产相应的静态字段
        if (updateUserInfo.clinic?.isNormalHospital) {
            RuntimeConstantsManager.setConstants(RuntimeConstantsType.hospital);
        } else {
            RuntimeConstantsManager.setConstants(RuntimeConstantsType.clinic);
        }

        await sharedPreferences.commit().catchIgnore();

        this.sUpdatedUserCenterObserver.next();
        return updateUserInfo;
    }

    //根据短信验证码，重置密码
    resetPassword(mobile: string, password: string, verifyCode: string): Promise<boolean> {
        return ABCApiNetwork.put<Response>("global-auth/password/reset", {
            body: {
                mobile: mobile,
                password: EncryptUtils.aesEncrypt(password ?? "", EncryptUtils.passwordKey),
                verify: verifyCode,
            },
            useRsp: true,
        }).then((rsp) => rsp.status == 200);
    }

    _saveEmployee(employee?: ClinicEmployee) {
        this.employee = employee;
        sharedPreferences.setString(PREF_CURRENT_CLINIC_EMPLOYEE, employee ? JSON.stringify(employee) : "");
    }

    _initDistributeConfig(hisType: HisType) {
        initDistributeViewConfig(hisType);
        if (getDistributeConfig().viewFeature.homeV2) {
            ThemeManager.setTheme(ThemeType.v2, true, false);
        } else {
            ThemeManager.rollbackTheme();
        }
    }

    async loginWithToken(token: string, regionHost?: string) {
        //保存分区信息-避免分区不匹配导致无法进入
        if (!!regionHost) {
            await environment.setReginApiObj(regionHost);
        }
        this._sveAppToken(token);
        sharedPreferences.setBool(TEST_LOGIN_FLAG, true);
        await ClinicAgent.getRecentClinics().catchIgnore();
        await sharedPreferences.commit().catchIgnore();

        this.loginObserver.next(new LoginEvent(LoginEvent.loginIn, this.appToken()));
        const { AbcPlatformMix } = require("../base-business/native-modules/abc-platform-mix");
        const { showConfirmDialog } = require("../base-ui/dialog/dialog-builder");
        showConfirmDialog("提示", "app会重新启动").then(() => {
            AbcPlatformMix.exitApp();
        });
    }

    appToken(): string | undefined {
        return this._appToken;
    }

    async refreshToken() {
        if (!this.appToken()) return;
        if (environment.isNormalEnv) {
            return ABCApiNetwork.post("usercenter/employees/refresh-token", {
                body: { token: this.appToken() },
            });
        } else {
            return ABCApiNetwork.post("region-auth/refresh-token/app", {
                body: { token: this.appToken() },
            });
        }
    }

    private _saveClinic(clinic?: Clinic) {
        this._clinic = clinic;
        sharedPreferences.setString(PREF_CURRENT_CLINIC, clinic ? JSON.stringify(clinic) : "");
    }

    async logout(forInvalidateToken = false) {
        this.isLogouting = true;
        employeeSharedPreferences.setInt("k_abc_pay_ad", 0);
        employeeSharedPreferences.setInt("double_eleven_ad", 0);
        employeeSharedPreferences.setInt("double_twelfth_ad", 0);
        employeeSharedPreferences.setInt("expire_payment_warning", 0);
        sharedPreferences.setBool(TEST_LOGIN_FLAG, false);
        const loadingDialog = new LoadingDialog("正在退出");
        loadingDialog.show(1000);

        for (const listener of this._listeners) {
            await listener.onWillLogout(forInvalidateToken).catchIgnore();
        }
        await loadingDialog.hide();

        //调用后台接口
        if (!forInvalidateToken) {
            await ABCApiNetwork.post(environment.isGlobalEnv ? "global-auth/logout" : "region-auth/logout", {
                body: { logoutWay: "logout", scene: 1 },
            }).catchIgnore();
        }

        this._saveClinic(undefined);
        this._saveEmployee(undefined);
        this._sveAppToken("", true);

        //清空分区相关数据
        const { abcNetDelegate } = require("../net/abc-net-delegate");
        await environment.clearReginApiObj();
        abcNetDelegate.init();

        this.isLogouting = false;
        this.loginObserver.next(new LoginEvent(LoginEvent.loginOut));
        ABCNavigator.navigateToPage(<LoginPage />, {
            clearStack: true,
        }).then();

        ThemeManager.rollbackTheme();
    }

    private async doLogin(options: {
        loginType: LoginType;
        wechatCode?: string;
        mobile?: string;
        verifyCode?: string;
        password?: string;
        jumpUrl?: string;
    }): Promise<LoginStatus> {
        const { abcNetDelegate } = require("../net/abc-net-delegate");
        await environment.clearReginApiObj();
        abcNetDelegate.init();

        const { loginType, wechatCode, mobile, verifyCode, password, jumpUrl } = options;
        const loadingDialog = new LoadingDialog();

        let loginStatus: LoginStatus = LoginStatus.failed;
        loadingDialog.show();
        const rsp: {
            code?: number;
            token?: string;
            employee?: ClinicEmployee;
            error: any;
            // } = await ABCApiNetwork.post<any>("usercenter/login", {
        } = await ABCApiNetwork.post<any>("global-auth/login/app", {
            body: {
                loginType: loginType,
                wechatCode: wechatCode ?? "",
                mobilePhoneNumber: mobile ?? "",
                mobileVerifyCode: verifyCode ?? "",
                mobilePassword: EncryptUtils.aesEncrypt(password ?? "", EncryptUtils.passwordKey),
                scene: 1,
            },
        }).catch((error) => {
            if (error instanceof ABCApiError) {
                return {
                    code: error.code,
                    error: error,
                };
            }
            return { error: error };
        });

        if (rsp.code == 450) {
            await loadingDialog.hide();
            await Toast.show(
                `账号不存在, 请先登录电脑端创建账号及诊所\n网址为：${environment.serverHostScheme}://${environment.serverHostName}`,
                { warning: true }
            );
            return LoginStatus.no_registered;
        }
        if (rsp.error) {
            await loadingDialog.hide();
            await Toast.show(`登录失败：${errorSummary(rsp.error)}`, {
                warning: true,
            });
            return LoginStatus.failed;
        }

        const { token, employee } = rsp;
        if (_.isEmpty(token) || !employee) {
            await loadingDialog.hide();
            return loginStatus;
        }
        const clinicEmployee = JsonMapper.deserialize(ClinicEmployee, rsp.employee);
        loginStatus = LoginStatus.success;

        this._saveEmployee(clinicEmployee!);
        this._sveAppToken(token, true);
        await sharedPreferences.commit().catchIgnore();
        const clinic = await ClinicAgent.getRecentClinics();
        if (!clinic) {
            await loadingDialog.hide();
            const { ClinicChangePage } = require("../clinics/clinic-change-page");
            await ABCNavigator.navigateToPage(<ClinicChangePage jumpUrl={jumpUrl} />, { clearStack: true });
            return loginStatus;
        }

        const result = await this.postClinicSwitch(clinic.clinicId!);
        if (result) {
            await loadingDialog.hide();
            await ABCNavigator.navigateToPage(jumpUrl ?? URLProtocols.ABCURL_HOME, {
                clearStack: true,
            });
            return loginStatus;
        }

        if (loginStatus === LoginStatus.success) {
            this.loginObserver.next(new LoginEvent(LoginEvent.loginIn, this.appToken()));
        }
        await loadingDialog.hide();

        return loginStatus;
    }

    private _sveAppToken(token?: string, isGlobal?: boolean) {
        this._appToken = token;
        this._isGlobalToken = !!isGlobal;
        sharedPreferences.setString(PREF_X_APP_TOKEN, this._appToken ?? "");
        sharedPreferences.setBool(PREF_X_APP_TOKEN_TYPE, this._isGlobalToken ?? false);
        sharedPreferences.setString(kPreferencesGetTokenDate, new Date().toString());
    }
}

const userCenter = new UserCenter();
export { userCenter };

export { Clinic, ClinicConfig, ClinicEmployee };
