import { BaseBlocPage } from "../../base-ui/base-page";
import { ProductAddPageBloc } from "./product-add-page-bloc";
import { _GoodsSearchDisplayView, _NewCategoryPicker, MedicineAddPageCategoryItem } from "./product-add-page";
import { ProductMedicineUsageParams } from "./product-add-page-bean";
import { MedicineAddType } from "../data/outpatient-const";
import { View } from "@hippy/react";
import { ABCStyles, Color, Colors, Sizes, TextStyles } from "../../theme";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import React from "react";
import { AbcTextInput, AbcToggleSystemKeyboardPanel } from "../../base-ui/views/abc-text-input";
import { AlphabetKeyboardBuilder } from "../../base-ui/views/keyboards/alphabet-keyboard";
import { DividerLine } from "../../base-ui";
import { DeviceUtils } from "../../base-ui/utils/device-utils";
import { AntibioticEnum } from "../../base-business/data/beans";

interface ProductAddPageSearchProps {
    keyword?: string; //中药支持外部传入字符串
    medicineUsageParams: ProductMedicineUsageParams;
    allowAntibiotic?: AntibioticEnum[]; // 当前医生允许开出的限制药品类型
}
const inputStyle = DeviceUtils.isOhos()
    ? {
          height: Sizes.dp54,
          ...TextStyles.t18NT1,
          fontSize: Sizes.dp16,
      }
    : {
          flex: 1,
          height: Sizes.dp54,
          paddingLeft: DeviceUtils.isAndroid() ? -6 : undefined,
          paddingVertical: Sizes.dp16,
          ...TextStyles.t18NT1,
          borderWidth: Sizes.dp1,
          borderColor: Colors.transparent,
          fontSize: Sizes.dp16,
      };
export class ProductAddPageSearch extends BaseBlocPage<ProductAddPageSearchProps, ProductAddPageBloc> {
    private _keyword: string;
    private _currentCategoryId?: number;

    pageName(): string | undefined {
        return "诊疗项目搜索";
    }

    constructor(props: ProductAddPageSearchProps) {
        super(props);
        this._keyword = props.keyword ?? "";
        this._currentCategoryId = props.medicineUsageParams.type;
        this.bloc = new ProductAddPageBloc({
            props: props.medicineUsageParams,
            isCallInterface: true,
            searchAllType: true,
            allowAntibiotic: props.allowAntibiotic,
        });
    }
    getAppBar(): JSX.Element {
        return (
            <View style={[ABCStyles.bottomLine, { marginHorizontal: Sizes.dp16 }]}>
                <AbcTextInput
                    autoFocus={true}
                    multiline={false}
                    placeholder={"请输入项目拼音码"}
                    style={inputStyle}
                    placeholderTextColor={Colors.t3}
                    customPanelBuilder={(textInput) => <AbcToggleSystemKeyboardPanel textInput={textInput} />}
                    customKeyboardBuilder={new AlphabetKeyboardBuilder()}
                    onChangeText={(text) => {
                        this._keyword = text;
                        this.bloc.requestSearchGoods(text);
                    }}
                    onFocus={() => {
                        this.bloc.requestUpdateSearchFocus(true);
                    }}
                />
            </View>
        );
    }

    getShowStatusBar(): boolean {
        return false;
    }

    getBackgroundColor(): Color {
        return Colors.white;
    }

    private _onCategoryChanged(category: MedicineAddPageCategoryItem): void {
        if (this._currentCategoryId == category.id) return;
        this._currentCategoryId = category.id;
        this.bloc.requestUpdateSearchType(category.id);
    }

    _renderDefaultSearch(): JSX.Element {
        const { switchTypes } = this.props.medicineUsageParams;
        const state = this.bloc.currentState;
        let categoryList = undefined;

        if (switchTypes?.length) {
            categoryList = [];
            switchTypes.forEach((item) => {
                switch (item) {
                    case MedicineAddType.examination:
                        categoryList.push(new MedicineAddPageCategoryItem(MedicineAddType.examination, "检查检验"));
                        break;
                    case MedicineAddType.treatment:
                        categoryList.push(new MedicineAddPageCategoryItem(MedicineAddType.treatment, "治疗理疗"));
                        break;
                    case MedicineAddType.medicalMaterial:
                        categoryList.push(new MedicineAddPageCategoryItem(MedicineAddType.medicalMaterial, "器械耗材"));
                        break;
                    case MedicineAddType.goods:
                        categoryList.push(new MedicineAddPageCategoryItem(MedicineAddType.goods, "商品"));
                        break;
                    case MedicineAddType.others:
                        categoryList.push(new MedicineAddPageCategoryItem(MedicineAddType.others, "其他费用"));
                        break;
                    case MedicineAddType.package:
                        categoryList.push(new MedicineAddPageCategoryItem(MedicineAddType.package, "套餐"));
                    case MedicineAddType.nurse:
                        categoryList.push(new MedicineAddPageCategoryItem(MedicineAddType.nurse, "护理"));
                    case MedicineAddType.surgery:
                        categoryList.push(new MedicineAddPageCategoryItem(MedicineAddType.surgery, "手术"));
                }
            });
        }
        return (
            <View
                style={{
                    flex: 1,
                    marginHorizontal: -Sizes.dp16,
                }}
            >
                {!state.keyword && (
                    <View>
                        <_NewCategoryPicker
                            selectCategory={this._currentCategoryId!}
                            categoryList={categoryList}
                            onChanged={(newCategory) => this._onCategoryChanged(newCategory)}
                        />
                    </View>
                )}
                {!state.keyword && <DividerLine lineHeight={Sizes.dpHalf} style={{ marginHorizontal: Sizes.dp16 }} />}

                <_GoodsSearchDisplayView
                    onClickItem={(goods) => {
                        ABCNavigator.pop(goods);
                    }}
                />
            </View>
        );
    }

    renderContent(): JSX.Element {
        return <View style={{ flex: 1, paddingHorizontal: Sizes.dp16 }}>{this._renderDefaultSearch()}</View>;
    }
}
