import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Spacer } from "../../base-ui";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import { ScrollView, View } from "@hippy/react";
import React from "react";
import { BlocBuilder } from "../../bloc";
import { PatientDetailEditPageProps, PatientEditPageBloc } from "./patient-edit-page-bloc";
import { AddressPickerAddressInfo } from "../../base-ui/picker/address-picker";
import { JsonMapper } from "../../common-base-module/json-mapper/json-mapper";
import { AbcTextInput, SimpleFinishPanel } from "../../base-ui/views/abc-text-input";
import { DeviceUtils } from "../../base-ui/utils/device-utils";
import {
    ListSettingAddressEditItem,
    ListSettingEditItem,
    ListSettingItem,
    ListSettingItemStyle,
    ListSettingRadiosItem,
} from "../../base-ui/views/list-setting-item";
import { CityInfo, DistrictInfo, ProvinceInfo } from "../data/patients";
import { NumberKeyboardBuilder } from "../../base-ui/views/keyboards/number-keyboard";
import _ from "lodash";
import { PatientSourceTypeSelectDialog } from "./patient-source-type-select-dialog";
import { LengthLimitingTextInputFormatter, PrecisionLimitFormatter } from "../../base-ui/utils/formatter";
import { PatientSource } from "../../base-business/data/beans";
import { showOptionsBottomSheet } from "../../base-ui/dialog/bottom_sheet";
import { ethnicityOptions, maritalOptions, professionOptions } from "../../data/crm/options";
import { BaseBlocPage } from "../../base-ui/base-page";
import { pxToDp } from "../../base-ui/utils/ui-utils";
import { AbcButton } from "../../base-ui/views/abc-button";
import { AbcText } from "../../base-ui/views/abc-text";
import PhoneCountryCodeSelectView from "../../views/patient-base-common-views/phone-country-code-select-view";
import { userCenter } from "../../user-center";
import { PatientInfoMethod } from "../../base-business/data/patient-beans";
import IdCardTypeSelectView from "../../views/patient-base-common-views/area-code-select-view";
import { ValidatorUtils } from "../../base-ui/utils/validator-utils";

export default class PatientDetailEditPage extends BaseBlocPage<PatientDetailEditPageProps, PatientEditPageBloc> {
    protected areaDetailInputHeight = Sizes.dp64;

    static defaultProps = {
        mode: "edit",
        patientSwitchable: true,
    };
    private _patientNameInput?: AbcTextInput | null;
    protected _mobileTextInput?: AbcTextInput | null;
    protected readonly _defaultAddressInfo?: AddressPickerAddressInfo;

    constructor(props: PatientDetailEditPageProps) {
        super(props);
        this.bloc = new PatientEditPageBloc(props);

        this._defaultAddressInfo = JsonMapper.deserialize(AddressPickerAddressInfo, {
            provinceInfo: JsonMapper.deserialize(ProvinceInfo, {
                id: userCenter.clinic?.addressProvinceId,
                name: userCenter.clinic?.addressProvinceName,
            }),
            cityInfo: JsonMapper.deserialize(CityInfo, {
                id: userCenter.clinic?.addressCityId,
                name: userCenter.clinic?.addressCityName,
            }),
            districtInfo: JsonMapper.deserialize(DistrictInfo, {
                id: userCenter.clinic?.addressDistrictId,
                name: userCenter.clinic?.addressDistrictName,
            }),
        });
    }

    getAppBar(): any {
        return <BlocBuilder bloc={this.bloc} condition={() => true} build={() => super.getAppBar()} />;
    }

    getAppBarTitle(): string {
        if (this.props.mode == "create") return "新建患者";
        else if (this.bloc.currentState.isEditing) return "编辑患者信息";
        else if (this.props.mode == "edit") return "患者信息";
        else return "编辑患者信息";
    }

    getRightAppBarIcons(): JSX.Element[] {
        if (!this.bloc.currentState.isEditing) return [<View key={"empty"} />];

        return [
            <AbcButton
                key={"save"}
                style={{ width: Sizes.dp52, height: Sizes.dp30 }}
                text={"保存"}
                onClick={
                    this.bloc.currentState.hasChanged || this.props.mode == "create"
                        ? () => this.savePatientInfo() // 保存
                        : undefined
                }
            />,
        ];
    }

    onBackClick(): void {
        this.bloc.requestBackPage();
    }

    savePatientInfo(): void {
        this.bloc.requestSaveEditInfo();
    }

    renderContent(): JSX.Element {
        const { /*mode,*/ /*pastHistory,*/ showAll } = this.props;
        const state = this.bloc.currentState,
            { isEditing, patientInfo, canEditSn, canEditFirstFromAway, canEditIdCard, canEditPatientName, consultantList } = state;
        const clinicFieldConfig = state.clinicFieldConfig;
        const oldPatient = !_.isEmpty(patientInfo?.id);
        const {
            showAllWhenOldPatient = oldPatient || showAll,
            showAddressInfo = showAllWhenOldPatient,
            showIDInfo = showAllWhenOldPatient,
            showPatientSource = showAllWhenOldPatient,
            showSN = showAllWhenOldPatient,
            showProfession = showAllWhenOldPatient,
            showRemark = showAllWhenOldPatient,
            requirePatientSource = false,
            requireMobile = false,
        } = this.props;
        const requireConsultant = clinicFieldConfig?.getFieldConfigDetail({
            sourceKey: "patient",
            type: "create",
            field: "consultantId",
        })?.required;
        if (!patientInfo) return <View />;
        const _addressInfo = JsonMapper.deserialize(AddressPickerAddressInfo, {
            provinceInfo: JsonMapper.deserialize(ProvinceInfo, {
                id: patientInfo.address?.addressProvinceId,
                name: patientInfo.address?.addressProvinceName,
            }),
            cityInfo: JsonMapper.deserialize(CityInfo, {
                id: patientInfo.address?.addressCityId,
                name: patientInfo.address?.addressCityName,
            }),
            districtInfo: JsonMapper.deserialize(DistrictInfo, {
                id: patientInfo.address?.addressDistrictId,
                name: patientInfo.address?.addressDistrictName,
            }),
        });

        let idTextInput: AbcTextInput | null = null;
        const memberName = patientInfo.memberInfo?.memberTypeInfo?.memberTypeName;
        //可以查看患者手机号
        const canSeePatientMobile = PatientInfoMethod.canSeePatientMobile({ ...this.props });
        // TODO：下面争对鸿蒙单独判断，去掉contentHintTextStyle样式，是解决下面报错问题，为什么又只改ListSettingEditItem中的样式呢，是因为其他组件没有key属性,只有这个组件继承了ListSettingItem，具体原因还未分析
        //  ListSettingItem: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop
        const isOhos = DeviceUtils.isOhos();

        const patientListConfig = [
            {
                key: "name",
                title: "姓名",
                onlineConfig: clinicFieldConfig?.getFieldConfigDetail({ sourceKey: "patient", type: "create", field: "name" }),
                contentBuilder: () => (
                    <View style={{ flexDirection: "row", flex: 1 }}>
                        {!isEditing && !patientInfo?.name ? (
                            <View />
                        ) : (
                            <AbcTextInput
                                ref={(ref) => {
                                    this._patientNameInput = ref;
                                }}
                                autoFocus={isEditing && !patientInfo?.name && !patientInfo?.id}
                                editable={isEditing && canEditPatientName}
                                placeholder={"输入姓名"}
                                placeholderTextColor={Colors.T4}
                                defaultValue={patientInfo?.name}
                                syncTextOnBlur={true}
                                multiline={false}
                                maxLength={64}
                                formatter={(oldValue, newValue) => {
                                    return newValue.trim();
                                }}
                                style={{
                                    ...TextStyles.t16NB,
                                    flex: 1,
                                    height: Sizes.dp20,
                                    backgroundColor: Colors.white,
                                    underlineColorAndroid: Colors.white,
                                    paddingLeft: DeviceUtils.isAndroid() ? -6 : 0,
                                }}
                                onChangeText={(name: string) => {
                                    this.bloc.requestChangePatientName(name);
                                }}
                                onEndEditing={() => {
                                    isEditing && !oldPatient && !patientInfo?.mobile ? this._mobileTextInput?.focus() : undefined;
                                }}
                            />
                        )}
                        {this.props.__switchPatient && (
                            <AbcText
                                style={TextStyles.t14NM}
                                onClick={() => {
                                    this.bloc.requestChangePatient();
                                }}
                            >
                                {"更换患者"}
                            </AbcText>
                        )}
                    </View>
                ),
            },
            {
                key: "sex",
                nodeType: ListSettingRadiosItem,
                title: "性别",
                enable: isEditing,
                check: patientInfo?.sex,
                options: ["男", "女"],
                marginBetweenItem: 41,
                onlineConfig: clinicFieldConfig?.getFieldConfigDetail({ sourceKey: "patient", type: "create", field: "sex" }),
                onChanged: (option: string) => {
                    this.bloc.requestUpdatePatientSex(option);
                    isEditing && !oldPatient && !patientInfo?.mobile ? this._mobileTextInput?.focus() : undefined;
                },
            },
            {
                key: "phone",
                title: "手机",
                starTitle: requireMobile ? "*" : undefined,
                onlineConfig: clinicFieldConfig?.getFieldConfigDetail({ sourceKey: "patient", type: "create", field: "mobile" }),
                onClick: () => isEditing && this._mobileTextInput?.focus(),
                contentBuilder: () => {
                    if (!isEditing && !patientInfo?.mobile) {
                        return <View />;
                    } else {
                        return (
                            <View style={[ABCStyles.rowAlignCenter]}>
                                <AbcTextInput
                                    ref={(ref) => (this._mobileTextInput = ref)}
                                    editable={isEditing}
                                    multiline={false}
                                    defaultValue={
                                        canSeePatientMobile
                                            ? patientInfo?.mobile
                                            : PatientInfoMethod.encryptThePhoneNumber(patientInfo?.mobile)
                                    }
                                    syncTextOnBlur={true}
                                    placeholder={"输入手机号"}
                                    maxLength={11}
                                    placeholderTextColor={Colors.T4}
                                    customKeyboardBuilder={new NumberKeyboardBuilder({})}
                                    formatter={(oldValue, newValue) => {
                                        if (_.isEmpty(newValue)) return newValue;
                                        //不可查看患者手机号时，编辑只能全部清空，重新输入
                                        if (!canSeePatientMobile && !/^\d+$/.test(newValue)) return "";
                                        if (/^\d+$/.test(newValue) && newValue.length <= 11) {
                                            return newValue;
                                        } else {
                                            return oldValue;
                                        }
                                    }}
                                    style={{
                                        ...TextStyles.t16NB,
                                        width: pxToDp(200),
                                        height: Sizes.dp20,
                                        backgroundColor: Colors.white,
                                        underlineColorAndroid: Colors.white,
                                        paddingLeft: DeviceUtils.isAndroid() ? -6 : 0,
                                    }}
                                    onChangeText={(tel: string) => {
                                        this.bloc.requestChangePatientTel(tel);
                                    }}
                                    onEndEditing={() => {
                                        isEditing &&
                                            !oldPatient &&
                                            !patientInfo.age?.displayAge &&
                                            !!patientInfo?.canEditAge &&
                                            this.bloc.requestChangePatientAge();
                                    }}
                                />
                                <Spacer />
                                <PhoneCountryCodeSelectView
                                    code={patientInfo?.countryCode}
                                    onChange={(code) => {
                                        this.bloc.requestModifyPatientCountryCode(code);
                                    }}
                                />
                            </View>
                        );
                    }
                },
            },
            {
                key: "age",
                type: "age",
                title: "年龄",
                contentHint: "点击选择",
                contentHintTextStyle: TextStyles.t16NT4,
                content: patientInfo.age?.displayAge ?? "",
                onlineConfig: clinicFieldConfig?.getFieldConfigDetail({ sourceKey: "patient", type: "create", field: "age" }),
                onClick: () => {
                    if (isEditing && !!patientInfo?.canEditAge) this.bloc.requestChangePatientAge();
                },
            },
            {
                key: "birthday",
                type: "birthday",
                title: "生日",
                contentHint: "点击选择",
                content: patientInfo.birthday ?? "",
                onlineConfig: clinicFieldConfig?.getFieldConfigDetail({ sourceKey: "patient", type: "create", field: "birthday" }),
                onClick: () => {
                    if (isEditing) this.bloc.requestChangePatientBirthday();
                },
            },
            {
                key: "member",
                type: "member",
                title: "会员",
                content: memberName ?? "",
                hidden: !patientInfo?.isMember,
            },
        ];

        const extendListConfig = [
            {
                key: "consultant",
                title: "咨询师",
                itemStyle: isEditing ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal,
                starTitle: requireConsultant ? "*" : undefined,
                hidden: !userCenter.clinic?.isDentistryClinic,
                contentHint: "选择咨询师",
                contentHintTextStyle: TextStyles.t16NT4,
                content: consultantList?.find((t) => t.employeeId == patientInfo?.consultantId)?.employeeName ?? "",
                onlineConfig: clinicFieldConfig?.getFieldConfigDetail({ sourceKey: "patient", type: "create", field: "consultantId" }),
                onClick: async () => {
                    if (!isEditing) return;
                    this.bloc.requestModifyConsultant();
                },
            },
            {
                key: "sourceInfo",
                title: "来源",
                itemStyle: isEditing && canEditFirstFromAway ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal,
                starTitle: requirePatientSource ? "*" : undefined,
                hidden: !showPatientSource,
                contentHint: "不选择",
                contentHintTextStyle: TextStyles.t16NT4,
                content: patientInfo?.patientSource?.sourceDisplay,
                onlineConfig: clinicFieldConfig?.getFieldConfigDetail({ sourceKey: "patient", type: "create", field: "sourceInfo" }),
                onClick: async () => {
                    if (!isEditing || !canEditFirstFromAway) return;
                    let source = await PatientSourceTypeSelectDialog.show({ source: patientInfo?.patientSource });
                    if (source) {
                        if (!source.id?.length) source = new PatientSource();
                        this.bloc.requestUpdatePatientSource(source);
                    }
                },
            },
            {
                key: "cardNo",
                title: "证件号码",
                hidden: !showIDInfo,
                onClick: () => isEditing && idTextInput?.focus(),
                onlineConfig: clinicFieldConfig?.getFieldConfigDetail({ sourceKey: "patient", type: "create", field: "certificates" }),
                contentBuilder: () => {
                    if (!isEditing && !patientInfo?.idCard) {
                        return <View />;
                    } else {
                        return (
                            <View style={[ABCStyles.rowAlignCenter]}>
                                <AbcTextInput
                                    editable={isEditing && canEditIdCard}
                                    ref={(ref) => (idTextInput = ref)}
                                    placeholder={isEditing && canEditIdCard ? "输入身份证号码" : ""}
                                    placeholderTextColor={Colors.T4}
                                    defaultValue={patientInfo?.idCard}
                                    syncTextOnBlur={true}
                                    multiline={false}
                                    maxLength={patientInfo?.idCardType === "其它" ? 20 : 18}
                                    formatter={(oldValue, newValue) => {
                                        return newValue.trim();
                                    }}
                                    style={{
                                        ...TextStyles.t16NB,
                                        flex: 1,
                                        height: Sizes.dp20,
                                        backgroundColor: Colors.white,
                                        underlineColorAndroid: Colors.white,
                                        paddingLeft: DeviceUtils.isAndroid() ? -6 : 0,
                                    }}
                                    onChangeText={(name: string) => {
                                        this.bloc.requestUpdateIdCard(name);
                                    }}
                                    onBlur={() => {
                                        ValidatorUtils.validateIdCard({ idCard: patientInfo?.idCard, type: patientInfo?.idCardType });
                                    }}
                                />
                                <IdCardTypeSelectView
                                    value={patientInfo?.idCardType}
                                    onChange={(value) => {
                                        isEditing && canEditIdCard && this.bloc.requestUpdatePatientIdCardType(value);
                                    }}
                                />
                            </View>
                        );
                    }
                },
            },
            {
                key: "profession",
                title: "职业",
                hidden: !showProfession,
                itemStyle: isEditing ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal,
                contentHint: "不选择",
                contentHintTextStyle: TextStyles.t16NT4,
                content: patientInfo?.profession,
                onlineConfig: clinicFieldConfig?.getFieldConfigDetail({ sourceKey: "patient", type: "create", field: "profession" }),
                onClick: async () => {
                    if (!isEditing) return;
                    let init;
                    if (patientInfo?.profession) {
                        const _init = professionOptions.findIndex((item) => item.value == patientInfo.profession);
                        if (_init > -1) {
                            init = new Set([_init]);
                        }
                    }
                    const profession = await showOptionsBottomSheet({
                        title: "选择职业",
                        options: professionOptions.map((item) => item.value),
                        initialSelectIndexes: init,
                        showTopRadius: true,
                        titlePosition: "center",
                        titleStyle: TextStyles.t18MT1,
                        showCloseButton: true,
                    });
                    if (profession && profession.length) {
                        this.bloc.requestUpdatePatientProfession(professionOptions[profession[0]].value);
                    }
                },
            },
            {
                nodeType: ListSettingEditItem,
                key: "sn",
                title: "档案号",
                hidden: !showSN,
                editable: isEditing && canEditSn,
                content: patientInfo?.sn ?? undefined,
                contentHint: canEditSn ? (isEditing ? "若不填写，挂号完成将自动生成" : "") : "已禁用，挂号完成将自动生成",
                contentHintTextStyle: isOhos ? undefined : TextStyles.t16NT4,
                keyboardType: "numeric",
                customPanelBuilder: () => (DeviceUtils.isAndroid() ? <View /> : <SimpleFinishPanel />),
                formatter: LengthLimitingTextInputFormatter(18),
                onlineConfig: clinicFieldConfig?.getFieldConfigDetail({ sourceKey: "patient", type: "create", field: "sn" }),
                onChanged: (sn: string) => isEditing && this.bloc.requestUpdateSN(sn),
            },
            {
                nodeType: ListSettingEditItem,
                key: "remark",
                title: "备注",
                hidden: !showRemark,
                editable: isEditing,
                content: patientInfo?.remark ?? undefined,
                contentHint: "添加备注",
                contentHintTextStyle: isOhos ? undefined : TextStyles.t16NT4,
                onlineConfig: clinicFieldConfig?.getFieldConfigDetail({ sourceKey: "patient", type: "create", field: "remark" }),
                onChanged: (remark: string) => isEditing && this.bloc.requestUpdateRemark(remark),
            },
            {
                nodeType: ListSettingAddressEditItem,
                key: "address",
                title: "所在地区",
                hidden: !showAddressInfo,
                hint: !isEditing && !_addressInfo.displayString ? "" : "点击选择",
                contentHintTextStyle: TextStyles.t16NT4,
                addressInfo: _addressInfo,
                editable: isEditing,
                defaultAddressInfo: this._defaultAddressInfo,
                onlineConfig: clinicFieldConfig?.getFieldConfigDetail({ sourceKey: "patient", type: "create", field: "address" }),
                onChanged: (address: AddressPickerAddressInfo) => {
                    if (address && isEditing) {
                        this.bloc.requestUpdateAddress(address.provinceInfo!, address.cityInfo!, address.districtInfo!);
                    }
                },
            },
            {
                key: "addressDetail",
                title: "详细地址",
                hidden: !showAddressInfo,
                height: this.areaDetailInputHeight - (DeviceUtils.isAndroid() ? Sizes.dp14 : Sizes.dp6),
                contentStyle: { paddingVertical: Sizes.dp12 - (DeviceUtils.isAndroid() ? 3 : 0) },
                onlineConfig: clinicFieldConfig?.getFieldConfigDetail({ sourceKey: "patient", type: "create", field: "address" }),
                contentBuilder: () => {
                    if (!isEditing && !patientInfo?.address?.addressDetail) {
                        return <View />;
                    } else {
                        return (
                            <AbcTextInput
                                editable={isEditing}
                                multiline={true}
                                defaultValue={patientInfo?.address?.addressDetail}
                                syncTextOnBlur={true}
                                style={{
                                    ...TextStyles.t16NB,
                                    paddingLeft: DeviceUtils.isAndroid() ? -6 : 0,
                                    height: this.areaDetailInputHeight,
                                    paddingVertical: Sizes.dp6,
                                }}
                                onContentSizeChange={(event) => {
                                    if (event?.contentSize.height > this.areaDetailInputHeight) {
                                        this.areaDetailInputHeight = event?.contentSize.height;
                                        this.forceUpdate();
                                    }
                                }}
                                placeholder={"如道路、门牌号、小区、楼栋号等"}
                                placeholderTextColor={Colors.T4}
                                maxLength={500}
                                onChangeText={(areaDetail) => {
                                    this.bloc.requestChangePatientAreaDetail(areaDetail);
                                }}
                            />
                        );
                    }
                },
            },
            {
                nodeType: ListSettingEditItem,
                key: "company",
                title: "工作单位",
                editable: isEditing,
                content: patientInfo.company ?? undefined,
                onlineConfig: clinicFieldConfig?.getFieldConfigDetail({ sourceKey: "patient", type: "create", field: "company" }),
                onChanged: (company: string) => isEditing && this.bloc.requestUpdatePatientCompany(company),
            },
            {
                nodeType: ListSettingEditItem,
                key: "weight",
                title: "体重",
                endIcon: () => <View>KG</View>,
                editable: isEditing,
                content: String(patientInfo.weight ?? ""),
                keyboardType: "numeric",
                customPanelBuilder: () => (DeviceUtils.isAndroid() ? <View /> : <SimpleFinishPanel />),
                formatter: PrecisionLimitFormatter(2, 999, 0),
                onlineConfig: clinicFieldConfig?.getFieldConfigDetail({ sourceKey: "patient", type: "create", field: "weight" }),
                onChanged: (weight: number) => isEditing && this.bloc.requestUpdatePatientWeight(weight),
            },
            {
                nodeType: ListSettingEditItem,
                key: "visitReason",
                title: "到店原因",
                editable: isEditing,
                content: patientInfo.visitReason ?? undefined,
                onlineConfig: clinicFieldConfig?.getFieldConfigDetail({ sourceKey: "patient", type: "create", field: "visitReason" }),
                onChanged: (visitReason: string) => isEditing && this.bloc.requestUpdatePatientVisitReason(visitReason),
            },
            {
                key: "marital",
                title: "婚否",
                editable: isEditing,
                content: patientInfo.maritalStr ?? undefined,
                onlineConfig: clinicFieldConfig?.getFieldConfigDetail({ sourceKey: "patient", type: "create", field: "marital" }),
                onClick: async () => {
                    if (!isEditing) return;
                    let init;
                    if (patientInfo?.marital) {
                        const _init = maritalOptions.findIndex((item) => item.value == patientInfo.marital);
                        if (_init > -1) {
                            init = new Set([_init]);
                        }
                    }
                    const marital = await showOptionsBottomSheet({
                        title: "选择婚姻状态",
                        options: maritalOptions.map((item) => item.label),
                        initialSelectIndexes: init,
                        showTopRadius: true,
                        titlePosition: "center",
                        titleStyle: TextStyles.t18MT1,
                        showCloseButton: true,
                    });
                    if (marital && marital.length) {
                        this.bloc.requestUpdatePatientMarital(maritalOptions[marital[0]].value);
                    }
                },
            },
            {
                key: "ethnicity",
                title: "民族",
                editable: isEditing,
                content: patientInfo.ethnicity ?? undefined,
                onlineConfig: clinicFieldConfig?.getFieldConfigDetail({ sourceKey: "patient", type: "create", field: "ethnicity" }),
                onClick: async () => {
                    if (!isEditing) return;
                    let init;
                    if (patientInfo?.ethnicity) {
                        const _init = ethnicityOptions.findIndex((item) => item == patientInfo.ethnicity);
                        if (_init > -1) {
                            init = new Set([_init]);
                        }
                    }
                    const ethnicity = await showOptionsBottomSheet({
                        title: "选择民族",
                        options: ethnicityOptions.map((item) => item),
                        initialSelectIndexes: init,
                        showTopRadius: true,
                        titlePosition: "center",
                        titleStyle: TextStyles.t18MT1,
                        showCloseButton: true,
                    });
                    if (ethnicity && ethnicity.length) {
                        this.bloc.requestUpdatePatientEthnicity(ethnicityOptions[ethnicity[0]]);
                    }
                },
            },
        ];

        return (
            <View style={{ justifyContent: "space-between", flex: 1 }}>
                <ScrollView style={{ flex: 1 }}>
                    <View style={{ backgroundColor: Colors.white, paddingHorizontal: Sizes.listHorizontalMargin }}>
                        {patientListConfig
                            .filter((item) => !item.hidden)
                            .map((item, index, self) => {
                                const { nodeType, ...others } = item;
                                //@ts-ignore
                                return React.createElement(nodeType ?? ListSettingItem, {
                                    itemStyle: ListSettingItemStyle.normal,
                                    titleStyle: TextStyles.t16NT2.copyWith({ color: Colors.t2 }),
                                    bottomLine: index < self.length,
                                    ...others,
                                    key: index,
                                });
                            })}
                    </View>
                    <DividerLine lineHeight={Sizes.dp6} style={{ backgroundColor: Colors.window_bg }} />
                    <View style={{ backgroundColor: Colors.white, paddingHorizontal: Sizes.listHorizontalMargin }}>
                        {extendListConfig
                            .filter((item) => !item.hidden)
                            .sort((a, b) => (a.onlineConfig?.sort ?? 0) - (b.onlineConfig?.sort ?? 0))
                            .map((item, index, self) => {
                                const { nodeType, ...others } = item;
                                //@ts-ignore
                                return React.createElement(
                                    nodeType ?? ListSettingItem,
                                    Object.assign(
                                        {
                                            itemStyle: ListSettingItemStyle.normal,
                                            titleStyle: TextStyles.t16NT2.copyWith({ color: Colors.t2 }),
                                            bottomLine: index < self.length,
                                            key: index,
                                        },
                                        others
                                    )
                                );
                            })}
                    </View>
                </ScrollView>
            </View>
        );
    }
}
