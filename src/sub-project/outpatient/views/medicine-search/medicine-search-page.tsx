/**
 * create by <PERSON><PERSON>
 * desc: 搜索药品通用组件
 * create date 2021/12/29
 */
import React from "react";
import { ListView, Text, View } from "@hippy/react";
import { BaseNetworkPage, SizedBox, Spacer } from "../../../base-ui";
import { ABCStyles, Color, Colors, Sizes, TextStyles } from "../../../theme";
import { Subject } from "rxjs";
import { debounceTime, switchMap } from "rxjs/operators";
import { GoodsAgent } from "../../../data/goods/goods-agent";
import { GoodsInfo, GoodsSubType, GoodsType, Patient } from "../../../base-business/data/beans";
import { ABCError } from "../../../common-base-module/common-error";
import { showBottomPanel } from "../../../base-ui/abc-app-library/panel";
import { ABCNetworkPageContentStatus } from "../../../base-ui/base-page";
import { ABCUtils } from "../../../base-ui/utils/utils";
import { AbcView } from "../../../base-ui/views/abc-view";
import { userCenter } from "../../../user-center";
import { AbcEmptyItemView } from "../../../base-ui/views/empty-view";
import { AbcTextInput } from "../../../base-ui/views/abc-text-input";
import { _ListItem, _NewCategoryPicker, MedicineAddPageCategoryItem } from "../../product-add-page/product-add-page";
import { ProductMedicineUsageParams as DiagnosisUsageParams } from "../../product-add-page/product-add-page-bean";
import { MedicineAddType } from "../../data/outpatient-const";
import abcI18Next from "../../../language/config";
import { PsychotropicNarcoticTagView } from "../../../views/business-tags";

export enum MedicineSearchType {
    external = 1, //外治处方
}

export interface MedicineSearchQueryParams {
    chiefComplaint?: string;
    diagnosis?: string;
    patient?: Patient;
    clinicId?: string;
    departmentId?: string;
}

export interface MedicineSearchPageProps {
    keyword?: string; //中药支持外部传入字符串
    queryParams?: MedicineSearchQueryParams;
    extendQueryParams?: () => {};
    /**
     * @deprecated 字段弃用
     */
    _searchClinicId?: string;
    showPrice?: boolean; // 成药处方

    searchInputRef?: AbcTextInput;

    onChange?(arg1: GoodsInfo): void;

    diagnosisUsage?: DiagnosisUsageParams; //诊疗项目用法
    /**
     * @deprecated 字段弃用
     */
    departmentId?: string;

    coverGoodsAndMedicine?: boolean; //外治处方特殊字段-是否包含商品、药品、器械耗材
}

export class MedicineSearchPage<L> extends BaseNetworkPage<MedicineSearchPageProps> {
    protected _keyword: string;
    protected searchList?: Array<GoodsInfo>;
    protected loadDataTrigger = new Subject<number>();

    constructor(props: MedicineSearchPageProps) {
        super(props);
        this._keyword = props.keyword ?? "";
        this.initLoadDataTrigger();
    }

    static async show<L>(options?: MedicineSearchPageProps): Promise<L> {
        return showBottomPanel(<MedicineSearchPage<L> {...options} />);
    }

    onChangeKeyword(text: string): void {
        if (this._keyword == text) return;
        this._keyword = text;
        this.forceUpdate();
        this.loadDataTrigger.next();
    }

    getAppBar(): JSX.Element {
        return (
            <AbcView
                key={this._keyword}
                style={[ABCStyles.bottomLine, { padding: Sizes.dp16, borderTopRightRadius: Sizes.dp6, borderTopLeftRadius: Sizes.dp6 }]}
                onClick={() => {
                    this.props.searchInputRef?.focus();
                }}
            >
                <Text style={TextStyles.t18NT1}>{this._keyword}</Text>
            </AbcView>
        );
    }

    getShowStatusBar(): boolean {
        return false;
    }

    getBackgroundColor(): Color {
        return Colors.transparent;
    }

    getFillKeyboardContent(): boolean {
        return true;
    }

    componentDidMount(): void {
        if (!!this._keyword) {
            this.loadDataTrigger.next();
        } else {
            this.setContentStatus(ABCNetworkPageContentStatus.empty);
        }
    }

    /**
     * @description 搜索相关trigger
     * @overwrite 子类可进行覆写
     */
    initLoadDataTrigger(): void {
        const { coverGoodsAndMedicine = false } = this.props;
        this.loadDataTrigger
            .pipe(
                debounceTime(50),
                switchMap(() => {
                    this.setContentStatus(ABCNetworkPageContentStatus.loading);
                    const jsonType = [
                        { type: GoodsType.treatment, subType: [GoodsSubType.treatment, GoodsSubType.treatmentPhysiotherapy] },
                        { type: GoodsType.otherGoods49 },
                    ];
                    if (coverGoodsAndMedicine) {
                        jsonType.push(
                            ...[
                                { type: GoodsType.medicine, subType: [GoodsSubType.medicineChinesePatent] },
                                { type: GoodsType.goods },
                                { type: GoodsType.material, subType: [GoodsSubType.materialMedical] },
                            ]
                        );
                    }
                    return GoodsAgent.searchStockGoods({
                        keyword: this._keyword ?? "",
                        jsonType: jsonType,
                    })
                        .catch((error) => new ABCError(error))
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                if (rsp instanceof ABCError) {
                    this.setContentStatus(ABCNetworkPageContentStatus.error, rsp);
                } else {
                    this.searchList = rsp.map((item) => {
                        return item;
                    });
                    if (!!this.searchList?.length) {
                        this.setContentStatus(ABCNetworkPageContentStatus.show_data);
                    } else {
                        this.setContentStatus(ABCNetworkPageContentStatus.empty);
                    }
                }
            })
            .addToDisposableBag(this);
    }

    handleCheckGoodsItem(goodsInfo: GoodsInfo): void {
        this.props.onChange?.(goodsInfo);
    }

    /**
     * @description 搜索相关trigger
     * @overwrite 子类可进行覆写
     */
    emptyContent(): JSX.Element {
        if (!this._keyword) {
            return <AbcEmptyItemView tips={"请输入要添加的项目名称"} />;
        }
        return super.emptyContent();
    }

    /**
     * @description 子类条目样子
     * @overwrite 子类可进行覆写
     */
    renderListItemView(goodsInfo: GoodsInfo): JSX.Element {
        return (
            <View style={[ABCStyles.rowAlignCenterSpaceBetween, ABCStyles.bottomLine, { padding: Sizes.dp16 }]}>
                <View style={{ flexDirection: "row", flex: 1, alignItems: "center" }}>
                    <Text style={{ flexShrink: 1, ...TextStyles.t16MT1 }} numberOfLines={2}>
                        {goodsInfo.displayName ?? ""}
                    </Text>
                    <View style={[ABCStyles.rowAlignCenter, { alignSelf: "flex-start" }]}>
                        <SizedBox width={Sizes.dp6} />
                        <View style={{ padding: Sizes.dp2, backgroundColor: Colors.dividerLineColor, borderRadius: Sizes.dp2 }}>
                            <Text style={TextStyles.t12NT6}>{`${goodsInfo.displayTypeShortNameV2}`}</Text>
                        </View>
                        <PsychotropicNarcoticTagView dangerIngredient={goodsInfo.getIngredientArray} antibiotic={goodsInfo.getAntibiotic} />
                    </View>
                </View>
                <View style={[ABCStyles.rowAlignCenter, { alignSelf: "flex-start" }]}>
                    <SizedBox width={Sizes.dp5} />
                    <Text style={{ ...TextStyles.t16MT1 }}>
                        {`${abcI18Next.t("¥")}${ABCUtils.formatPrice(goodsInfo.packagePrice ?? goodsInfo.unitPrice)}`}
                    </Text>
                </View>
            </View>
        );
    }

    renderContent(): JSX.Element {
        const list = this.searchList ?? [];
        return (
            <View style={{ flex: 1 }}>
                <ListView
                    style={{ flex: 1 }}
                    numberOfRows={list.length}
                    dataSource={list}
                    scrollEventThrottle={300}
                    renderRow={(data) => {
                        return (
                            <AbcView onClick={this.handleCheckGoodsItem.bind(this, data)}>
                                {this.renderListItemView(data as GoodsInfo)}
                            </AbcView>
                        );
                    }}
                    getRowKey={(index) => list[index].scrollKey}
                />
            </View>
        );
    }
}

/**
 * 成药处方搜索药品
 */

export class WesternMedicineSearchPage<L> extends MedicineSearchPage<MedicineSearchPageProps> {
    static async show<L>(options?: MedicineSearchPageProps): Promise<L> {
        return showBottomPanel(<WesternMedicineSearchPage<L> {...options} />);
    }

    initLoadDataTrigger(): void {
        const { queryParams, departmentId, extendQueryParams } = this.props;
        const clinicId = queryParams?.clinicId ?? userCenter.clinic?.clinicId;
        this.loadDataTrigger
            .pipe(
                debounceTime(50),
                switchMap(() => {
                    this.setContentStatus(ABCNetworkPageContentStatus.loading);
                    return GoodsAgent.smartSearchMedicine({
                        keyword: this._keyword,
                        clinicId: clinicId,
                        chiefComplaint: queryParams?.chiefComplaint ?? "",
                        diagnosis: queryParams?.diagnosis ?? "",
                        jsonType: [
                            { type: GoodsType.medicine, subType: [GoodsSubType.medicineWestern, GoodsSubType.medicineChinesePatent] },
                            { type: GoodsType.material },
                        ],
                        sex: queryParams?.patient?.sex,
                        age: queryParams?.patient?.age,
                        goodsIds: [],
                        offset: 0,
                        limit: 50,
                        withDomainMedicine: 1,
                        departmentId: departmentId,
                        sceneType: 1,
                        ...extendQueryParams?.(),
                    })
                        .catch((error) => new ABCError(error))
                        .toObservable();
                })
            )
            .subscribe((data) => {
                if (data instanceof ABCError) {
                    this.setContentStatus(ABCNetworkPageContentStatus.error, data);
                } else {
                    const _inStock: GoodsInfo[] = [],
                        _noStock: GoodsInfo[] = [];
                    data?.forEach((item: GoodsInfo) => {
                        if (item.inStock) _inStock.push(item);
                        else _noStock.push(item);
                    });
                    this.searchList = _inStock.concat(_noStock);
                    if (!!this.searchList?.length) {
                        this.setContentStatus(ABCNetworkPageContentStatus.show_data);
                    } else {
                        this.setContentStatus(ABCNetworkPageContentStatus.empty);
                    }
                }
            })
            .addToDisposableBag(this);
    }

    renderListItemView(goodsInfo: GoodsInfo): JSX.Element {
        const { showPrice } = this.props;
        const isHasStock = goodsInfo.inStock; // 是否有库存
        const displayName =
            (goodsInfo.displayName?.length ?? 0) > 12 ? goodsInfo.displayName.substring(0, 12) + "..." : goodsInfo.displayName;
        return (
            <View
                style={[ABCStyles.rowAlignCenterSpaceBetween, ABCStyles.bottomLine, { height: Sizes.dp72, paddingHorizontal: Sizes.dp16 }]}
            >
                <View style={{ flex: 1, paddingVertical: Sizes.dp12 }}>
                    <View style={{ ...ABCStyles.rowAlignCenterSpaceBetween, flex: 1 }}>
                        <Text
                            style={[
                                isHasStock
                                    ? TextStyles.t16NB.copyWith({ lineHeight: Sizes.dp22 })
                                    : TextStyles.t16NB.copyWith({ color: Colors.t4, lineHeight: Sizes.dp22 }),
                                { flexShrink: 1, marginRight: Sizes.dp4 },
                            ]}
                            numberOfLines={1}
                            ellipsizeMode={"tail"}
                        >
                            {displayName}
                        </Text>
                        <PsychotropicNarcoticTagView dangerIngredient={goodsInfo.getIngredientArray} antibiotic={goodsInfo.antibiotic} />
                        <Text
                            numberOfLines={1}
                            style={[
                                TextStyles.t14NT6.copyWith({ color: isHasStock ? Colors.T6 : Colors.T4, lineHeight: Sizes.dp20 }),
                                {
                                    paddingRight: !!goodsInfo.medicalFeeGrade2Str ? Sizes.dp12 : 0,
                                    flexWrap: "nowrap",
                                    flex: 1,
                                    textAlign: "right",
                                },
                            ]}
                        >
                            {goodsInfo.packageSpec}
                        </Text>
                        {!!goodsInfo.medicalFeeGrade2Str && <Spacer />}
                        {!!goodsInfo.medicalFeeGrade2Str && (
                            <Text style={[TextStyles.t14NT6, { marginLeft: Sizes.dp16 }]}>{goodsInfo.medicalFeeGrade2Str}</Text>
                        )}
                    </View>
                    <SizedBox height={Sizes.dp6} />
                    <View style={{ ...ABCStyles.rowAlignCenter }}>
                        {showPrice && (
                            <Text
                                style={[
                                    isHasStock
                                        ? TextStyles.t14NT6.copyWith({ lineHeight: Sizes.dp20 })
                                        : TextStyles.t14NT3.copyWith({ color: Colors.T4, lineHeight: Sizes.dp20 }),
                                    { marginRight: Sizes.dp12 },
                                ]}
                            >
                                {`${abcI18Next.t("¥")}${goodsInfo.packagePrice?.toFixed(2) ?? "--"}${
                                    !!goodsInfo.packageUnit ? "/" + goodsInfo.packageUnit : ""
                                }`}
                            </Text>
                        )}
                        <Text
                            numberOfLines={1}
                            style={[isHasStock ? TextStyles.t14NT6 : TextStyles.t14NT4, { lineHeight: Sizes.dp20, flexShrink: 1 }]}
                        >
                            {goodsInfo.manufacturer ?? ""}
                        </Text>

                        <Spacer />
                        <Text
                            style={[
                                isHasStock ? (goodsInfo.hasStock ? TextStyles.t14NT6 : TextStyles.t14NY2) : TextStyles.t14NT4,
                                { flexDirection: "row" },
                            ]}
                        >
                            库存{goodsInfo.displayStockInfo()}
                        </Text>
                    </View>
                </View>
            </View>
        );
    }
}

/**
 * 诊疗项目搜索
 */
export class DiagnosisProjectSearchPage<L> extends MedicineSearchPage<MedicineSearchPageProps> {
    private _currentCategoryId?: number;
    constructor(props: MedicineSearchPageProps) {
        super(props);
        this._keyword = props.keyword ?? "";
        this.initLoadDataTrigger();
        this._currentCategoryId = props.diagnosisUsage?.type;
    }

    componentDidMount(): void {
        this.loadDataTrigger.next();
    }

    static async show<L>(options?: MedicineSearchPageProps): Promise<L> {
        return showBottomPanel(<DiagnosisProjectSearchPage<L> {...options} />);
    }

    getAppBar(): JSX.Element {
        const { diagnosisUsage } = this.props;
        const switchTypes = diagnosisUsage?.switchTypes;
        let categoryList = undefined;
        if (switchTypes?.length) {
            categoryList = [];
            switchTypes.forEach((item) => {
                switch (item) {
                    case MedicineAddType.examination:
                        categoryList.push(new MedicineAddPageCategoryItem(MedicineAddType.examination, "检查检验"));
                        break;
                    case MedicineAddType.treatment:
                        categoryList.push(new MedicineAddPageCategoryItem(MedicineAddType.treatment, "治疗理疗"));
                        break;
                    case MedicineAddType.medicalMaterial:
                        categoryList.push(new MedicineAddPageCategoryItem(MedicineAddType.medicalMaterial, "医用材料"));
                        break;
                    case MedicineAddType.goods:
                        categoryList.push(new MedicineAddPageCategoryItem(MedicineAddType.goods, "商品"));
                        break;
                    case MedicineAddType.others:
                        categoryList.push(new MedicineAddPageCategoryItem(MedicineAddType.others, "其他费用"));
                        break;
                    case MedicineAddType.package:
                        categoryList.push(new MedicineAddPageCategoryItem(MedicineAddType.package, "套餐"));
                }
            });
        }
        return (
            <AbcView>
                <AbcView
                    key={this._keyword}
                    style={[ABCStyles.bottomLine, { padding: Sizes.dp16 }]}
                    onClick={() => {
                        this.props.searchInputRef?.focus();
                    }}
                >
                    <Text style={[TextStyles.t18NT1.copyWith({ color: !!this._keyword ? Colors.T1 : Colors.t3 })]}>
                        {!!this._keyword ? this._keyword : "输入项目首字母"}
                    </Text>
                </AbcView>
                {!this._keyword && (
                    <View style={{ height: Sizes.dp44 }}>
                        <_NewCategoryPicker
                            selectCategory={this._currentCategoryId!}
                            categoryList={categoryList}
                            onChanged={(newCategory) => this._onCategoryChanged(newCategory)}
                        />
                    </View>
                )}
            </AbcView>
        );
    }

    initLoadDataTrigger(): void {
        const clinic = userCenter.clinic;
        this.loadDataTrigger
            .pipe(
                switchMap((/*_*/) => {
                    this.setContentStatus(ABCNetworkPageContentStatus.loading);
                    const offset = 0;
                    const limit = 30;

                    //如果当前有搜索词，则进行全部类型搜索，否则只争对单独某一个类别查询
                    if (!!this._keyword) {
                        return GoodsAgent.searchGoodsWithPaginating({
                            clinicId: clinic!.clinicId,
                            keyword: this._keyword,
                            offset,
                            limit,
                            jsonType: [
                                { type: GoodsType.examination },
                                { type: GoodsType.treatment },
                                { type: GoodsType.goods },
                                { type: GoodsType.material },
                                { type: GoodsType.package },
                            ],
                        })
                            .catch((error) => new ABCError(error))
                            .toObservable();
                    } else {
                        switch (this._currentCategoryId) {
                            case MedicineAddType.examination: {
                                return GoodsAgent.searchGoodsWithPaginating({
                                    clinicId: clinic!.clinicId,
                                    keyword: this._keyword,
                                    offset,
                                    limit,
                                    jsonType: [{ type: GoodsType.examination }],
                                })
                                    .catch((error) => new ABCError(error))
                                    .toObservable();
                            }
                            case MedicineAddType.treatment: {
                                return GoodsAgent.searchGoodsWithPaginating({
                                    clinicId: clinic!.clinicId,
                                    keyword: this._keyword,
                                    offset,
                                    limit,
                                    jsonType: [{ type: GoodsType.treatment }],
                                })
                                    .catch((error) => new ABCError(error))
                                    .toObservable();
                            }
                            case MedicineAddType.goods: {
                                return GoodsAgent.searchGoodsWithPaginating({
                                    clinicId: clinic!.clinicId,
                                    keyword: this._keyword,
                                    offset,
                                    limit,
                                    jsonType: [{ type: GoodsType.goods }],
                                })
                                    .catch((error) => new ABCError(error))
                                    .toObservable();
                            }
                            case MedicineAddType.medicalMaterial:
                                return GoodsAgent.searchGoodsWithPaginating({
                                    clinicId: clinic!.clinicId,
                                    keyword: this._keyword,
                                    offset,
                                    limit,
                                    jsonType: [{ type: GoodsType.material, subType: [GoodsSubType.materialMedical] }],
                                })
                                    .catch((error) => new ABCError(error))
                                    .toObservable();
                            case MedicineAddType.others:
                                return GoodsAgent.searchGoodsWithPaginating({
                                    clinicId: clinic!.clinicId,
                                    keyword: this._keyword,
                                    offset,
                                    limit,
                                    jsonType: [{ type: GoodsType.treatment, subType: [GoodsSubType.treatmentOther] }],
                                })
                                    .catch((error) => new ABCError(error))
                                    .toObservable();
                            case MedicineAddType.package:
                                return GoodsAgent.searchGoodsWithPaginating({
                                    clinicId: clinic!.clinicId,
                                    keyword: this._keyword,
                                    offset,
                                    limit,
                                    jsonType: [{ type: GoodsType.package }],
                                })
                                    .catch((error) => new ABCError(error))
                                    .toObservable();
                            default: {
                                return GoodsAgent.searchGoodsWithPaginating({
                                    clinicId: clinic!.clinicId,
                                    keyword: this._keyword,
                                    offset,
                                    limit,
                                    jsonType: [{ type: GoodsType.examination }],
                                })
                                    .catch((error) => new ABCError(error))
                                    .toObservable();
                            }
                        }
                    }
                })
            )
            .subscribe((data) => {
                if (data instanceof ABCError) {
                    this.setContentStatus(ABCNetworkPageContentStatus.error, data);
                } else {
                    const _inStock: GoodsInfo[] = [],
                        _noStock: GoodsInfo[] = [];
                    data.list?.forEach((item: GoodsInfo) => {
                        if (item.inStock) _inStock.push(item);
                        else _noStock.push(item);
                    });
                    this.searchList = _inStock.concat(_noStock);
                    if (!!this.searchList?.length) {
                        this.setContentStatus(ABCNetworkPageContentStatus.show_data);
                    } else {
                        this.setContentStatus(ABCNetworkPageContentStatus.empty);
                    }
                }
            });
    }

    private _onCategoryChanged(category: MedicineAddPageCategoryItem): void {
        if (this._currentCategoryId == category.id) return;
        this._currentCategoryId = category.id;
        this.loadDataTrigger.next();
    }

    renderListItemView(goodsInfo: GoodsInfo): JSX.Element {
        return (
            <View style={{ paddingHorizontal: Sizes.dp16 }}>
                <_ListItem goodsInfo={goodsInfo} />
            </View>
        );
    }
}
