/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-03-24
 *
 * @description
 */

import React from "react";
import { ChatMessage } from "./model/chat-message";
import _ from "lodash";
import { Dimensions, Image, Style, StyleSheet, Text, View } from "@hippy/react";
import { Color, Colors, Sizes, TextStyles } from "../../../../theme";
import SizedBox from "../../../../base-ui/sized-box";
import { BaseComponent } from "../../../../base-ui/base-component";
import AbcAudioView from "../../../../base-ui/views/abc-audio-view";
import { LogUtils } from "../../../../common-base-module/log";
import { FileDownloader } from "../../../../base-business/file/file-downloader";
import { ABCError } from "../../../../common-base-module/common-error";
import { AssetImageView } from "../../../../base-ui/views/asset-image-view";
import { AbcKeyframeView } from "../../../../base-ui/views/abc-keyframe-view";
import { ThemeManager, ThemeType } from "../../../../theme/themes";
import { ArrowView, ArrowViewDirection } from "../../../../base-ui/views/arrow-view";
import FileUtils from "../../../../common-base-module/file/file-utils";
import { Spacer } from "../../../../base-ui";

const { width: screenWidth } = Dimensions.get("window");

const kBorderRadius = Sizes.dp4;

interface ChatColor {
    selfTextBackgroundColor: Color;
    otherTextBackgroundColor: Color;
}

const chatColors = new Map<ThemeType, ChatColor>([
    [
        ThemeType.normal,
        {
            selfTextBackgroundColor: "#44E67E",
            otherTextBackgroundColor: Colors.white,
        },
    ],

    [
        ThemeType.chineseMedicine,
        {
            selfTextBackgroundColor: "#F3B8A3",
            otherTextBackgroundColor: Colors.white,
        },
    ],
]);

function _getChatColor(): ChatColor {
    const config = chatColors.get(ThemeManager.currentTheme);
    return config ?? chatColors.get(ThemeType.normal)!;
}

// @ts-ignore
const styles = StyleSheet.create({
    selfTextContainer: {
        backgroundColor: _getChatColor().selfTextBackgroundColor,
        paddingHorizontal: Sizes.dp16,
        borderRadius: kBorderRadius,
        paddingVertical: Sizes.dp8,
        marginLeft: Sizes.dp51,
    },
    otherTextContainer: {
        backgroundColor: _getChatColor().otherTextBackgroundColor,
        paddingHorizontal: Sizes.dp16,
        borderRadius: kBorderRadius,
        paddingVertical: Sizes.dp8,
        marginRight: Sizes.dp51,
    },

    voiceWidth: {
        width: 24,
        height: 24,
    },
    voiceContainer: {
        flexDirection: "row",
        alignItems: "center",
        borderRadius: kBorderRadius,
        justifyContent: "flex-end",
    },
});

interface MessageViewProps {
    message: ChatMessage;
    isUser: boolean;
    onClick?: () => void;
    onLongClick?: (ref?: View | null) => void;
    cacheDir?: string; //语音等相关数据的存储路径
}

export class MessageView extends React.Component<MessageViewProps> {
    maxWidth: number;

    constructor(props: MessageViewProps) {
        super(props);
        const { width } = Dimensions.get("window");
        this.maxWidth = width * 0.6;
    }

    public contentRef?: View | null;

    render(): JSX.Element {
        const { message, isUser } = this.props;
        const maxWidth = Sizes.dp140;
        let imageWidth = Sizes.dp140;
        let imageHeight = Sizes.dp140;
        if (!_.isEmpty(message.image) && message.imageSize && message.imageSize.width && message.imageSize.height) {
            const size = message.imageSize;
            const width = size.width || 0;
            const height = size.height || 0;
            const aspect = size.width! / size.height!;
            if (width > height) {
                imageWidth = Math.min(maxWidth, width);
                imageHeight = imageWidth / aspect;
            } else {
                imageHeight = Math.min(maxWidth, height);
                imageWidth = imageHeight * aspect;
            }
        }

        let content: JSX.Element | undefined;
        let bgColor = Colors.white;
        let needArrow = true;
        if (!_.isEmpty(message.text)) {
            bgColor = (isUser ? styles.selfTextContainer : styles.otherTextContainer).backgroundColor;
            content = (
                <View
                    style={{
                        flexDirection: "row",
                    }}
                >
                    {isUser && <Spacer />}
                    <View
                        style={isUser ? styles.selfTextContainer : styles.otherTextContainer}
                        onLongClick={() => {
                            this.props.onLongClick?.(this.contentRef);
                        }}
                        onClick={this.props.onClick}
                        ref={(ref) => (this.contentRef = ref)}
                    >
                        <Text style={[TextStyles.t16NB, { lineHeight: Sizes.dp24 }]}>{message.text || ""}</Text>
                    </View>
                    {!isUser && <Spacer />}
                </View>
            );
        } else if (!_.isEmpty(message.image)) {
            needArrow = false;
            content = (
                <View ref={(ref) => (this.contentRef = ref)} collapsable={false} onClick={() => ({})}>
                    <Image
                        source={{ uri: message.image! }}
                        style={{
                            width: imageWidth,
                            height: imageHeight,
                            borderRadius: Sizes.dp3,
                            borderWidth: 0.5,
                            borderColor: Colors.P1,
                        }}
                        onClick={this.props.onClick}
                        onLongClick={() => {
                            this.props.onLongClick?.(this.contentRef);
                        }}
                    />
                </View>
            );
        } else if (!_.isEmpty(message.audio)) {
            //音频
            bgColor = isUser ? _getChatColor().selfTextBackgroundColor : _getChatColor().otherTextBackgroundColor;
            content = (
                <_AudioMessage
                    message={message}
                    cacheDir={this.props.cacheDir}
                    style={{ backgroundColor: bgColor }}
                    onLongClick={isUser ? this.props.onLongClick : undefined}
                />
            );
        } else content = message.render();

        if (!content) {
            bgColor = (isUser ? styles.selfTextContainer : styles.otherTextContainer).backgroundColor;
            content = (
                <View
                    style={{
                        flexDirection: "row",
                    }}
                >
                    {isUser && <Spacer />}
                    <View
                        style={isUser ? styles.selfTextContainer : styles.otherTextContainer}
                        onLongClick={this.props.onLongClick}
                        onClick={this.props.onClick}
                    ></View>
                    {!isUser && <Spacer />}
                </View>
            );
        }

        return (
            <View
                style={{
                    flexDirection: "row",
                    justifyContent: isUser ? "flex-end" : "flex-start",
                    flex: 1,
                }}
            >
                {isUser ? <SizedBox width={Sizes.dp50} /> : null}
                {!needArrow && !isUser && <SizedBox width={Sizes.dp6} />}
                {needArrow && !isUser && (
                    <ArrowView size={Sizes.dp6} color={bgColor} direction={ArrowViewDirection.Left} style={{ marginTop: Sizes.dp10 }} />
                )}
                {content}
                {needArrow && isUser && (
                    <ArrowView size={Sizes.dp6} color={bgColor} direction={ArrowViewDirection.Right} style={{ marginTop: Sizes.dp10 }} />
                )}
                {!needArrow && isUser && <SizedBox width={Sizes.dp6} />}
                {!isUser ? <SizedBox width={Sizes.dp50} /> : null}
            </View>
        );
    }
}

interface _AudioMessageProps {
    message: ChatMessage;
    cacheDir?: string;
    style?: Style;
    onLongClick?(ref?: View | null): void;
}

interface _AudioMessageState {
    downloading: boolean;
    downloadError: any;
    playing: boolean;
    playError: any;
}

class _AudioMessage extends BaseComponent<_AudioMessageProps, _AudioMessageState> {
    private _audioView?: AbcAudioView | null;
    private _contentView?: View | null;

    constructor(props: _AudioMessageProps) {
        super(props);

        this.state = {
            downloading: false,
            downloadError: null,
            playing: false,
            playError: null,
        };
    }

    render() {
        const { message, style, onLongClick } = this.props;
        let { playing } = this.state;
        const { downloading } = this.state;
        playing = playing || downloading;

        //根据语音长度计算语音的背景色块长度
        const maxWidth = screenWidth - 100;
        let width = 40 + (message.duration! / 1000 / 60) * maxWidth;
        if (width > maxWidth) {
            width = maxWidth;
        }

        return (
            <View
                style={[styles.voiceContainer, { width: width }, style ?? {}]}
                onClick={this._onClick.bind(this)}
                ref={(ref) => {
                    this._contentView = ref;
                }}
                onLongClick={() => {
                    onLongClick?.(this._contentView);
                }}
            >
                {!_.isNil(message.duration) ? <Text>{`${_.round(message.duration / 1000)}"`}</Text> : null}
                {playing ? (
                    <AbcKeyframeView
                        keyframes={["voice_left1", "voice_left2", "voice_left3"]}
                        duration={1000}
                        loop={true}
                        style={styles.voiceWidth}
                    />
                ) : null}
                {!playing ? <AssetImageView name={"voice_left3"} style={styles.voiceWidth} /> : null}
                <AbcAudioView
                    ref={(ref) => (this._audioView = ref)}
                    src={message.audio!}
                    onPlaying={this._onPlay.bind(this)}
                    onPause={this._onPause.bind(this)}
                    onError={this._onError.bind(this)}
                    onEnded={this._onEnded.bind(this)}
                />
            </View>
        );
    }

    private _onPlay() {
        LogUtils.d("_onPlay ");
        this.setState({ playing: true });
    }

    private _onEnded() {
        this.setState({ playing: false });
    }

    private _onPause() {
        this.setState({ playing: false });
    }

    private _onError(error: any) {
        this.setState({
            playing: false,
            playError: error,
        });
    }

    private async _onClick() {
        const { playing } = this.state;
        if (playing) {
            this._audioView?.pause();
            return;
        }

        const { message, cacheDir } = this.props;
        const fileName = FileUtils.fileNameFromURL(message.audio!);
        const path = `${cacheDir}/${fileName}`;
        LogUtils.d("_AudioMessage._onClick ready download file " + message.audio! + ", to path = " + path);

        if (!(await FileUtils.fileExists(path))) {
            LogUtils.d("_AudioMessage._onClick file not exists");
            this.setState({
                downloading: true,
                downloadError: null,
            });

            //先下载到临时目录
            const tmpFile = `${await FileUtils.getTmpDir()}/${fileName}`;
            await FileUtils.deleteFile(tmpFile).catch((/*ignored*/) => null);

            const result = await FileDownloader.downloadFile(message.audio!, {
                filePath: tmpFile,
                redirect: "follow",
            }).catch((error) => new ABCError(error));
            if (result instanceof ABCError) {
                //下载失败了
                this.setState({
                    downloading: false,
                    downloadError: result.detailError,
                });
                return;
            } else if (result.statusCode != 200) {
                await FileUtils.deleteFile(tmpFile); //删除临时文件
                return;
            } else {
                //下载成功后再移动到最终目录
                await FileUtils.mv(tmpFile, path);
                this.setState({
                    downloading: false,
                    downloadError: null,
                });
            }
        }

        this._audioView?.play(path);
    }
}
