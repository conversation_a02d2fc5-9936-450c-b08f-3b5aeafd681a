/**
 * 成都字节星球科技公司
 *
 * Created by he<PERSON><PERSON> on 2020-03-21
 *
 * @description
 *
 */

import { ABCApiNetwork } from "../../net";
import {
    ConsultationFormItem,
    ConsultationQuestion,
    OutpatientHistory,
    OutpatientInvoiceDetail,
    OutpatientOrderItem,
    PrescriptionAttachment,
} from "./outpatient-beans";
import { fromJsonToDate, JsonMapper, JsonProperty } from "../../common-base-module/json-mapper/json-mapper";
import { userCenter } from "../../user-center";
import {
    OutpatientTemplateType,
    PrescriptionTemplate,
    PrescriptionTemplateInfo,
    PrescriptionTemplatesFolderTrees,
    PrescriptionTemplateType,
    SearchTemplateResult,
} from "./prescription-template-bean";
import { OutpatientUtils } from "../utils/outpatient-utils";
import { Subject } from "rxjs";
import _ from "lodash";
import { StringUtils } from "../../base-ui/utils/string-utils";
import { ApiMixService } from "../../data/ApiMixService";
import { ChargeInvoiceDetailData } from "../../charge/data/charge-beans";
import { PharmacyType } from "../../charge/data/charge-bean-air-pharmacy";
import {
    AntibioticEnum,
    ChineseMedicineSpecType,
    GoodsTypeId,
    MedicalRecord,
    Patient,
    PrescriptionFormLogisticsTraceRsp,
    SupervisionSccaDoctorCaInfoRsp,
} from "../../base-business/data/beans";
import { RegistrationFormItem } from "../../registration/data/bean";
import { ignore } from "../../common-base-module/global";
import { UniqueKey } from "../../base-ui";
import { Range } from "../../base-ui/utils/value-holder";

export class OutpatientOrderSearchResult {
    keyword?: string;

    @JsonProperty({ type: Array, clazz: OutpatientOrderItem })
    result?: Array<OutpatientOrderItem>;

    offset?: number;
    limit?: number;
    totalCount?: number;
    @JsonProperty({ fromJson: fromJsonToDate })
    currentTime?: Date;

    @JsonProperty({ ignore: true })
    fromDraft = false; //本字段为本地，表示结果来自于本地草稿
}

export class DoctorAdviseItem {
    content?: string;
    sort?: number;
}

class DoctorAdviseListRsp {
    @JsonProperty({ type: Array, clazz: DoctorAdviseItem })
    list?: DoctorAdviseItem[];
}

export class SaveTemplateReq {
    name?: string;
    parentId?: string;
    category?: number;
    effect?: string;
    majorDisease?: string;
    @JsonProperty({ type: PrescriptionTemplateInfo })
    detail?: PrescriptionTemplateInfo;
}

class PastHistoryQueryRsp {
    pastHistory?: string;
    patientId?: string;
}

export class GetOutpatientHistoryListRsp {
    offset?: number;
    limit?: number;
    totalCount?: number;

    @JsonProperty({ type: Array, clazz: OutpatientHistory })
    result?: OutpatientHistory[];
}

class OutpatientDataEvent {}

class PostCreateOnlineDraftRsp {
    chainId?: string;
    clinicId?: string;
    copywriterId?: string;
    departmentId?: string;
    doctorId?: string;
    id?: string;
    // patient?: Patient;
    // prescriptionAttachments?: [];
    // prescriptionForms?: [];
    // productForms?: [];
    // registrationFee: number;
    // status?: 0;
    // type?: 0;
}
export enum OutpatientLimitType {
    // 3个月
    withTheLastThreeMonths = 10,
    // 2个月
    withTheLastTwoMonths = 9,
    // 3周
    withTheLastThreeWeeks = 8,
    // 2天内
    withTheLastTwoDays = 6,
    //     当天内
    withTheDay = 5,
    //     3天内
    withTheLastThreeDays = 4,
    //     7天内
    withTheLastSevenDays = 3,
    //     2周内
    withTheLastFourteenDays = 7,
    //     1月内
    withTheLastOneMonth = 1,
    //     任何时间
    anyTime = 0,
}

export interface OutpatientCalcSettings {
    infusion: number;
    nebulizer: number;
    quickDiagnosis: number; // 是否支持快速接诊
    chinesePrescriptionSupportMix?: number; // 中药处方是否支持混开 0不允许； 1允许
    prescriptionUpdateLimit?: OutpatientLimitType; // 医生修改病历（已完诊的门诊单，允许在xx时间修改病历）
    medicalRecordUpdateLimit?: OutpatientLimitType; // 医生新增处方（已完诊的门诊单，允许在xx时间继续新增诊疗项目、处方医嘱）
}
export class AntimicrobialDrugRuleItem {
    restrictedLevel?: AntibioticEnum;
    availableTitles?: string[];
    availableBusiness?: number[];
}
export class AntimicrobialDrugConfig {
    enable?: number;
    rules?: AntimicrobialDrugRuleItem[];
    get isOpenAntimicrobialDrug(): boolean {
        return this.enable === 1;
    }

    /**
     * 检查医生可以使用指定的抗菌药类型
     * @param practiceInfo 医生职称信息
     * @param business 当前业务类型 - 0门诊 1住院
     */
    checkDoctorUseAntimicrobialTypes(practiceInfo?: PracticeInfoItem[], business = 0): AntibioticEnum[] | undefined {
        if (!this.isOpenAntimicrobialDrug) return undefined;
        const restrictedLevelList: AntibioticEnum[] = [];
        this.rules
            ?.filter((ruleItem) => ruleItem.availableBusiness?.includes(business))
            .forEach((ruleItem) => {
                if (
                    ruleItem.availableTitles?.some((ruleItemTitle) =>
                        practiceInfo?.some((practiceInfoItem) => practiceInfoItem.title === ruleItemTitle)
                    )
                ) {
                    restrictedLevelList.push(ruleItem.restrictedLevel!);
                }
            });
        return restrictedLevelList;
    }
}

class GetAntimicrobialDrugConfigRsp {
    @JsonProperty({ type: AntimicrobialDrugConfig })
    antimicrobialDrugManagement?: AntimicrobialDrugConfig;
}

class AssistScope {
    id?: string;
    name?: string;
}
class AssistFor {
    isAll?: boolean; // 是否包含所有医生
    @JsonProperty({ type: Array, clazz: AssistScope })
    scope?: AssistScope[];
}
export class ClinicEmployeeInfo {
    @JsonProperty({ type: AssistFor })
    assistFor?: AssistFor;
    chainId?: string;
    clinicId?: string;
    hisType?: number;
    id?: number;
    isAdmin?: boolean;
    isDoctor?: number;
    moduleIds?: string;
}
export class PracticeInfoItem {
    type?: string;
    title?: string;
    get typeValue(): string | undefined {
        return this.type?.split("|")[0];
    }
    get typeStr(): string | undefined {
        return this.type?.split("|")[1];
    }
    get titleValue(): string | undefined {
        return this.title?.split("|")[0];
    }
    get titleStr(): string | undefined {
        return this.title?.split("|")[1];
    }
}
export class ChainEmployeeInfo {
    birthday?: string;
    certNo?: string;
    certType?: string;
    chainId?: string;
    chongqingExtend?: {};
    clinicPracticeBeginDate?: string;
    clinicPracticeEndDate?: string;
    code?: string;
    credentials?: string;
    credentialsRegisteredDate?: string;
    employeeId?: string;
    goodAt?: string;
    introduction?: number;
    isFamilyDoctor?: number;
    isMultiPointPractice?: number;
    nation?: string;
    position?: string;
    practiceBeginDate?: string;
    practiceEndDate?: string;
    practiceCertCode?: string;
    practiceImgUrl?: number;
    @JsonProperty({ type: Array, clazz: PracticeInfoItem })
    practiceInfo?: PracticeInfoItem[];
    practicePlace?: object;
    practiceRegisteredDate?: string;
    practiceScope?: string;
    prPrivilege?: string;
    qrcode?: string;
    sex?: string;
    specialPrPrivilege?: [];
}
export class ClinicEmployeesInfo {
    @JsonProperty({ name: "boundWeçchat" })
    boundWechat?: number;
    @JsonProperty({ type: ChainEmployeeInfo })
    chainInfo?: ChainEmployeeInfo;
    @JsonProperty({ type: ClinicEmployeeInfo })
    clinicInfo?: ClinicEmployeeInfo;
    handSign?: string;
    hasPassword?: number;
    headImgUrl?: string;
    id?: string;
    mobile?: string;
    name?: string;
    needUpdatePassword?: number;
    tags?: AssistScope[];
    themeConfig?: string;
    wechatNickName?: string;
    wechatSubscribe?: number;
}

export class ReferenceRangeRef {
    max?: string;
    min?: string;
}
export class InspectionItem {
    chainId?: string;
    clinicId?: string;
    enName?: string;
    goodsId?: string;
    id?: string;
    itemCode?: string;
    name?: string;
    @JsonProperty({ clazz: ReferenceRangeRef })
    ref?: ReferenceRangeRef;
    resultDisplayScale?: number;
    type?: number; //检验类型（1--数值，2--文本，3--阴阳型）
    unit?: string;
    value?: number | string;
}
export class ExaminationSheetReport {
    advice?: string;
    imageFiles?: [];
    method?: string;
    resultInfo?: string;
    videoDescription?: string;
}
export class ExaminationAttachments {
    fileName?: string;
    fileSize?: number;
    hash?: string;
    imgLoading?: boolean;
    percentage?: number;
    sort?: number;
    url?: string;
}

export class ExaminationInfo {
    attachments?: ExaminationAttachments[]; //报告附件
    barCode?: number;
    canExecute?: number;
    chargeFormItemId?: string;
    chargeFormItemStatus?: number;
    chargeSheetId?: string;
    chargeSheetType?: number;
    checkerId?: string;
    checkerName?: string;
    clinicPrintName?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    created?: Date; // 开单时间
    departmentId?: string;
    departmentName?: string; // 科室名称
    deviceData?: string;
    deviceName?: string;
    deviceStatus?: number;
    diagnosis?: string;
    doctorId?: string;
    doctorName?: string; // 医生名称
    @JsonProperty({ clazz: ExaminationSheetReport })
    examinationSheetReport?: ExaminationSheetReport; //老版本检查报告相关信息
    extendDiagnosisInfos?: [];
    id?: string;
    itemsValue?: any; // table-form数据
    innerFlag?: number;
    patient?: Patient;
    lastModifiedMillsTime?: string;
    lastModifiedTime?: string;
    modifierName?: string;
    name?: string;
    orderNo?: string; // 检查编号
    patientOrderNumber?: number; // 门诊号
    outpatientFormItemId?: string;
    relationPatientOrderId?: string;
    remark?: string; // 诊断意见
    reportTime?: string;
    sampleType?: string; //样本类型
    status?: number; // 检验状态；0：待检；1：已检；2：已退
    testTime?: string;
    testerId?: string;
    testerName?: string; // 开单人
    @JsonProperty({ type: Array, clazz: InspectionItem })
    items?: InspectionItem[]; //检验报告列表
    @JsonProperty({ clazz: RegistrationFormItem })
    registrationFormItem?: RegistrationFormItem; //就诊关联对象
}

export class PutOutpatientInvoiceStatusRsp {
    id?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    currentTime?: Date;
    @JsonProperty({ fromJson: fromJsonToDate })
    statusLastModified?: Date;
    status?: number; //OutpatientInvoiceStatus,
    statusName?: string;
}

export class OutpatientAgent {
    static changeObserver = new Subject<OutpatientDataEvent>();

    static doctorAdviseTemplateList?: Array<DoctorAdviseItem>;

    static async getOutpatientList(params: {
        pageSize?: number;
        offset?: number;
        limit?: number;
        keyword?: string;
        beginDate?: Date;
        endDate?: Date;
        tab?: number;
    }): Promise<OutpatientOrderSearchResult> {
        // limit 30
        const { keyword, offset, pageSize, beginDate, endDate, tab } = params;
        return ABCApiNetwork.get("outpatients", {
            clazz: OutpatientOrderSearchResult,
            queryParameters: {
                keyword: keyword ?? "",
                offset: (offset ?? 0).toString(),
                limit: (pageSize ?? 20).toString(),
                beginDate: beginDate?.format("yyyy-MM-dd"),
                endDate: endDate?.format("yyyy-MM-dd"),
                tab: tab?.toString() ?? "",
            },
            clearUndefined: true,
        });
    }

    /**
     * 根据门诊id查询门诊单
     * @param outpatientId 门诊单ID
     */
    static async getOutpatientInvoiceDetail(outpatientId: string): Promise<OutpatientInvoiceDetail> {
        return ABCApiNetwork.get(`outpatients/${outpatientId}`, {
            clazz: OutpatientInvoiceDetail,
        }).then((rsp) => {
            rsp.registrationFee = rsp.registrationFee ?? 0;
            rsp.prescriptionChineseForms?.forEach((item) => {
                item.keyId = item.keyId ?? UniqueKey();
            });
            return rsp;
        });
    }

    /**
     * 将门诊单的结构转换成收费单的结构
     * @param patientOrderId 门诊单id
     */
    static async changePatientToOrderCopy(patientOrderId: string): Promise<ChargeInvoiceDetailData> {
        return ABCApiNetwork.get(`charge/patientorder/${patientOrderId}/copy`, {
            clazz: ChargeInvoiceDetailData,
        });
    }

    /**
     * 根据会话id查询门诊单
     * @param conversionId
     */
    static async getOutpatientDetailByConversionId(conversionId: string): Promise<OutpatientInvoiceDetail> {
        return ABCApiNetwork.get(`outpatients/consultation/conversation/${conversionId}/outpatient`, { clazz: OutpatientInvoiceDetail });
    }

    /**
     * 获取患者的就诊历史列表
     * @param patientId 患者ID
     * @param offset
     * @param limit
     */
    static async getOutpatientHistoryList(patientId: string, offset = 0, limit = 40): Promise<GetOutpatientHistoryListRsp | undefined> {
        if (patientId == "00000000000000000000000000000000") return undefined; // 当患者Id为00000000000000000000000000000000时不调用接口
        return await ABCApiNetwork.get(`outpatients/history/${patientId}`, {
            queryParameters: { offset: offset, limit },
            clazz: GetOutpatientHistoryListRsp,
        });
    }

    /**
     * 获取医嘱信息列表
     */
    static async getDoctorAdviseTemplate(): Promise<Array<DoctorAdviseItem>> {
        return ABCApiNetwork.get<DoctorAdviseListRsp>("outpatients/doctoradvice/templates", { clazz: DoctorAdviseListRsp }).then((res) => {
            OutpatientAgent.doctorAdviseTemplateList = res.list;
            return res.list!;
        });
    }

    /**
     * 门诊费用计算
     * @param data OutpatientInvoiceDetail
     */
    static async chargeCalculate(data: OutpatientInvoiceDetail): Promise<OutpatientInvoiceDetail> {
        if (!data.clinicId) {
            const clinic = userCenter.clinic;
            data.clinicId = clinic?.clinicId;
        }
        return ABCApiNetwork.post("outpatients/calculate/sheet", {
            clazz: OutpatientInvoiceDetail,
            body: data,
        });
    }

    /**
     * 搜索处方模板
     * @param options
     * @param options.types
     * @param options.subTypes
     * @param options.keyword
     * @param options.offset
     * @param options.limit
     */
    static async searchTemplate(options: {
        types?: Array<number>;
        subTypes?: Array<number>;
        keyword?: string;
        offset?: number;
        limit?: number;
    }): Promise<SearchTemplateResult> {
        const { types, subTypes, keyword, offset, limit } = options;
        const rsp: SearchTemplateResult = await ABCApiNetwork.get("outpatients/prescriptions/templates", {
            queryParameters: {
                type: types?.join(",") ?? "",
                subType: subTypes?.join(",") ?? "",
                keyword: keyword ?? "",
                offset: offset?.toString() ?? "0",
                limit: limit?.toString() ?? "30",
            },
            clazz: SearchTemplateResult,
        });

        rsp.list = rsp.list ?? [];
        return rsp;
    }

    /**
     * 模版保存
     * @param detailData 模板详情
     * @param name 模板名称
     * @param parentId
     * @param type
     * @param category
     * @param effect
     * @param majorDisease
     */
    static async saveTemplate(
        detailData: PrescriptionTemplateInfo,
        name: string,
        parentId: string,
        type = OutpatientTemplateType.prescription,
        category = 2,
        effect = "",
        majorDisease = ""
    ): Promise<PrescriptionTemplate> {
        const prInfo = _.cloneDeep(detailData);

        //清除模板上的收费等标记
        OutpatientUtils.clearPrescriptionFlags(prInfo.prescriptionChineseForms ?? []);
        OutpatientUtils.clearPrescriptionFlags(prInfo.prescriptionWesternForms ?? []);
        OutpatientUtils.clearPrescriptionFlags(prInfo.prescriptionInfusionForms ?? []);
        OutpatientUtils.clearPrescriptionFlags(prInfo.prescriptionExternalForms ?? []);

        //空中药房、虚拟药房需要强制转换为本地药房类型
        for (const form of prInfo.prescriptionChineseForms ?? []) {
            if (form.pharmacyType == PharmacyType.air || form.pharmacyType == PharmacyType.virtual) {
                form.pharmacyType = PharmacyType.normal;
            }

            const { eqConversionRule, pharmacyType } = form;
            const isChinesePiece = eqConversionRule === ChineseMedicineSpecType.chinesePiece;
            // 本地药房 按等效饮片开方 中药颗粒&单位是g/克，需要将 eqUnitCount 转化为 unitCount
            if (pharmacyType === PharmacyType.normal && isChinesePiece && form.prescriptionFormItems?.length) {
                form.prescriptionFormItems.forEach((item) => {
                    const { typeId, pieceUnit } = item.goodsInfo || {};
                    const isMedicineChineseGranule = GoodsTypeId.medicineChineseGranule === typeId;
                    // 中药颗粒 & 单位为g/克
                    const isChineseGranuleAndUnitG = isMedicineChineseGranule && (pieceUnit === "g" || pieceUnit === "克");
                    if (userCenter.enableEqCoefficient && isChineseGranuleAndUnitG) {
                        item.unitCount = item.eqUnitCount;
                    }
                });
            }
        }

        const req = JsonMapper.deserialize(SaveTemplateReq, {
            detail: prInfo,
            name,
            category,
            effect,
            majorDisease,
            parentId,
        });

        return await ABCApiNetwork.post(`outpatients/templates/${type}`, {
            body: req,
        });
    }

    private static createOutpatientInvoiceBody(patientOrderDetail: OutpatientInvoiceDetail): {} {
        //改变diagnosis的传输类型
        const medicalRecord = patientOrderDetail.medicalRecord;
        const isMedicalRecordFilled = !!medicalRecord?.medicalRecordDisplayName ? 1 : 0;
        const diagnosis = JSON.stringify(medicalRecord?.diagnosis?.split(StringUtils.specialComma)?.filter((it) => !!it));
        //包装月经婚育史
        const obstetricalHistory = !!medicalRecord?.obstetricalHistory ? JSON.stringify(medicalRecord?.obstetricalHistory) : "";

        return {
            ...patientOrderDetail,
            isMedicalRecordFilled,
            medicalRecord: {
                ...medicalRecord,
                diagnosis: !!diagnosis?.length ? diagnosis : "",
                obstetricalHistory,
                extendDiagnosisInfos: medicalRecord?.extendDiagnosisInfos?.filter((t) => !!t), //过滤掉空的诊断信息，防止后台报错
            },
            registrationFee: patientOrderDetail?.registrationFee ?? 0, //解决挂号费为null，导致接诊报错问题
            //兜底处理specification没有的情况
            prescriptionChineseForms: patientOrderDetail.prescriptionChineseForms?.map((form) => {
                form.specification = form.specification || "中药饮片";
                return form;
            }),
        };
    }

    /**
     * 创建门诊单
     * @param patientOrderDetail
     */
    static async createOutpatientInvoice(patientOrderDetail: OutpatientInvoiceDetail): Promise<OutpatientInvoiceDetail> {
        const requestBody = this.createOutpatientInvoiceBody(patientOrderDetail);
        const rsp = await ABCApiNetwork.post("outpatients", {
            body: requestBody,
            clazz: OutpatientInvoiceDetail,
        });
        OutpatientAgent.changeObserver.next(new OutpatientDataEvent());
        return rsp;
    }

    /**
     * 修改门诊单
     * @param patientOrderDetail
     * @param callback
     */
    static async updateOutpatientInvoice(
        patientOrderDetail: OutpatientInvoiceDetail,
        callback?: () => Promise<void>
    ): Promise<OutpatientInvoiceDetail> {
        const requestBody = this.createOutpatientInvoiceBody(patientOrderDetail);
        const rsp = await ABCApiNetwork.put(`outpatients/${patientOrderDetail.id}`, {
            body: requestBody,
            clazz: OutpatientInvoiceDetail,
        });
        //预诊后需要删除草稿
        !!callback && (await callback());
        OutpatientAgent.changeObserver.next(new OutpatientDataEvent());
        return rsp;
    }

    /**
     * 上传拍方照片
     * @param id 门诊单id
     * @param images 要上传的图片
     * @param fromDoctor true 医生发起, false 医助
     * @param departmentId
     */
    static async updateAttachments(
        id: string,
        images: PrescriptionAttachment[],
        fromDoctor: boolean,
        departmentId: string
    ): Promise<OutpatientInvoiceDetail> {
        images.forEach((item, index) => (item.sort = index));
        return ABCApiNetwork.put(`outpatients/${id}/attachments`, {
            body: {
                prescriptionAttachments: images,
                outpatientSource: fromDoctor ? 0 : 1,
                departmentId,
            },

            clazz: OutpatientInvoiceDetail,
        });
    }

    /**
     * 结束咨询单
     * @param outpatientId 门诊单id
     */
    static async finishConsultation(outpatientId: string): Promise<string> {
        return await ABCApiNetwork.put(`outpatients/${outpatientId}/consultation/finish`, {
            body: {
                data: outpatientId,
            },
        });
    }

    /**
     * 获取咨询单中问诊单
     * @param id //问诊单id
     */
    static async getConsultationQuestion(id: string): Promise<ConsultationQuestion> {
        return await ABCApiNetwork.get(`outpatients/consultation/question/${id}`, {
            clazz: ConsultationQuestion,
        });
    }

    /**
     *  获取问诊单列表
     */
    static async getConsultationQuestionForms(sex: string): Promise<ConsultationFormItem[]> {
        const rsp: any = await ABCApiNetwork.get(`outpatients/consultation/question/forms`, {
            queryParameters: {
                sex: sex,
            },
        });
        return rsp["rows"];
    }

    /**
     * 医生发送问诊单
     */
    static async sendConsultationQuestion(consultationId: string, formId: string): Promise<ConsultationQuestion> {
        return await ABCApiNetwork.post(`outpatients/consultation/${consultationId}/question/${formId}/send`, {
            clazz: ConsultationQuestion,
        });
    }

    /**
     *设置治疗方案详情可见性
     */
    static async updateConsultationOutpatientVisible(outpatientId: string, planVisible: boolean): Promise<boolean> {
        const rsp: any = await ABCApiNetwork.put(`outpatients/${outpatientId}/consultation/planvisible`, {
            body: { planVisible: planVisible ? 1 : 0 },
        });

        return rsp["planVisible"] == 1;
    }

    /**
     * 获取患史
     * @param patientId
     */
    static async getPastHistory(patientId: string): Promise<string> {
        const rsp: PastHistoryQueryRsp = await ABCApiNetwork.get(`patients/${patientId}/pastHistory`, { clazz: PastHistoryQueryRsp });
        return rsp.pastHistory!;
    }

    /**
     * 在线问诊医生赠送时长
     * @param consultationId 咨询单id
     * @param promotionServiceTime 赠送时长
     * @return 累计赠送分钟数
     */
    static async consultationPromotion(consultationId: string, promotionServiceTime: number): Promise<number> {
        const rsp: {
            promotionServiceTime: number;
        } = await ABCApiNetwork.put(`outpatients/consultation/${consultationId}/promotion`, {
            body: { promotionServiceTime: promotionServiceTime },
        });

        return rsp.promotionServiceTime;
    }

    /**
     * 新版处方模版的文件树列表
     * @param type
     * @param category
     */
    static async templatesFolderTree(type: string, category = 2): Promise<PrescriptionTemplatesFolderTrees> {
        return await ABCApiNetwork.get(`outpatients/templates/${type}/catalogue/folder-tree`, {
            queryParameters: { category },
            clazz: PrescriptionTemplatesFolderTrees,
        });
    }

    static async templatesCatalogueList(
        type: string,
        params: { category?: number; parentId?: string }
    ): Promise<PrescriptionTemplatesFolderTrees> {
        return await ABCApiNetwork.get(`outpatients/templates/${type}/catalogue/list`, {
            queryParameters: { ...params },
            clazz: PrescriptionTemplatesFolderTrees,
        });
    }

    static async getTemplateDetail(type: string, id: string): Promise<PrescriptionTemplate> {
        return await ABCApiNetwork.get(`outpatients/templates/${type}/${id}`, {
            clazz: PrescriptionTemplate,
        });
    }

    static async templateApply(type: string, id: string): Promise<void> {
        return await ABCApiNetwork.put(`outpatients/templates/${type}/${id}/apply`, {});
    }

    /**
     * 新搜索处方模板
     * @param type
     * @param options
     * @param options.types
     * @param options.subTypes
     * @param options.keyword
     * @param options.offset
     * @param options.limit
     */
    static async searchTemplateV2(
        type: string,
        options: {
            keyword?: string;
            offset?: number;
            limit?: number;
            scope?: number;
        }
    ): Promise<SearchTemplateResult> {
        const { keyword, offset, limit, scope } = options;
        const rsp: SearchTemplateResult = await ABCApiNetwork.get(`outpatients/templates/${type}/catalogue/search`, {
            queryParameters: {
                keyword: keyword ?? "",
                offset: offset?.toString() ?? "0",
                limit: limit?.toString() ?? "30",
                scope: scope ?? PrescriptionTemplateType.personal,
            },
            clazz: SearchTemplateResult,
        });

        rsp.list = rsp.list ?? [];
        return rsp;
    }

    /**
     * 创建在线草稿
     * @param params
     * @param patientId   口腔诊所特有，必须在有患者的情况下才进行同步
     */
    static async postCreateOnlineDraft(params: { type: number; patientId?: string }): Promise<PostCreateOnlineDraftRsp> {
        return ABCApiNetwork.post("outpatients/draft", {
            body: params,
            clazz: PostCreateOnlineDraftRsp,
        }).then((rsp) => {
            this.changeObserver.next();
            return rsp;
        });
    }

    /**
     * 创建在线草稿
     */
    static async deleteOnlineDraft(id: string, type: number): Promise<void> {
        return ABCApiNetwork.delete(`outpatients/draft/${id}`, {
            queryParameters: { type },
        })
            .then(() => {
                this.changeObserver.next();
            })
            .catch(() => {
                this.changeObserver.next();
            });
    }

    /**
     * 获取注射雾化计算规则
     */
    private static _localUsageConfig?: OutpatientCalcSettings;

    static async getUsageConfig(cache = true): Promise<OutpatientCalcSettings> {
        if (cache && !!this._localUsageConfig) return this._localUsageConfig;
        return ApiMixService.getProperty<{ settings: OutpatientCalcSettings }>("clinic", "", "clinicBasic.outpatient.settings")
            .then((rsp) => {
                this._localUsageConfig = rsp.settings;
                return this._localUsageConfig;
            })
            .catch(() => ({
                infusion: 0,
                nebulizer: 1,
                quickDiagnosis: 1,
                chinesePrescriptionSupportMix: 1,
            }));
    }
    private static _localAntimicrobialDrugConfig?: AntimicrobialDrugConfig;
    static async getAntimicrobialDrugConfig(cache = true): Promise<AntimicrobialDrugConfig | undefined> {
        if (cache && !!this._localAntimicrobialDrugConfig) return this._localAntimicrobialDrugConfig;
        return ApiMixService.getProperty("clinic", "", "antimicrobialDrugManagement", GetAntimicrobialDrugConfigRsp)
            .then((rsp) => {
                this._localAntimicrobialDrugConfig = rsp.antimicrobialDrugManagement;
                return this._localAntimicrobialDrugConfig;
            })
            .catchIgnore();
    }

    /**
     * 获取当前登录人所有信息
     * @param employeeId
     */
    static async getClinicEmployeesList(employeeId: string): Promise<ClinicEmployeesInfo> {
        return ABCApiNetwork.get(`clinics/employees/${employeeId}`, { clazz: ClinicEmployeesInfo });
    }

    static async putOutpatientAdjustmentPrice(data: OutpatientInvoiceDetail): Promise<OutpatientInvoiceDetail> {
        if (!data.clinicId) {
            const clinic = userCenter.clinic;
            data.clinicId = clinic?.clinicId;
        }
        const { medicalRecord, ...others } = data;
        ignore(medicalRecord);
        return ABCApiNetwork.put(`outpatients/${data.id}/adjustment-price`, {
            clazz: OutpatientInvoiceDetail,
            body: others,
        });
    }

    /**
     * 根据examinationSheetId查询眼科检查报告详情
     * @param examinationSheetId
     */
    static async queryExaminations(examinationSheetId?: string): Promise<ExaminationInfo> {
        return ABCApiNetwork.get(`examinations/${examinationSheetId}`, { clazz: ExaminationInfo });
    }

    /**
     * 获取检查报告版本，判断后续使用前端组件
     * @param examinationSheetId
     */
    static async fetchExaminationReportVersion(examinationSheetId?: string): Promise<{ reportVersion?: number }> {
        return ABCApiNetwork.get(`examinations/report-version/${examinationSheetId}`);
    }

    /**
     * 门诊获取物流信息
     * @param formId
     */
    static async getPrescriptionFormLogisticsTrace(formId: string): Promise<PrescriptionFormLogisticsTraceRsp | undefined> {
        if (!formId) return Promise.resolve(undefined);
        return ABCApiNetwork.get(`outpatients/prescription/form/${formId}/logistics-trace/`, { clazz: PrescriptionFormLogisticsTraceRsp });
    }

    /**
     * 修改门诊单状态
     */
    static async putOutpatientInvoiceStatus(params: {
        formId: string;
        status: number;
        consultantId?: string;
        departmentId?: string;
        doctorId?: string;
        departmentName?: string;
        doctorName?: string;
        id?: string;
        reserveDate?: string; //预约日期
        reserveTime?: Range<string>;
        revisitStatus?: number; //初复诊标识状态；1：初诊；2：复诊
        pay?: {
            fee?: number; //挂号费
            memberId?: string;
            payMode?: number; //支付方式
            paySubMode?: number; //支付子方式
            payType?: number; //支付类型
            receivable?: number; //实收费用
            refundOriginalPayMode?: number; //退费方式
        };
        patientOrderId?: string;
    }): Promise<PutOutpatientInvoiceStatusRsp> {
        const {
            formId,
            status,
            consultantId,
            departmentId,
            doctorId,
            doctorName,
            departmentName,
            reserveDate,
            id,
            reserveTime,
            revisitStatus,
            patientOrderId,
        } = params;
        return ABCApiNetwork.put(`outpatients/${formId}/status`, {
            body: {
                status,
                consultantId,
                departmentId,
                doctorId,
                reserveDate,
                id,
                doctorName,
                departmentName,
                reserveTime,
                revisitStatus,
                patientOrderId,
            },
            clazz: PutOutpatientInvoiceStatusRsp,
            clearUndefined: true,
        });
    }

    /**
     * 医生证书查询 （可查询签名状态：0 已绑定、1 失效、undefined 未绑定）
     * @param doctorId 医生id
     * @param isValid 是否只查询有效签名信息 (true 会查询医生在连锁下的任意有效签名信息 )
     */
    static getSupervisionSccaDoctorCaInfo(doctorId: string, isValid: boolean): Promise<SupervisionSccaDoctorCaInfoRsp | undefined> {
        return ABCApiNetwork.get(`supervision/scca/doctor-ca-info`, {
            queryParameters: { doctorId: doctorId, isValid: isValid },
            clazz: SupervisionSccaDoctorCaInfoRsp,
        });
    }

    /**
     * 根据patientOrderId获取门诊就诊单
     * @param patientOrderId
     */
    static getPatientOrdersExtendByPatientOrderId(patientOrderId: string): Promise<OutpatientInvoiceDetail> {
        return ABCApiNetwork.get(`outpatients/patientorders/${patientOrderId}/extend`, { clazz: OutpatientInvoiceDetail });
    }

    /**
     * 保存门诊病历单
     * @param params
     * @returns
     */
    static async saveMedicalRecord(params: { medicalRecordId: string; medicalRecord: MedicalRecord }): Promise<MedicalRecord> {
        const { medicalRecordId, medicalRecord } = params;
        let obstetricalHistory = "";
        if (!medicalRecord.obstetricalHistory?.length) {
            obstetricalHistory = "";
        } else if (medicalRecord.obstetricalHistory instanceof Object) {
            obstetricalHistory = JSON.stringify(medicalRecord.obstetricalHistory);
        } else {
            obstetricalHistory = JSON.parse(medicalRecord.obstetricalHistory);
        }
        const req = {
            ...medicalRecord,
            obstetricalHistory: obstetricalHistory,
        };
        return ABCApiNetwork.put(`outpatients/medical-record/${medicalRecordId}`, {
            body: req,
            clazz: MedicalRecord,
        });
    }
}
