/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/7/9
 *
 * @description
 */
import React from "react";
import { ClickableProps, Style, Text, View } from "@hippy/react";
import { BaseComponent } from "../base-ui/base-component";
import { Colors, FontSizes, Sizes, TextStyles } from "../theme";
import { AssetImageView } from "../base-ui/views/asset-image-view";
import { SizedBox } from "../base-ui";
import { userCenter } from "../user-center";
import { ABCNavigator } from "../base-ui/views/abc-navigator";
import { DebugPage } from "../debug/debug-page";
import { LoginPasswordPage } from "./login-password-page";
import IconFontView from "../base-ui/iconfont/iconfont-view";
import UiUtils, { pxToDp } from "../base-ui/utils/ui-utils";
import { LicenseAgreementDialog } from "./license-agreement-dialog";
import { AbcView } from "../base-ui/views/abc-view";
import { wxLoginHelper } from "./wx-login-helper";
import { LicenseCheckView } from "./views/license-check-view";
import { DialogIndex, showQueryDialog } from "../base-ui/dialog/dialog-builder";
import WillPopListener from "../base-ui/views/will-pop-listener";
import { AbcPlatformMix } from "../base-business/native-modules/abc-platform-mix";

interface LoginPageProps {
    jumpUrl?: string; //登录成功后的跳转页面
}

interface LoginPageStates {
    licenseAgreeStatus: boolean;
}

export class LoginPage extends BaseComponent<LoginPageProps, LoginPageStates> {
    constructor(props: LoginPageProps) {
        super(props);
        this.state = {
            licenseAgreeStatus: false,
        };
    }

    componentDidMount(): void {
        wxLoginHelper.wxLoginStatusObserver
            .subscribe(() => {
                this.setState({});
            })
            .addToDisposableBag(this);

        if (!LicenseAgreementDialog.hasAgreeLicense()) {
            setTimeout(() => {
                LicenseAgreementDialog.show().then(() => {
                    this.setState({});
                });
            }, 0);
            return;
        }

        // 如果用户已经同意隐私协议，确保微信登录状态已初始化
        this.ensureWxLoginInitialized();
    }

    // 确保微信登录状态已初始化
    private ensureWxLoginInitialized() {
        // 如果微信登录没有启用，尝试手动触发初始化
        if (!wxLoginHelper.isWxLoginEnable()) {
            wxLoginHelper.initWxLoginManually();
        }
    }

    public render(): JSX.Element {
        return (
            <View
                accessibilityLabel={"abc-page-loginPage"}
                collapsable={false}
                style={{
                    flex: 1,
                    backgroundColor: Colors.mainColor,
                    alignItems: "center",
                }}
            >
                <WillPopListener
                    onWillPop={() => {
                        AbcPlatformMix.exitApp();
                    }}
                />
                <View style={[{ height: UiUtils.safeStatusHeight() }]} />
                <View style={[{ flex: 140 }]} />
                <View style={{ alignItems: "center", width: pxToDp(212), height: pxToDp(130) }}>
                    <AssetImageView name={"login_circle_sign"} style={{ width: pxToDp(212), height: Sizes.dp94 }} />
                    <AssetImageView
                        name={"login_log_v2"}
                        style={{
                            position: "absolute",
                            width: Sizes.dp96,
                            height: Sizes.dp66,
                            marginTop: Sizes.dp54,
                        }}
                    />
                </View>
                <View style={[{ flex: 47 }]} />

                <Text
                    style={{
                        fontSize: FontSizes.size24,
                        color: Colors.white,
                        letterSpacing: Sizes.dp10,
                        fontWeight: "400",
                    }}
                >
                    {" "}
                    懂医懂药懂经营
                </Text>
                <View style={[{ flex: 45 }]} />

                {LicenseAgreementDialog.hasAgreeLicense() && (
                    <View style={{ flexDirection: "row" }}>
                        {wxLoginHelper.isWxLoginEnable() &&
                            this._renderLoginButton("微信登录", "login_wechat", async () => {
                                this.validateLicenseAgreeStatus().then(() => {
                                    userCenter.loginWithWeChat(this.props.jumpUrl);
                                });
                            })}
                        {wxLoginHelper.isWxLoginEnable() && <SizedBox width={Sizes.dp33} />}
                        {this._renderLoginButton("手机登录", "login_password", async () => {
                            this.validateLicenseAgreeStatus().then(() => {
                                ABCNavigator.navigateToPage(<LoginPasswordPage jumpUrl={this.props.jumpUrl} />);
                            });
                        })}
                    </View>
                )}
                <View style={[{ flex: 260 }]} />
                <LicenseCheckView
                    checked={this.state.licenseAgreeStatus}
                    onChange={(status: boolean) => {
                        this.setState({ licenseAgreeStatus: status });
                    }}
                />
                <View style={[{ flex: 60 }]} />
                <AssetImageView
                    name={"login_wave"}
                    style={{
                        position: "absolute",
                        left: 0,
                        right: 0,
                        bottom: 0,
                        height: Sizes.dp90,
                    }}
                />
            </View>
        );
    }

    private async validateLicenseAgreeStatus(): Promise<void> {
        return new Promise((resolve, reject) => {
            if (this.state.licenseAgreeStatus) {
                resolve();
            } else {
                showQueryDialog("服务协议和隐私政策", <LicenseAgreementDialog />, "同意", "不同意").then((select) => {
                    if (select != DialogIndex.positive) return reject("拒绝");
                    this.setState({ licenseAgreeStatus: true });
                    resolve();
                });
            }
        });
    }

    private _renderLoginButton(title: string, icon: string, onClick: () => void) {
        return (
            <AbcView
                accessibilityLabel={`${title}`}
                collapsable={false}
                style={{
                    width: Sizes.dp118,
                    height: Sizes.dp118,
                    backgroundColor: 0xccffffff,
                    alignItems: "center",
                    justifyContent: "center",
                    borderRadius: Sizes.dp6,
                }}
                onClick={onClick}
                onLongClick={() => {
                    ABCNavigator.navigateToPage(<DebugPage />);
                }}
            >
                <AssetImageView name={icon} style={{ width: Sizes.dp36, height: Sizes.dp36 }} ignoreTheme={false} />
                <SizedBox height={12} />
                <Text style={TextStyles.t16NM}>{title}</Text>
            </AbcView>
        );
    }
}

interface LoginButtonProps extends ClickableProps {
    title?: string;
    style?: Style;
}

export class LoginButton extends BaseComponent<LoginButtonProps> {
    constructor(props: LoginButtonProps) {
        super(props);
    }

    public render(): JSX.Element {
        const { style, title = "登录" } = this.props;
        return (
            <AbcView
                {...this.props}
                style={[
                    {
                        height: Sizes.dp44,
                        backgroundColor: Colors.mainColor,
                        justifyContent: "center",
                        alignItems: "center",
                        borderRadius: Sizes.dp3,
                    },
                    style ?? {},
                ]}
            >
                <Text style={TextStyles.t16NW}>{title}</Text>
            </AbcView>
        );
    }
}

interface LoginWithWeChatButtonProps {
    onClick?(): void;
}

export class LoginWithWeChatButton extends BaseComponent<LoginWithWeChatButtonProps> {
    constructor(props: LoginWithWeChatButtonProps) {
        super(props);
    }

    public componentDidMount(): void {
        wxLoginHelper.wxLoginStatusObserver.subscribe(() => {
            this.setState({});
        });
    }

    public render(): JSX.Element {
        const { onClick } = this.props;
        if (!wxLoginHelper.isWxLoginEnable()) return <View />;

        return (
            <AbcView style={{ flexDirection: "row" }} onClick={onClick}>
                <IconFontView name={"Wechat"} size={Sizes.dp18} color={Colors.mainColor} style={{ marginRight: Sizes.dp8 }} />
                <Text style={TextStyles.t16NT2}>微信一键登录</Text>
            </AbcView>
        );
    }
}
