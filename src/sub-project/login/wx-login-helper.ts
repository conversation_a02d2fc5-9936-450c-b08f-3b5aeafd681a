import { WxApi } from "../base-business/wxapi/wx-api";
import { LicenseAgreementDialog } from "./license-agreement-dialog";
import { DeviceUtils } from "../base-ui/utils/device-utils";
import { BehaviorSubject } from "rxjs";
import { onlinePreferences } from "../base-business/preferences/online-preferences";
import { sharedPreferences } from "../base-business/preferences/shared-preferences";
import { AppInfo } from "../base-business/config/app-info";

/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2022/7/25
 *
 * @description
 * 是否允许微信登录，由于ios应用审核的问题，如果提供微信登录就需要提供apple账号登录，而apple登录对我们系统用处不大，暂时不提供
 * 所以在apple审核期间关闭微信登录
 */

class WxLoginHelper {
    _weChatInstalled = true; //微信是否安装
    _enableWxLogin = DeviceUtils.isAndroid(); //Android默认允许，ios默认关闭后面根据在线配置来打开

    // 是否
    public wxLoginStatusObserver = new BehaviorSubject<boolean>(this.isWxLoginEnable());

    constructor() {
        const kDisableWxLoginIOS = "disableWxLoginIOS";

        // 如果用户已经同意隐私协议，直接初始化微信登录状态
        if (LicenseAgreementDialog.hasAgreeLicense()) {
            this.initWxLoginStatus(kDisableWxLoginIOS);
        }

        //监听隐私是否通过
        LicenseAgreementDialog.sLicenseAgreementObserver.subscribe((agree: boolean) => {
            if (!agree) return;
            this.initWxLoginStatus(kDisableWxLoginIOS);
        });
    }

    private initWxLoginStatus(kDisableWxLoginIOS: string) {
        WxApi.isWeChatInstalled().then((installed) => {
            this._weChatInstalled = installed ?? false;
            this.wxLoginStatusObserver.next(this.isWxLoginEnable());
        });

        DeviceUtils.isIOS() &&
            onlinePreferences
                .getBool(kDisableWxLoginIOS, false) // 强制刷新缓存
                .catch((/*ignore*/) => {
                    const disableWxLoginIOSCache: {
                        version: string;
                        value: boolean;
                    } = sharedPreferences.getObject(kDisableWxLoginIOS);

                    return disableWxLoginIOSCache && disableWxLoginIOSCache.version === AppInfo.appVersion
                        ? disableWxLoginIOSCache.value
                        : true;
                })
                .then((disable) => {
                    this._enableWxLogin = !disable;

                    sharedPreferences.setObject(kDisableWxLoginIOS, {
                        version: AppInfo.appVersion,
                        value: disable,
                    });
                    this.wxLoginStatusObserver.next(this.isWxLoginEnable());
                });
    }

    /**
     * 是否允许微信登录
     */
    isWxLoginEnable(): boolean {
        return LicenseAgreementDialog.hasAgreeLicense() && this._weChatInstalled && this._enableWxLogin;
    }

    /**
     * 手动初始化微信登录状态（用于处理时序问题）
     */
    public initWxLoginManually() {
        const kDisableWxLoginIOS = "disableWxLoginIOS";
        this.initWxLoginStatus(kDisableWxLoginIOS);
    }
}

const wxLoginHelper = new WxLoginHelper();
export { wxLoginHelper };
