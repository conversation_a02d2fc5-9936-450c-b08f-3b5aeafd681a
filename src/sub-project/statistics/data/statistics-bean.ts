/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/7/29
 *
 * @description
 */
import { fromJsonToDate, JsonMapper, JsonProperty } from "../../common-base-module/json-mapper/json-mapper";
import { Range } from "../../base-ui/utils/value-holder";
import { TimeUtils } from "../../common-base-module/utils";
import { GetAnalysisOfRetailPatients, GetManagementAnalysisOfOutpatients, GetManagementRevenueRetailDetailRsp } from "./statistics-agent";
import { OrderActualPriceTrendItem, OrderProductSaleTopItem, OrderSummaryInfo } from "../../order-cloud/data/order-cloud-bean";

import { DINAlternate } from "../../theme/text-styles";
import { Style } from "@hippy/react";
export const DINAlternateFontFamily = { fontFamily: DINAlternate };
export enum FeeTypeId {
    regFee = "fee-5-1",
    consultingFee = "fee-12-0",
    rechargeMemberFee = "-2",

    kanBanTotalAmount = "totalAmount",
    kanBanConsultingFee = "12-1", // 咨询费
    medicineChineseGranuleFee = "1-13", // 中药颗粒
    medicineChinesePieceFee = "1-12", // 中药饮片
}

// 开单业绩
export interface PersonalAchievementItem {
    feeTypes: FeeTypesItem[];
    // 人员
    employeeName: string;
    // 店铺
    clinicName: string;
    amount: number; // 计提金额（实收）
    commissionAmount: number; // 计提金额（原价）

    patientCount: number; // 客量/人数

    // 代录
    isWriter: number;

    [key: string]: string | number | FeeTypesItem[];

    // 成本
    // cost: number;

    // 平均患者人数
    // avgPatientAmount: number;
}

// 费用类型
interface FeeTypesItem {
    id?: string;
    // 费用名称(XX费)
    name: string;
    // 费用金额
    value: number;
    // 对应name key
    field: string;
}

class FeeTypesHeaderItem {
    prop?: string;
    label?: string;
    type?: "money" | string;
    field?: string;
    columnBeMergedIntoKey?: "feeType" | string;
    columnChildren?: FeeTypesHeaderItem[];
}

// 支付方式
interface PayModesItem {
    // 付款方式
    name: string;
    // 支持金额
    value: number;
}
export class PersonalAchievementSummary {
    amount?: number; // 实收金额
    commissionAmount?: number; // 原价金额
    amountText?: string;

    patientCount?: number; // 客量
}

// 今日工作汇总
export class WorkbenchCardInformation {
    firstVisitOutpatientCount?: number; // 首诊患者
    outpatientCount?: number; // 门诊量
    reVisitOutpatientCount?: number; // 复诊患者
}

// 开单业绩
export class PersonalAchievement {
    data?: PersonalAchievementItem[];
    @JsonProperty({ type: Array, clazz: FeeTypesHeaderItem })
    header?: FeeTypesHeaderItem[];

    summary?: PersonalAchievementSummary;
    get displayFeeTypes(): FeeTypesHeaderItem[] {
        const list: FeeTypesHeaderItem[] = [];
        this.header?.forEach((feeType) => {
            const __feeType = PersonalAchievement.createFeeTypesHeaderItem(feeType);
            if (!!__feeType) {
                list.push(...__feeType);
            }
        });

        return list;
    }

    static createFeeTypesHeaderItem(feeType: FeeTypesHeaderItem, beforeSub?: string, key?: string): FeeTypesHeaderItem[] | void {
        if (!!feeType.columnChildren?.length) {
            const list: FeeTypesHeaderItem[] = [];
            let _beforeKeyStr = beforeSub ?? "";
            let _keyStr = key ?? "";
            if (feeType.type == "money") {
                _beforeKeyStr = `${_beforeKeyStr}${feeType.label}`;
                _keyStr = feeType.prop ?? _keyStr;
            }
            feeType.columnChildren.map((_feeType) => {
                const __feeType = PersonalAchievement.createFeeTypesHeaderItem(_feeType, _beforeKeyStr, _keyStr);
                if (!!__feeType) {
                    list.push(...__feeType);
                }
            });
            return list;
        }

        if (feeType.columnBeMergedIntoKey == "feeType") {
            feeType.label = !!beforeSub ? beforeSub : `${beforeSub ?? ""}${feeType.label}`;
            feeType.field = !!key ? key : feeType.prop;
            return [feeType];
        }
    }
}

// 看板挂号费用
export class WorkbenchRegistrationExpense {
    returnedCount?: number; // 退号数量
    returnedFee?: number; // 退号金额
    totalCount?: number; // 总挂号数量
    totalFee?: number; // 总挂号金额
    treatedCount?: number; // 已诊号数量
    treatedFee?: number; // 已诊号金额
    treatingCount?: number; // 未诊号数量
    treatingFee?: number; // 未诊号金额
    summary?: WorkbenchRegistrationExpenseSummary; //挂号费看板汇总
}
export class WorkbenchRegistrationExpenseSummary {
    registrationAmount?: number; // 总挂号金额
}

// 看板开单金额合计 零售人次
export class WorkbenchTotalInvoicedAmountPersonTime {
    executionAmount?: number; // 执行金额
    chargeAmount?: number; // 开单金额 合计
    retailPersonTime?: number; // 零售人次
    outpatientCount?: number; // 门诊量
    prescriptionCount?: number; // 处方量
}

export class WorkbenchDiagnosisTreatmentExpenseHeaderItem {
    label?: string; // 标题
    prop?: string; // 编号
    type?: "money" | string; // 类型
    width?: number;
    field?: string;

    _data?: number; // 前端组合参数
    _feeType?: WorkbenchDiagnosisTreatmentExpenseHeaderItem[]; // 前端自定
}

export interface WorkbenchDiagnosisTreatmentExpenseData {
    [key: string]: number;
}

// 看板诊疗费用
export class WorkbenchDiagnosisTreatmentExpense {
    data?: WorkbenchDiagnosisTreatmentExpenseData[];
    @JsonProperty({ type: Array, clazz: WorkbenchDiagnosisTreatmentExpenseHeaderItem })
    header?: WorkbenchDiagnosisTreatmentExpenseHeaderItem[];

    get displayFeeTypes(): WorkbenchDiagnosisTreatmentExpenseHeaderItem[] {
        const list: WorkbenchDiagnosisTreatmentExpenseHeaderItem[] = [];
        const _dataList = this.data?.[0];
        for (const item of this.header ?? []) {
            for (const i in _dataList) {
                if (item.prop == i) {
                    list.push(
                        JsonMapper.deserialize(WorkbenchDiagnosisTreatmentExpenseHeaderItem, {
                            ...item,
                            _data: _dataList[i],
                        })
                    );
                }
            }
        }

        return list;
    }
}
export class RevenueOverviewDepartmentResp {
    id?: string;
    value?: number; //金额
    name?: string;
}

/**
 * 营收统计-营收概况（汇总数据项）
 * 营业收入/营业收费人次/会员充值金额/会员充值人次/卡项充值金额/卡项充值人次/还款金额/还款人次
 */
export class StatSummaryData {
    totalAmount?: number; //营业收入
    avgTotalAmount?: number; // 营业收入(日均)
    patientCount?: number; // 营业收费人次
    avgPatientCount?: number; // 营业收费人次(日均)
    rechargeMember?: number; // 会员充值金额(本金+赠金)
    rechargePrincipalMember?: number; // 会员充值金额(本金)
    rechargePresentMember?: number; // 会员充值金额(赠金)
    avgRechargeMember?: number; // 会员充值金额(日均)
    rechargeCount?: number; // 会员充值人次
    avgRechargeCount?: number; // 会员充值人次(日均)
    promotionCardRecharge?: number; // 卡项充值金额(本金+赠金)
    promotionCardRechargePrincipal?: number; // 卡项充值金额(本金)
    promotionCardRechargePresent?: number; // 卡项充值金额(赠金)
    promotionCardRechargeCount?: number; // 卡项充值人次
    repaymentAmount?: number; //还款金额
    avgRepaymentAmount?: number; // 还款收费(日均)
    repaymentPatientorderCount?: number; // 还款人次
    avgRepaymentPatientorderCount?: number; // 还款人次(日均)
    feeTypes?: FeeTypesItem[]; // 费用类型
    payModes?: PayModesItem[]; // 支付方式
    avgRechargeOutpatient?: number;
    avgRechargeRetail?: number;
    cost?: number;
    patientCountOutpatient?: number;
    patientCountRetail?: number;
    rechargeOutpatient?: number; //门诊收费
    rechargeRetail?: number; //零售收费
    hospitalAmount?: number; //住院收费
    peChargeAmount?: number; //体检收费
    depositAmount?: number; //住院押金
    preDepositAmount?: number; //住院预交金
    @JsonProperty({ type: Array, clazz: RevenueOverviewDepartmentResp })
    departments?: RevenueOverviewDepartmentResp[]; // 科室收入分析
}

export class StatDailyRevenue {
    @JsonProperty({ fromJson: fromJsonToDate })
    date!: Date;
    value!: number;

    dateTime(): Date {
        return this.date;
    }
}

export class StatMonthRevenue {
    @JsonProperty({ fromJson: fromJsonToDate })
    month!: Date;
    total!: number;

    dayTime(): Date {
        return this.month;
    }
}

export class StatPayMode {
    name?: string;
    field?: string;
    amount?: number;
}

export class StatFeeType {
    amount?: number;
    name?: string;
    field?: string;
}

export enum StatSummaryCardType {
    clinicDailyReport = "1", //门店统计
    outpatientDailyReport = "2", //门诊日报
    treatmentAndPhysiotherapy = "3", //治疗理疗日报
    chargeDailyReport = "4", //收银日报
    dispensingDailyReport = "5", //发药日报
}

export interface KeyAndValue {
    name: string;
    value: string | number;
}

export interface StatValue extends KeyAndValue {
    value: number;
}

export class AchievementDispensingPersonnelDetail {
    clinicId?: string;
    clinicName?: string;
    composeTypeNumber?: number; //套餐内项目发药种数
    dispensedBy?: string;
    dispenser?: string;
    dispensingNumber?: number; //发药单数
    materialTypeNumber?: number; //材料商品发药种数
    granularMedicineList?: StatValue[]; //颗粒
    slicesMedicineList?: StatValue[]; //饮片
    westernMedicineList?: StatValue[]; //成药
    typeNumber?: number; //发药种数

    get __allPrescriptionCount(): number {
        let count = 0;
        count += this.granularMedicineList?.find((item) => item.name == "granularPrescriptionNum")?.value ?? 0;
        count += this.slicesMedicineList?.find((item) => item.name == "slicesPrescriptionNum")?.value ?? 0;
        count += this.westernMedicineList?.find((item) => item.name == "westernPrescriptionNum")?.value ?? 0;
        return count;
    }
}

// 收入趋势统计类型
export enum IncomeTrendStatType {
    recentMonth, //近一月
    recent3Month, //近三月
    recentHalfYear, //近半年
    monthTrend, //月趋势
}

export const incomeTrendLabelDataList = [
    {
        title: "近一月",
        type: IncomeTrendStatType.recentMonth,
        groupBy: "day",
        dateRange: new Range<Date>(TimeUtils.getRecentMonthsFirstDay(1), new Date()),
    },
    {
        title: "近三月",
        type: IncomeTrendStatType.recent3Month,
        groupBy: "day",
        dateRange: new Range<Date>(TimeUtils.getRecentMonthsFirstDay(3), new Date()),
    },
    {
        title: "近半年",
        type: IncomeTrendStatType.recentHalfYear,
        groupBy: "day",
        dateRange: new Range<Date>(TimeUtils.getRecentMonthsFirstDay(6), new Date()),
    },
    {
        title: "月趋势",
        type: IncomeTrendStatType.monthTrend,
        groupBy: "month",
        dateRange: new Range<Date>(TimeUtils.getRecentMonthsFirstDay(12), new Date()),
    },
];

export class ChargeDailyReportDetail {}

/**
 * 开单业绩-筛选门店
 */

export class AchievementScreeningInformationItemEmployee {
    id?: string;
    name?: string;
    namePy?: string;
    namePyFirst?: string;
}

export class AchievementScreeningInformationItemFeeTypeListChild {
    name?: string;
    value?: string;
}

export class AchievementScreeningInformationItemFeeTypeList {
    name?: string;
    value?: string;
    children?: AchievementScreeningInformationItemFeeTypeListChild[];
}

export class AchievementScreeningInformationItemClinic {
    @JsonProperty({ fromJson: fromJsonToDate })
    created?: Date;
    id?: string;
    name?: string;
    namePy?: string;
    namePyFirst?: string;
    shortName?: string;
}

export class AchievementScreeningInformationItemPayMode {
    name?: string;
    value?: string;
    @JsonProperty({ type: Array /*, clazz:*/ })
    children?: AchievementScreeningInformationItemFeeTypeListChild[];
}

export class AchievementScreeningInformationData {
    @JsonProperty({ type: Array, clazz: AchievementScreeningInformationItemEmployee })
    employees?: AchievementScreeningInformationItemEmployee[];
    @JsonProperty({ type: Array, clazz: AchievementScreeningInformationItemFeeTypeList })
    feeTypeList?: AchievementScreeningInformationItemFeeTypeList[];
    @JsonProperty({ type: Array, clazz: AchievementScreeningInformationItemClinic })
    clinics?: AchievementScreeningInformationItemClinic[];
    @JsonProperty({ type: Array, clazz: AchievementScreeningInformationItemPayMode })
    payModes?: AchievementScreeningInformationItemPayMode[];
}

export class EmployeesSelectionItem {
    id?: string;
    name?: string;
    namePy?: string;
    namePyFirst?: string;
}
export class EmployeesSelectionData {
    @JsonProperty({ type: Array, clazz: EmployeesSelectionItem })
    data?: EmployeesSelectionItem[];
}

/**
 * 运营概况
 */
export class OperationOverviewData {
    arrivalPeopleCount?: number; // 到店人数
    patientCompleteCount?: number; // 完诊人数
    patientCompletePayCount?: number; // 完诊付费人数
    retailTraffic?: number; // 零售人数
    clinicNewCustomers?: number; // 门店新客
    clinicOldCustomers?: number; // 门店老客
    firstVisitPatientCount?: number; // 初诊人数
    returnVisitPatientCount?: number; // 复诊人数
    chainClinicNewCustomers?: number; // 连锁新客
    chainClinicOldCustomers?: number; // 连锁老客
}

export enum StatSummaryEmployeeType {
    all = 1,
    charger = 2,
}

/**
 * 营收详情-费用报表（门店、费用类型、支付方式）
 */

export class GetRevenueExpenseReport extends AchievementScreeningInformationData {}

export class ExecuteSummaryItem {
    chainId?: string;
    clinicId?: string;
    clinicName?: string;
    clinicShortName?: string;
    sheetClinicId?: string;
    sheetClinicName?: string;
    sheetClinicShortName?: string;
    executeClinicId?: string;
    executeClinicName?: string;
    executeClinicShortName?: string;
    executorId?: string;
    executorName?: string;
    productId?: string;
    productName?: string;
    productType?: string;
    productTypeText?: string;
    productSubType?: number;
    productSubTypeText?: string;
    type?: string;
    classifyLevel1Id?: string;
    classifyLevel1Name?: string;
    classifyLevel2Id?: string;
    classifyLevel2Name?: string;
    countExecute?: number;
    originalFee?: number;
    receivedFee?: number;
    deductPromotionPrice?: number;
    commissionPrice?: number;
    mergeKey?: string;
    chargeSheetIds?: string;
}

export class ChargeGoodsAchievement {
    airCount?: number;
    amount?: number;
    chainId?: string;
    classifyLevel1?: string;
    classifyLevel2?: string;
    clinicId?: string;
    clinicName?: string;
    commissionAmount?: number;
    copyWriter?: string;
    cost?: number;
    count?: number;
    deductPrice?: number;
    employeeName?: string;
    feeType1?: string;
    feeType2?: string;
    goodsId?: string;
    grade?: string;
    gross?: number;
    isWriter?: number;
    manufacturer?: string;
    name?: string;
    originPrice?: number;
    personnelId?: string;
    profit?: number;
    shortId?: string;
    spec?: string;
    unit?: string;
}

export class PostShareReportStatOperationData {
    dateRange?: Range<Date>;
    statSummaryData?: StatSummaryData;
    statDailyRevenueList?: StatDailyRevenue[];
    incomeTrendStatType?: IncomeTrendStatType; //实收趋势统计类型
    clinicName?: string; //诊所名字
}

export class PostShareReportStatOperationalSituationData {
    dateRange?: Range<Date>;
    customerFlowStatistics?: GetManagementRevenueRetailDetailRsp; // 客流统计
    analysisOutpatients?: GetManagementAnalysisOfOutpatients; // 门诊患者分析
    retailPatientAnalysis?: GetAnalysisOfRetailPatients; // 零售患者分析
    clinicName?: string; //诊所名字
    inpatientWardData?: InpatientWorkReportRsp; //住院病区数据
    physicalPatientAnalysis?: PhysicPatientAnalysisData; // 体检患者分析
}

export class PostShareReportStatPerformanceRevenueData {
    dateRange?: Range<Date>;
    achievement?: PersonalAchievement; // 开单业绩
    clinicName?: string; //诊所名字
    hospitalBillingPerformanceList?: BillingPerformanceData[]; // 医院统计-开单业绩
}
export class OrderCloudPageInfo {
    dateRange?: Range<Date>;
    orderSummaryData?: OrderSummaryInfo;
    orderCloudSaleTopData?: OrderProductSaleTopItem[];
    orderCloudDailyRevenueList?: OrderActualPriceTrendItem[];
    incomeTrendStatType?: IncomeTrendStatType; //实收趋势统计类型
    platformName?: string; //平台
    mallName?: string; // 网店
}

export class PostShareReportData {
    statOperation?: PostShareReportStatOperationData;
    statOperationalSituation?: PostShareReportStatOperationalSituationData;
    statPerformanceRevenue?: PostShareReportStatPerformanceRevenueData;
    orderCloudPageInfo?: OrderCloudPageInfo; //订单云页面信息
}
export interface PerformanceItemViewProps {
    title?: string;
    amount?: number;
    reception?: number;
    hospitalization?: number;
    isShowBottomLine?: boolean;
    isHeaderRow?: boolean;
    onCheckDetail?: () => void;
    style?: Style | Style[];
    textStyle?: Style | Style[];
    amountStyle?: Style | Style[];
}

export class IndicatorItem {
    title?: string;
    value?: string | number;
    list?: Array<{ title?: string; value?: string | number }>;
}
class InpatientWorkReportSummary {
    clinicId?: string;
    clinicName?: string;
    departmentId?: string;
    departmentName?: string;
    wardId?: string;
    wardName?: string; //病区
    admissionHospitalCount?: number; //新入患者
    regularDischargeCount?: number; //普通出院
    deathDischargeCount?: number; //死亡出院
    transferDischargeCount?: number; //转院出院
    totalDischargeCount?: number; //出院合计
    beginInpatientCount?: number; //期初患者
    endInpatientCount?: number; //期末患者
    bedUsedTotal?: number; //实际占用床日数
    bedTotal?: number; //实际开放总床日数
    bedUsageRate?: number;
    bedUsageRateText?: string; //床位使用率
    depositAmount?: number;
    chargeFormAmount?: number; //新增计费
    settleAmount?: number; //出院结算
    admissionHospitalCountSummary?: any;
    regularDischargeCountSummary?: any;
    deathDischargeCountSummary?: any;
    transferDischargeCountSummary?: any;
    totalDischargeCountSummary?: any;
    beginInpatientCountSummary?: any;
    endInpatientCountSummary?: any;
    bedUsedTotalSummary?: any;
    bedTotalSummary?: any;
    bedUsageRateSummary?: any;
    bedUsageRateSummaryText?: string;
    depositAmountSummary?: any;
    chargeFormAmountSummary?: any;
    settleAmountSummary?: any;
    // 住院人次 = 期初 + 新入 - 出院
    get inpatientsCount(): number {
        return (this.beginInpatientCount ?? 0) + (this.admissionHospitalCount ?? 0) - (this.totalDischargeCount ?? 0);
    }
}
export class InpatientWorkReportRsp {
    header?: [];
    keyData?: string;
    @JsonProperty({ type: Array, clazz: InpatientWorkReportSummary })
    data?: InpatientWorkReportSummary[];
    @JsonProperty({ type: InpatientWorkReportSummary })
    summary?: InpatientWorkReportSummary;
    total?: {
        template?: string;
        data?: [];
        count?: number;
        offset?: number;
        size?: number;
    };
}

export class BillingPerformanceData {
    amount?: number; //计提金额
    departmentName?: string; //科室名字
    departmentId?: string;
    doctorId?: string;
    doctorName?: string; //医生名字
    hospitalCount?: number; //住院人数
    outpatientCount?: number; //接诊人数
}
export class PhysicPatientAnalysisData {
    generalPersonalExamBusinessCount?: number; //普通个检-预约人次
    generalPersonalExamCheckedCount?: number; //普通个检-完检人次
    generalPersonalExamOrderCount?: number; //普通个检-开单人次
    generalTeamExamBusinessCount?: number; //普通团检-预约人次
    generalTeamExamCheckedCount?: number; //普通团检-完检人次
    generalTeamExamOrderCount?: number; //普通团检-开单人次
    phsPersonalExamBusinessCount?: number; //公卫个检-预约人次
    phsPersonalExamCheckedCount?: number; //公卫个检-完检人次
    phsPersonalExamOrderCount?: number; //公卫个检-开单人次
    phsTeamExamBusinessCount?: number; //公卫团检-预约人次
    phsTeamExamCheckedCount?: number; //公卫团检-完检人次
    phsTeamExamOrderCount?: number; //公卫团检-开单人次
    // 普通体检（完检） = 普通个检（完检） + 普通团检（完检）
    get generalExamCheckedCount(): number {
        return (this.generalPersonalExamCheckedCount ?? 0) + (this.generalTeamExamCheckedCount ?? 0);
    }
    //  公卫体检（完检） = 公卫个检（完检） + 公卫团检（完检）
    get phsExamCheckedCount(): number {
        return (this.phsPersonalExamCheckedCount ?? 0) + (this.phsTeamExamCheckedCount ?? 0);
    }
    //     完检人次 = 普通体检（完检） + 公卫体检（完检）
    get totalExamCheckedCount(): number {
        return this.generalExamCheckedCount + this.phsExamCheckedCount;
    }
}

export class RevenueSourceTypeListItem {
    id?: number;
    strId?: string;
    name?: string;
    sort?: number;
    children?: RevenueSourceTypeListItem[];
}
