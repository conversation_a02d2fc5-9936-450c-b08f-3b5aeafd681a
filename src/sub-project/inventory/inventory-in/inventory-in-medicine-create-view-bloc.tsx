/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-03-20
 *
 * @description
 */

import { Bloc, BlocEvent } from "../../bloc";
import React from "react";
import { EventName } from "../../bloc/bloc";
import {
    AntibioticEnum,
    BaseMedicineTypeEnum,
    GoodsInfo,
    GoodsInfoShebao,
    GoodsMultiPriceView,
    GoodsSubType,
    GoodsType,
    GoodsTypeId,
    IngredientEnum,
    MaintainTypeEnum,
    MedicalMaterialDeviceType,
    OtcTypeEnum,
    SheBaoPayModeEnum,
    SubClinicPricePriceMode,
} from "../../base-business/data/beans";
import { InventoryMedicineItem } from "../data/inventory-draft";
import { showOptionsBottomSheet } from "../../base-ui/dialog/bottom_sheet";
import _, { isNil } from "lodash";
import { WesternMedicineConfigProvider } from "../../outpatient/data/western-medicine-config";
import { userCenter } from "../../user-center";
import { InventoryConst } from "../inventory-const";
import { GoodsAgent, GoodsFeeTypeListItem, PharmacyListItem } from "../../data/goods/goods-agent";
import {
    GoodsSecondaryClassificationCustomTypesItem,
    InventoryGoodsAgent,
    SheBaoCodeSearchGoodsListItem,
    GoodsTagTypesItemListItem,
} from "../inventory-goods/data/inventory-goods-agent";
import { switchMap } from "rxjs/operators";
import { ABCError } from "../../common-base-module/common-error";
import { Subject } from "rxjs";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { BarcodeScanner } from "../../base-ui/camera/barcode-scanner";
import { showConfirmDialog } from "../../base-ui/dialog/dialog-builder";
import { errorSummary } from "../../common-base-module/utils";
import { ChainReview, InOutTaxList, InventoryClinicConfig, SearchParams } from "../data/inventory-bean";
import { clinicSharedPreferences } from "../../base-business/preferences/scoped-shared-preferences";
import { CreateDirectoryTypeEnum, SheBaoCodeTypeEnum } from "../inventory-goods/data/inventory-goods-bean";
import { SheBaoSearchNationalCodeSearchPage } from "../inventory-goods/inventory-shebao-national-code-search-page-";
import { JsonMapper } from "../../common-base-module/json-mapper/json-mapper";
import { WesternMedicine } from "../../../assets/medicine_usage/western-medicine-config";
import { MedicineCreateSearchName } from "../inventory-goods/medicine-base-info/medicine-create-search-name";
import { MedicineBaseInfoAgent } from "../inventory-goods/medicine-base-info/data/medicine-base-info-agent";
import { InventoryUtils } from "../utils/inventory-utils";
import { TextStyles } from "../../theme";
import { pxToDp } from "../../base-ui/utils/ui-utils";
import { SelectPharmacologicalActionFormDialog } from "../inventory-goods/views/inventory-goods-pharmacological-action-dialog";
import { showNarcoticsDangerousDrugsReleaseSheet } from "../inventory-goods/views/inventory-narcotics-dangerous-drugs-release-dialog";
import { AbcDialog } from "../../base-ui/abc-app-library";
import { DrugClassifyList, GoodsClassifyList, MaterialClassifyList } from "./inventory-in-data/inventory-in-bean";
import { InventoryBusinessScopeDialog } from "../inventory-goods/views/inventory-business-scope-dialog";
import { InventoryStorageConditionDialog } from "../inventory-goods/views/inventory-storage-condition-dialog";
import { RangePicker } from "../../base-ui/picker/range-picker";
import { Range } from "../../base-ui/utils/value-holder";
import { GetGoodsSearchDomainMedicineCreateRsp } from "../inventory-goods/medicine-base-info/data/medicine-base-info-bean";
import { InventoryAgent } from "../data/inventory-agent";
import { PharmacyType } from "../../charge/data/charge-bean-air-pharmacy";
import { ABCUtils } from "../../base-ui/utils/utils";
import { CreateGoodsScene } from "../../data/goods/goods-bean";
import { InventoryInTagDialog } from "./inventory-in-views/inventory-in-tag-dialog";
import { InventorySettingMemberPricePage } from "../inventory-goods/views/inventory-setting-member-price/inventory-setting-member-price-page";
import { BusinessScopeList } from "../../base-business/data/clinic-data";
import { Toast } from "../../base-ui/dialog/toast";
import { showBottomPanel } from "../../base-ui/abc-app-library/panel";
import { InventoryGoodsSelectDosageDialog } from "../inventory-goods/views/inventory-goods-select-dosage-dialog";

const SAVE_INVENTORY_MEDICINE_TYPE_STATUS = "saveInventoryMedicineTypeStatus";
const SAVE_INVENTORY_MATERIAL_TYPE_STATUS = "saveInventoryMaterialTypeStatus";
const SAVE_INVENTORY_GOODS_TYPE_STATUS = "saveInventoryGoodsTypeStatus";

export enum PharmacologicSourceType {
    medicineWestern = 1, // 西成药
    medicineChinesePatent = 2, // 中成药
    medicineChinese = 3, // 中药
}

class State {
    isUploading = false;
    goodsInfo: GoodsInfo = new GoodsInfo();
    useUnit?: string; //包装单位
    useCount?: number; //入库量

    costPrice?: number; //进价
    expiredTime?: Date; //效期
    productionTime?: Date; //效期
    addInventory?: number; // 入库数量
    batchNumber?: string; //批号
    showErrorHint = false; // 是否显示错误提示

    manageTaxRat = false;
    medicineInTaxRat?: number; //药品进项税率
    medicineOutTaxRat?: number; //药品销项税率
    specifications?: string[]; // 规格 add by jianglei 2022.04.26
    formUnits?: string[];
    focusKey?: string;
    barCodeKey = "barCodeKey"; //条形码key
    cadnKey = "cadnKey"; //能用名cadnKey
    packageKey = "packageKey"; //包装规格key
    eqCoefficientKey = "eqCoefficientKey"; //包装规格key
    sellPriceKey = "sellPriceKey"; //售价key
    dismountPriceKey = "dismountPriceKey"; //拆零价key

    packagePriceKey = "packagePriceKey"; // 允许对外销售价格
    costPriceKey = "costPriceKey"; //进价key
    addStockCountKey = "addStockCountKey"; //入库数量
    pieceNumKey = "pieceNumKey"; // 制剂、包装数量

    pieceUnitKey = "pieceUnitKey"; // 计量计价单位、包装单位

    piecePriceKey = "piecePriceKey"; // 零售价格
    packageUnitKey = "packageUnitKey"; // 包装、包装单位
    nameKey = "nameKey"; // 商品名、物资名称、商品名称

    sheBaoCodeKey = "sheBaoCodeKey"; // 医保对码
    priceTypeKey = "priceTypeKey"; // 定价模式
    priceMakeupPercentKey = "priceMakeupPercentKey"; // 加成率
    otcTypeKey = "otcTypeKey"; // 处方OTC
    remarkKey = "remarkKey"; // 备注
    tagKey = "tagKey"; // 标签

    pieceUnitWeightKey = "pieceUnitWeightKey"; // 单x重量

    defaultMedicalInsuranceCodeStr?: string; // 医保对码默认显示文本

    createDirectoryType?: CreateDirectoryTypeEnum; // 新建类型（药品、物资、商品）
    customTypesList: GoodsSecondaryClassificationCustomTypesItem[] = []; // 药品物资 添加药品 选择药品类型的二级分类列表

    sheBaoCodeSearchGoodsSubType?: number; // 医保对码药品类型（西药 中成药  中药饮片 中药颗粒）
    sheBaoCodeSearchGoodsSelectItem?: SheBaoCodeSearchGoodsListItem; // 选中的医保对码项目

    inOutTaxList?: InOutTaxList[]; // 进销项税列表

    searchParams?: SearchParams;

    hasChanged?: boolean;

    feeTypesList: GoodsFeeTypeListItem[] = []; // 费用类型接口

    isHospital = userCenter.clinic?.isNormalHospital;

    chainReview?: ChainReview;
    priceMode?: SubClinicPricePriceMode; // 定价模式(1 固定售价 3 进价加成)

    //goods配置相关信息--此处关注多药房
    pharmacyInfoConfig?: InventoryClinicConfig;
    // 是否开启调价申请
    isOpenPriceAdjustmentApplication = false;
    list?: GoodsTagTypesItemListItem[];
    currentPharmacy?: PharmacyListItem;
    get isLocalPharmacy(): boolean {
        return this.currentPharmacy?.type == PharmacyType.normal;
    }

    get isOpenMultiplePharmacy(): boolean {
        return !!this.pharmacyInfoConfig?.isOpenMultiplePharmacy;
    }

    // 启用加成配置
    get isPurchaseMarkup(): boolean {
        return this.priceMode === SubClinicPricePriceMode.purchaseMarkup && !!this.chainReview?.isSupportPriceMakeUpMode;
    }

    toInventoryMedicineItem(): InventoryMedicineItem {
        const item = new InventoryMedicineItem();

        item.goodsInfo = this.goodsInfo;
        item.qrcode = this.goodsInfo.barCode;
        item.useUnit = this.useUnit;
        item.useCount = this.useCount;
        item.useUnitCostPrice = this.costPrice;
        item.expiredTime = this.expiredTime;
        item.addInventory = this.addInventory;
        item.batchNumber = this.batchNumber;
        item.productionDate = this.productionTime;

        return item;
    }

    get createGoodsTypeSettingItemTitleStr(): {
        goodsTypeTitle?: string; // 类型
        NameTitle?: string; // 名称
        materialSpecTitle?: string; // 规格
        manufacturerFullTitle?: string; // 厂家
    } {
        switch (this.createDirectoryType) {
            case CreateDirectoryTypeEnum.CreateMedicine:
                return {
                    goodsTypeTitle: "药品类型",
                    NameTitle: "商品名",
                    manufacturerFullTitle: this.goodsInfo.isMedicineWestAndChinesePatent ? "厂家" : "厂家/产地",
                };
            case CreateDirectoryTypeEnum.CreateSupplies:
                return {
                    goodsTypeTitle: "类型",
                    NameTitle: "通用名",
                    materialSpecTitle: "物资规格",
                    manufacturerFullTitle: "生产厂家",
                };
            case CreateDirectoryTypeEnum.CreateGoods:
                return {
                    goodsTypeTitle: "商品类型",
                    NameTitle: "商品名",
                    materialSpecTitle: "商品规格",
                    manufacturerFullTitle: "生产厂家",
                };
        }
        return { goodsTypeTitle: "", NameTitle: "" };
    }

    get covidDictFlagStr(): string {
        if (this.sheBaoCodeSearchGoodsSelectItem?.covidDictFlag == true) {
            return "新冠";
        }
        return "";
    }

    //医保相关的判断
    get medicalFeeGradeStr(): string {
        switch (this.sheBaoCodeSearchGoodsSelectItem?.medicalFeeGrade) {
            case 1:
                return "甲";
            case 2:
                return "乙";
            case 3:
                return "丙";
            case 4:
                return "医保";
            default:
                return "";
        }
    }

    clone(): State {
        return Object.assign(new State(), this);
    }
}

export class ScrollToFocusItemState extends State {
    static fromState(state: State): ScrollToFocusItemState {
        const newState = new ScrollToFocusItemState();
        Object.assign(newState, state);
        return newState;
    }
}

class InventoryInMedicineCreateViewBloc extends Bloc<_Event, State> {
    static Context = React.createContext<InventoryInMedicineCreateViewBloc | undefined>(undefined);

    private _loadCustomTypesTrigger: Subject<number> = new Subject();
    private _subType?: number;
    private _typeId?: number;

    static fromContext(context: InventoryInMedicineCreateViewBloc): InventoryInMedicineCreateViewBloc {
        return context;
    }

    constructor(options: { barcode: string; createDirectoryType?: CreateDirectoryTypeEnum; searchParams?: SearchParams }) {
        super();
        this.innerState.goodsInfo = new GoodsInfo();
        this.innerState.goodsInfo.barCode = options.barcode;
        this.innerState.searchParams = options.searchParams;
        this.innerState.createDirectoryType = options.createDirectoryType;
        this.dispatch(new _EventInit());
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventUpdateExpiredDate, this._mapEventUpdateExpiredDate); // 效期
        map.set(_EventUpdateProductionDate, this._mapEventUpdateProductionDate); // 生产日期
        map.set(_EventUpdateCostPrice, this._mapEventUpdateCostPrice); // 进价
        map.set(_EventUpdateUseUnit, this._mapEventUpdateUseUnit); // 入库单位
        map.set(_EventUpdateUseNum, this._mapEventUpdateUseNum); // 入库数量
        map.set(_EventUpdateBatchNum, this._mapEventUpdateBatchNum); // 批号
        map.set(_EventUpdateMedicineSubtype, this._mapEventUpdateMedicineSubtype); // 扫码入库-药品类型
        map.set(_EventSelectDosageForm, this._mapEventSelectDosageForm); // 剂型
        map.set(_EventModifyGoodsInfoFeeType, this._mapEventModifyGoodsInfoFeeType); // 费用类型
        map.set(_EventScrollToFocusItemEvent, this._mapEventScrollToFocusItemEvent); // 滚动到焦点项目
        // 基础
        map.set(_EventUpdateCadn, this._mapEventUpdateCadn); // 通用名
        map.set(_EventSearchCadn, this._mapEventSearchCadn); // 搜索通用名
        map.set(_EventUpdateMedicineGoodsTypeId, this._mapEventUpdateMedicineGoodsTypeId); // 切换药品类型
        map.set(_EventSelectCustomTypesForm, this._mapEventSelectCustomTypesForm); // 二级分类
        map.set(_EventSelectPharmacologic, this._mapEventSelectPharmacologic); // 药理作用、科室分类、功效分类
        map.set(_EventSearchName, this._mapEventSearchName); // 搜索商品名
        map.set(_EventUpdateName, this._mapEventUpdateName); // 商品名
        map.set(_EventSelectPharmaceuticalForm, this._mapEventSelectPharmaceuticalForm); // 剂型
        map.set(_EventUpdateOrigin, this._mapEventUpdateOrigin); // 进口/国产
        map.set(_EventSearchManufacturerFull, this._mapEventSearchManufacturerFull); // 搜索厂家/上市许可持有人
        map.set(_EventUpdateManufacturer, this._mapEventUpdateManufacturer); // 厂家
        map.set(_EventUpdateCertificateName, this._mapEventUpdateCertificateName); // 注册证名称
        map.set(_EventUpdateNmpn, this._mapEventUpdateNmpn); // 批准文号
        map.set(_EventUpdateBarCode, this._mapEventUpdateBarCode); // 条码
        map.set(_EventScanQRCode, this._mapEventScanQRCode); // 扫描条码
        map.set(_EventUpdateQRCode, this._mapEventUpdateQRCode); // 更新二维码
        map.set(_EventContinueScan, this._mapEventContinueScan); // 继续扫码
        map.set(_EventUpdateMedicineShortId, this._mapEventUpdateMedicineShortId); // 商品编码
        map.set(_EventUpdateExtendSpec, this._mapEventUpdateExtendSpec); // 中药规格
        map.set(_EventUpdateMaterialSpec, this._mapEventUpdateMaterialSpec); // 物资、商品（规格）
        map.set(_EventUpdatePieceNum, this._mapEventUpdatePieceNum); // 小包装数数量
        map.set(_EventUpdateEqCoefficient, this.mapEventUpdateEqCoefficient); // 换算当量
        map.set(_EventSelectPieceUnit, this._mapEventSelectPieceUnit); // 小包装数单位
        map.set(_EventSelectPackageUnit, this._mapEventSelectPackageUnit); // 包装单位
        map.set(_EventMedicineSelectSpecification, this._mapEventMedicineSelectSpecification); // 选择药品规格
        map.set(_EventUpdateComponentNum, this._mapEventUpdateComponentNum); // 容量
        map.set(_EventSelectComponentUnit, this._mapEventSelectComponentUnit); // 容量单位
        map.set(_EventUpdateDosageNum, this._mapEventUpdateDosageNum); // 成分含量
        map.set(_EventSelectDosageUnit, this._mapEventSelectDosageUnit); // 成分含量单位
        // 扩展
        map.set(_EventSelectOTCForm, this._mapEventSelectOTCForm); // 处方OTC
        map.set(_EventSelectNarcoticsDangerousDrugsRelease, this._mapEventSelectNarcoticsDangerousDrugsRelease); // 精麻毒放
        map.set(_EventSelectAntibiotics, this._mapEventSelectAntibiotics); // 抗菌药物级别
        map.set(_EventUpdateDefinedDailyDose, this._mapEventUpdateDefinedDailyDose); // DDD值
        map.set(_EventSelectBaseMedicineType, this._mapEventSelectBaseMedicineType); // 国家基药
        map.set(_EventSelectMedicalMaterialDeviceType, this._mapEventSelectMedicalMaterialDeviceType); // 器械耗材分类
        map.set(_EventSelectMaintenanceClassification, this._mapEventSelectMaintenanceClassification); // 养护分类
        map.set(_EventSelectStorageConditions, this._mapEventSelectStorageConditions); // 存储条件
        map.set(_EventSelectExpirationDate, this._mapEventSelectExpirationDate); // 保质期（月）
        map.set(_EventSelectTag, this._mapEventSelectSelectTag); // 标签
        map.set(_EventModifyPosition, this._mapEventModifyPosition); // 柜号
        // 定价
        map.set(_EventSelectPriceType, this._mapEventSelectPriceType); // 定价模式（收配置控制，允许进价加成时可选）
        map.set(_EventSelectMarkupRate, this._mapEventSelectMarkupRate); // 加成率（进价加成时展示）
        map.set(_EventUpdateIsSell, this._mapEventUpdateIsSell); // 物资、商品（是否允许对外销售）
        map.set(_EventUpdatePackagePrice, this._mapEventUpdatePackagePrice); // 售价（固定售价、进价均展示 进价加成是范围值且只读）
        map.set(_EventUpdateDismounting, this._mapEventUpdateDismounting); // 是否拆零
        map.set(_EventUpdatePiecePrice, this._mapEventUpdatePiecePrice); // 拆零价
        map.set(_EventUpdateInOutTax, this._mapEventUpdateInOutTax); // 默认进/销项税率
        map.set(_EventUpdateInTaxRat, this._mapEventUpdateInTaxRat); // 进项税率
        map.set(_EventUpdateOutTaxRat, this._mapEventUpdateOutTaxRat); // 销项税率
        // map.set(_EventSelectProfitCategorization, this._mapEventSelectProfitCategorization); // 利润分类
        // 医保
        map.set(_EventUpdateMedicineSheBaoCode, this._mapEventUpdateMedicineSheBaoCode); // 更新医保对码
        map.set(_EventSelectMedicalInsurancePayment, this._mapEventSelectMedicalInsurancePayment); // 选择医保支付

        map.set(_EventFinishCreate, this._mapEventFinishCreate); // 完成
        map.set(_EventSelectBusinessScope, this._mapEventSelectBusinessScope); // 所属经营范围
        map.set(_EventUpdateMark, this._mapEventUpdateMark); // 备注
        map.set(_EventSelectNpmnExpiryDate, this._mapEventSelectNpmnExpiryDate); // 批准文号有效期
        map.set(_EventSelectMemberPrice, this._mapEventSelectMemberPrice); // 设置会员价
        map.set(_EventModifyDosageUnit, this._EventModifyDosageUnit); // 中药类型--剂量单位
        map.set(_EventModifyPlaceOfOrigin, this._mapEventModifyPlaceOfOrigin); // 产地
        map.set(_EventUpdatePieceWeight, this._mapEventUpdatePieceWeight); // 单X重量

        return map;
    }

    // 效期
    public requestUpdateExpiredDate(time: Date): void {
        this.dispatch(new _EventUpdateExpiredDate(time));
    }

    // 生产日期
    public requestUpdateProductionDate(time: Date): void {
        this.dispatch(new _EventUpdateProductionDate(time));
    }

    // 进价
    public requestUpdateCostPrice(price: number): void {
        this.dispatch(new _EventUpdateCostPrice(price));
    }

    // 入库数量
    public requestUpdateUseNum(packageNum: number): void {
        this.dispatch(new _EventUpdateUseNum(packageNum));
    }

    // 入库单位
    public requestUpdateUseUnit(useUnit: string): void {
        this.dispatch(new _EventUpdateUseUnit(useUnit));
    }

    // 批号
    public requestUpdateBatchNum(batchNo: string): void {
        this.dispatch(new _EventUpdateBatchNum(batchNo));
    }

    public requestUpdateQRCode(qrCode: string): void {
        this.dispatch(new _EventUpdateQRCode(qrCode));
    }

    public requestUpdateCadn(value: string): void {
        this.dispatch(new _EventUpdateCadn(value));
    }

    // 搜索通用名
    public requestSearchCadn(): void {
        this.dispatch(new _EventSearchCadn());
    }

    // 商品名
    public requestUpdateName(name: string): void {
        this.dispatch(new _EventUpdateName(name));
    }

    // 搜索名称商品名
    public requestSearchName(): void {
        this.dispatch(new _EventSearchName());
    }

    // 批准文号
    public requestUpdateNmpn(value: string): void {
        this.dispatch(new _EventUpdateNmpn(value));
    }

    // 选择进口/国产
    public requestUpdateOrigin(/*value: string*/): void {
        this.dispatch(new _EventUpdateOrigin());
    }

    // 生产厂家
    public requestUpdateManufacturer(value: string): void {
        this.dispatch(new _EventUpdateManufacturer(value));
    }

    // 搜索(生产厂家/上市许可持有人)
    public requestSearchManufacturerFull(type: "manufacturerFull" | "mha"): void {
        this.dispatch(new _EventSearchManufacturerFull(type));
    }

    // 扫码入库-药品类型
    public requestUpdateMedicineSubType(subType: number, typeId: number): void {
        this.dispatch(new _EventUpdateMedicineSubtype(subType, typeId));
    }

    // "容量:成分含量*制剂/包装"
    public requestMedicineSelectSpecification(): void {
        this.dispatch(new _EventMedicineSelectSpecification());
    }

    // 物资、商品规格
    public requestUpdateMaterialSpec(materialSpec?: string): void {
        this.dispatch(new _EventUpdateMaterialSpec(materialSpec));
    }

    // 剂型
    public requestSelectDosageForm(): void {
        this.dispatch(new _EventSelectDosageForm());
    }

    // 零售价格
    public requestUpdatePackagePrice(price?: number): void {
        this.dispatch(new _EventUpdatePackagePrice(price));
    }

    // 允许对外销售
    public requestUpdateIsSell(isSell: number): void {
        this.dispatch(new _EventUpdateIsSell(isSell));
    }

    // 是否拆零
    public requestUpdateDismounting(dismounting: number): void {
        this.dispatch(new _EventUpdateDismounting(dismounting));
    }

    // 拆零价
    public requestUpdatePiecePrice(piecePrice?: number): void {
        this.dispatch(new _EventUpdatePiecePrice(piecePrice));
    }

    // 费用类型
    public requestModifyGoodsInfoFeeType(): void {
        this.dispatch(new _EventModifyGoodsInfoFeeType());
    }

    // 容量单位
    public requestSelectComponentUnit(): void {
        this.dispatch(new _EventSelectComponentUnit());
    }

    // 剂量单位
    public requestSelectDosageUnit(): void {
        this.dispatch(new _EventSelectDosageUnit());
    }

    // 制剂单位
    public requestSelectPieceUnit(): void {
        this.dispatch(new _EventSelectPieceUnit());
    }

    // 包装单位
    public requestSelectPackageUnit(): void {
        this.dispatch(new _EventSelectPackageUnit());
    }

    // 制剂
    public requestUpdatePieceNum(value: number): void {
        this.dispatch(new _EventUpdatePieceNum(value));
    }

    // 换算当量
    public requestUpdateEqCoefficient(value: number): void {
        this.dispatch(new _EventUpdateEqCoefficient(value));
    }

    // 条码
    public requestUpdateBarCode(value: string): void {
        this.dispatch(new _EventUpdateBarCode(value));
    }

    // 商品编码
    public requestUpdateMedicineShortId(shortId: string): void {
        this.dispatch(new _EventUpdateMedicineShortId(shortId));
    }

    // 更新注册证名称
    public requestUpdateCertificateName(certificateName: string): void {
        this.dispatch(new _EventUpdateCertificateName(certificateName));
    }

    // 选择医保对码
    public requestUpdateMedicineSheBaoCode(): void {
        this.dispatch(new _EventUpdateMedicineSheBaoCode());
    }

    // 选择医保支付
    public requestSelectMedicalInsurancePayment(): void {
        this.dispatch(new _EventSelectMedicalInsurancePayment());
    }

    // 更新类型
    public requestUpdateMedicineGoodsTypeId(): void {
        this.dispatch(new _EventUpdateMedicineGoodsTypeId());
    }

    // 二级分类
    public requestSelectCustomTypesForm(): void {
        this.dispatch(new _EventSelectCustomTypesForm());
    }

    // 药理作用(1 西药-药理作用分类  2 中成药-科室分类  3 中药-功效分类)
    public requestSelectPharmacologic(type: PharmacologicSourceType): void {
        this.dispatch(new _EventSelectPharmacologic(type));
    }

    // 剂型
    public requestSelectPharmaceuticalForm(): void {
        this.dispatch(new _EventSelectPharmaceuticalForm());
    }

    // 处方OTC
    public requestSelectOTCForm(): void {
        this.dispatch(new _EventSelectOTCForm());
    }

    // 精麻毒放
    public requestSelectNarcoticsDangerousDrugsRelease(): void {
        this.dispatch(new _EventSelectNarcoticsDangerousDrugsRelease());
    }

    // 抗菌药物
    public requestSelectAntibiotics(): void {
        this.dispatch(new _EventSelectAntibiotics());
    }

    // DDD值
    public requestUpdateDefinedDailyDose(options: { dddOfAntibiotic?: number; unitOfAntibiotic?: string }): void {
        const { dddOfAntibiotic, unitOfAntibiotic } = options;
        this.dispatch(new _EventUpdateDefinedDailyDose(dddOfAntibiotic, unitOfAntibiotic));
    }

    // 国家基药
    public requestSelectBaseMedicineType(): void {
        this.dispatch(new _EventSelectBaseMedicineType());
    }

    // 器械耗材分类
    public requestSelectMedicalMaterialDeviceType(): void {
        this.dispatch(new _EventSelectMedicalMaterialDeviceType());
    }

    // 养护分类
    public requestSelectMaintenanceClassification(): void {
        this.dispatch(new _EventSelectMaintenanceClassification());
    }

    // 存储条件
    public requestSelectStorageConditions(): void {
        this.dispatch(new _EventSelectStorageConditions());
    }

    // 标签
    public requestSelectTag(): void {
        this.dispatch(new _EventSelectTag());
    }

    // 保质期（月）
    public requestSelectExpirationDate(monthNum: number): void {
        this.dispatch(new _EventSelectExpirationDate(monthNum));
    }

    // 柜号
    public requestModifyPosition(text?: string): void {
        this.dispatch(new _EventModifyPosition(text));
    }

    // 定价模式
    public requestSelectPriceType(): void {
        this.dispatch(new _EventSelectPriceType());
    }

    // 加成率
    public requestSelectMarkupRate(markupRate: number): void {
        this.dispatch(new _EventSelectMarkupRate(markupRate));
    }

    // 利润分类
    // public requestSelectProfitCategorization(): void {
    //     this.dispatch(new _EventSelectProfitCategorization());
    // }

    // 中药规格
    public requestUpdateExtendSpec(extendSpec: string): void {
        this.dispatch(new _EventUpdateExtendSpec(extendSpec));
    }

    // 启用默认进销项税率
    public requestEnableDefaultInOutTax(defaultInOutTax: number): void {
        this.dispatch(new _EventUpdateInOutTax(defaultInOutTax));
    }

    // 扫条码
    public requestScanQRCode(): void {
        this.dispatch(new _EventScanQRCode());
    }

    //继续扫码
    public requestContinueScan(): void {
        this.dispatch(new _EventContinueScan());
    }

    public requestFinishCreate(): void {
        this.dispatch(new _EventFinishCreate());
    }

    async requestCreateMedicine(): Promise<GoodsInfo | undefined> {
        let goodsInfo: GoodsInfo;
        const innerState = this.innerState;
        if (!_.isEmpty(innerState.goodsInfo.id)) {
            goodsInfo = await GoodsAgent.updateGoodsInfo(innerState.goodsInfo);
        } else {
            goodsInfo = await GoodsAgent.createGoods(innerState.goodsInfo, CreateGoodsScene.MANUAL);
        }
        if (goodsInfo != null) {
            innerState.goodsInfo = goodsInfo;
            this.update();
            return goodsInfo;
        }

        return undefined;
    }

    // 进项税率
    public requestUpdateInTaxRat(inTax: number): void {
        this.dispatch(new _EventUpdateInTaxRat(inTax));
    }

    // 销项税率
    public requestUpdateOutTaxRat(outTax: number): void {
        this.dispatch(new _EventUpdateOutTaxRat(outTax));
    }

    public requestUpdateComponentNum(value: number): void {
        this.dispatch(new _EventUpdateComponentNum(value));
    }

    // 成分含量
    public requestUpdateDosageNum(value: number): void {
        this.dispatch(new _EventUpdateDosageNum(value));
    }

    // 选择所属经营范围
    requestSelectBusinessScope(): void {
        this.dispatch(new _EventSelectBusinessScope());
    }

    requestUpdateMark(text: string): void {
        this.dispatch(new _EventUpdateMark(text));
    }

    requestSelectNpmnExpiryDate(): void {
        this.dispatch(new _EventSelectNpmnExpiryDate());
    }
    // 设置会员价
    requestSelectMemberPrice(): void {
        this.dispatch(new _EventSelectMemberPrice());
    }

    // 剂量
    requestModifyDosageUnit(dosageUnit: string): void {
        this.dispatch(new _EventModifyDosageUnit(dosageUnit));
    }

    // 产地
    requestModifyPlaceOfOrigin(placeOrigin: string): void {
        this.dispatch(new _EventModifyPlaceOfOrigin(placeOrigin));
    }

    // 单X重量
    requestUpdatePieceWeight(value: number): void {
        this.dispatch(new _EventUpdatePieceWeight(value));
    }

    // 针对shebao属地为河北省的定点机构，且有医保对码的药品：售价校验不能为空，不能为0医保价格校验
    // 新建、修改库存档案时，如果用户选择的定价模式是「进价加成」，则前端不校验售价是否为0。
    private isSheBaoNationalCodeGoods(): boolean {
        const goodsInfo = this.innerState.goodsInfo;
        // 针对shebao属地为河北省的定点机构
        const heBeiDistraction = userCenter.inventoryClinicConfig?.chainReview?.currentRegionPriceNonZero;
        // 医保对码的药品(如果shebaoPayMode不存在就不校验，如果存在则需要校验是否不过医保)
        const isSheBaoGoods =
            !!goodsInfo?.shebao?.nationalCode && (isNil(goodsInfo?.shebaoPayMode) || goodsInfo?.shebaoPayMode != SheBaoPayModeEnum.NO_USE);

        // 固定售价
        const fixedSellingPrice = goodsInfo?.priceType == SubClinicPricePriceMode.sellingPrice;

        return !!heBeiDistraction && isSheBaoGoods && fixedSellingPrice;
    }

    // 单X重量必填规则：开通医保 & 该饮片有医保对码 & 结算方式非不过医保
    private isNeedVerifyPieceUnitWeight(): boolean {
        const goodsInfo = this.innerState.goodsInfo;
        // 开通医保
        const openSheBao = userCenter.shebaoConfig?.isOpenSocial;
        // 地区限制：山东济南
        const shandongJinan = userCenter.shebaoConfig?.isShandongJinan;
        // 中药饮片（配方饮片、非配方饮片）
        const isChinesePiece = goodsInfo.isMedicineChinesePiece || goodsInfo.isMedicineChineseNonFormula;
        // 医保对码的药品(如果shebaoPayMode不存在就不校验，如果存在则需要校验是否不过医保)
        const isSheBaoGoods =
            !!goodsInfo?.shebao?.nationalCode && (isNil(goodsInfo?.shebaoPayMode) || goodsInfo?.shebaoPayMode != SheBaoPayModeEnum.NO_USE);
        return !!(openSheBao && shandongJinan && isChinesePiece && isSheBaoGoods);
    }

    validate(): boolean {
        const innerState = this.innerState;
        const goodsInfo = innerState.goodsInfo;
        //【新建药品必填项】
        // 西药必填:(通用名：medicineCadn 制剂：pieceNum 制剂单位：pieceUnit 包装：packageUnit 总部定价：packagePrice 处方OTC:otcType 定价模式：priceType 加成率：priceMakeupPercent)
        // 中成药:(通用名：medicineCadn 制剂：pieceNum 制剂单位：pieceUnit 包装：packageUnit 总部定价：packagePrice 处方OTC:otcType 定价模式：priceType 加成率：priceMakeupPercent)
        // 中药必填:(通用名：medicineCadn 计量计价单位：pieceUnit 零售价格：piecePrice 处方OTC：otcType 定价模式:priceType 加成率:priceMakeupPercent)
        // 医用材料：(定价模式:priceType 加成率:priceMakeupPercent)
        // 物资必填:(物资名称：name 包装数量：pieceNum 最小单位：pieceUnit 包装单位：packageUnit)
        // 商品必填:(物资名称：name 包装数量：pieceNum 最小单位：pieceUnit 包装单位：packageUnit 定价模式:priceType 加成率:priceMakeupPercent)
        // 固定售价才需要校验售价与拆零价
        const fixedSellingPrice = goodsInfo?.priceType == SubClinicPricePriceMode.sellingPrice,
            purchaseMarkup = goodsInfo?.priceType == SubClinicPricePriceMode.purchaseMarkup;
        // 进价加成模式下，加成率不能为空
        if (purchaseMarkup && isNil(goodsInfo?.priceMakeupPercent)) {
            this._setErrorHint(innerState.priceMakeupPercentKey);
            return false;
        }

        if (this.isSheBaoNationalCodeGoods()) {
            if (goodsInfo.isMedicineWestAndChinesePatent) {
                if (fixedSellingPrice && !goodsInfo?.packagePrice) {
                    this._setErrorHint(innerState.packagePriceKey);
                    Toast.show("医保要求售价必填且不能0", { warning: true });
                    return false;
                }
            } else if (goodsInfo.isChineseMedicine) {
                if (fixedSellingPrice && !goodsInfo?.piecePrice) {
                    this._setErrorHint(innerState.dismountPriceKey);
                    Toast.show("医保要求售价必填且不能0", { warning: true });
                    return false;
                }
            }
        }

        if (this.isNeedVerifyPieceUnitWeight() && isNil(goodsInfo?.pieceUnitWeight)) {
            Toast.show(`单${goodsInfo?.pieceUnit}重量不能为空`, { warning: true });
            return false;
        }

        if (innerState.createDirectoryType) {
            switch (innerState.createDirectoryType) {
                case CreateDirectoryTypeEnum.CreateMedicine:
                    if (goodsInfo.medicineCadn == undefined) {
                        this._setErrorHint(innerState.cadnKey);
                        return false;
                    }
                    if (goodsInfo.pieceUnit == undefined) {
                        this._setErrorHint(innerState.pieceUnitKey);
                        return false;
                    }
                    // if (goodsInfo.otcType == undefined) {
                    //     this._setErrorHint(innerState.otcTypeKey);
                    //     return false;
                    // }
                    if (goodsInfo.isMedicineWestAndChinesePatent) {
                        if (goodsInfo.pieceNum == undefined) {
                            this._setErrorHint(innerState.pieceNumKey);
                            return false;
                        }
                        if (goodsInfo.packageUnit == undefined) {
                            this._setErrorHint(innerState.packageUnitKey);
                            return false;
                        }
                        if (fixedSellingPrice && goodsInfo.packagePrice == undefined) {
                            this._setErrorHint(innerState.packagePriceKey);
                            return false;
                        }
                    } else if (goodsInfo.isChineseMedicine) {
                        if (fixedSellingPrice && goodsInfo.piecePrice == undefined) {
                            this._setErrorHint(innerState.piecePriceKey);
                            return false;
                        }
                    }
                    break;
                case CreateDirectoryTypeEnum.CreateSupplies:
                case CreateDirectoryTypeEnum.CreateGoods:
                    if (goodsInfo.name == undefined) {
                        this._setErrorHint(innerState.nameKey);
                        return false;
                    }
                    if (goodsInfo.pieceNum == undefined) {
                        this._setErrorHint(innerState.pieceNumKey);
                        return false;
                    }
                    if (goodsInfo.pieceUnit == undefined) {
                        this._setErrorHint(innerState.pieceUnitKey);
                        return false;
                    }
                    if (goodsInfo.packageUnit == undefined) {
                        this._setErrorHint(innerState.packageUnitKey);
                        return false;
                    }
                    break;
            }
        } else {
            // 【扫码入库】
            if (_.isEmpty(goodsInfo.barCode)) {
                this._setErrorHint(innerState.barCodeKey!);
                return false;
            }
            if (_.isEmpty(goodsInfo.medicineCadn)) {
                this._setErrorHint(innerState.cadnKey);
                return false;
            }

            if (goodsInfo.pieceNum == undefined || goodsInfo.pieceUnit == undefined || goodsInfo.packageUnit == undefined) {
                this._setErrorHint(innerState.packageKey);
                return false;
            }
            if (fixedSellingPrice && goodsInfo.packagePrice == undefined) {
                this._setErrorHint(innerState.sellPriceKey);
                return false;
            }

            if (fixedSellingPrice && goodsInfo.dismounting == 1 && goodsInfo.piecePrice == undefined) {
                this._setErrorHint(innerState.dismountPriceKey);
                return false;
            }

            if ((innerState.useCount ?? 0) <= 0 || innerState.useCount! > InventoryConst.maxStockItemNum) {
                this._setErrorHint(innerState.addStockCountKey);
                return false;
            }
            if (innerState.costPrice == null || innerState.costPrice < 0) {
                this._setErrorHint(innerState.costPriceKey);
                return false;
            }
        }

        return true;
    }
    /**
     * 根据商品类型修改默认费用类别
     */
    _goodsTypeToDefaultType(): void {
        const { feeTypesList, goodsInfo } = this.innerState;
        let defaultFeeTypeName = "";
        // 获取默认费用类型
        if (goodsInfo.isWesternMedicine) {
            defaultFeeTypeName = "西药费";
        } else if (goodsInfo.isMedicineChinesePiece) {
            defaultFeeTypeName = "中药费";
        } else if (goodsInfo.isMedicineChineseGranule) {
            defaultFeeTypeName = "中药费";
        } else if (goodsInfo.isChineseWesternMedicine) {
            defaultFeeTypeName = "中成药费";
        } else if (goodsInfo.isMedicalMaterial || goodsInfo.isMaterial || goodsInfo.isGoods) {
            defaultFeeTypeName = "卫生材料费";
        }
        const defaultFeeType = feeTypesList.find((item) => item.name === defaultFeeTypeName);
        if (!!defaultFeeType) {
            goodsInfo.feeTypeId = defaultFeeType.feeTypeId;
            goodsInfo.feeTypeName = defaultFeeType.name;
        }
        this.update();
    }

    private async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
        if (event.state) {
            yield event.state;
            return;
        }

        yield this.innerState.clone();
    }

    private async *_mapEventUpdateExpiredDate(event: _EventUpdateExpiredDate): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.expiredTime = event.time;

        yield innerState.clone();
    }

    private async *_mapEventUpdateProductionDate(event: _EventUpdateProductionDate): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.productionTime = event.date;

        yield innerState.clone();
    }

    private async *_mapEventUpdateCostPrice(event: _EventUpdateCostPrice): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.costPrice = event.price;
        if (innerState.goodsInfo.priceType == SubClinicPricePriceMode.purchaseMarkup && !isNil(innerState.costPrice)) {
            const ratio = 1 + (innerState.goodsInfo.priceMakeupPercent || 0) / 100;
            innerState.goodsInfo.packagePrice = Number(ABCUtils.paddingMoney(innerState.costPrice * ratio));
            if (innerState.goodsInfo.dismounting) {
                innerState.goodsInfo.piecePrice = Number(
                    ABCUtils.paddingMoney(innerState.goodsInfo.packagePrice / (innerState.goodsInfo.pieceNum ?? 1))
                );
            }
        }
        yield innerState.clone();
    }

    private async *_mapEventUpdateUseUnit(event: _EventUpdateUseUnit): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.useUnit = event.packageUnit;

        const units = innerState.goodsInfo!.stockInUnits;

        //选择小单位g时去掉小数位
        if (units.length > 1 && units.indexOf(this.innerState.useUnit ?? "") == 0) {
            innerState.useCount = Math.trunc(innerState.useCount ?? 0);
        }

        yield innerState.clone();
    }

    private async *_mapEventUpdateUseNum(event: _EventUpdateUseNum): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.useCount = event.packageNum;
        yield innerState.clone();
    }

    private async *_mapEventUpdateBatchNum(event: _EventUpdateBatchNum): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.batchNumber = event.batchNum;
        yield innerState.clone();
    }

    private async *_mapEventUpdateQRCode(event: _EventUpdateQRCode): AsyncGenerator<State> {
        const innerState = this.innerState;
        const rsp: GetGoodsSearchDomainMedicineCreateRsp | undefined = await MedicineBaseInfoAgent.getGoodsSearchDomainMedicineCreate({
            keywords: event.qrCode,
            type: innerState.createDirectoryType,
        }).catchIgnore();
        if (!!rsp?.list?.length) {
            const goodsInfo = innerState.goodsInfo;
            const {
                //基础
                medicineCadn,
                typeId,
                subType,
                pharmacologicId,
                pharmacologicFullName,
                name,
                manufacturerFull,
                medicineNmpn,
                barCode,
                packageUnit,
                pieceNum,
                pieceUnit,
                //扩展
                otcType,
                dangerIngredient,
                antibiotics,
                dddOfAntibiotics,
                unitOfAntibiotics,
                shelfLife,
                baseMedicineType,
                mha,
                maintainType,
                storageType,
                dosageFormType,
            } = rsp.list[0];

            // 基础
            goodsInfo.medicineCadn = medicineCadn; // 通用名
            goodsInfo.typeId = typeId; // 药品类型ID
            goodsInfo.subType = subType; // 药品类型
            goodsInfo.pharmacologicId = pharmacologicId; // 药理作用分类ID
            goodsInfo.pharmacologicsName = pharmacologicFullName; // 药理作用分类名
            goodsInfo.name = name; // 商品名
            goodsInfo.manufacturerFull = manufacturerFull; // 生产厂家
            goodsInfo.medicineNmpn = medicineNmpn; // 批准文号
            goodsInfo.barCode = barCode; // 条形码
            goodsInfo.pieceNum = pieceNum; // 制剂
            goodsInfo.pieceUnit = pieceUnit; // 制剂单位
            goodsInfo.packageUnit = packageUnit; // 包装
            //扩展
            goodsInfo.otcType = otcType; // 处方药/OTC
            goodsInfo.dangerIngredient = dangerIngredient; // 精麻毒放
            goodsInfo.antibiotic = antibiotics; // 抗菌级别
            goodsInfo.dddOfAntibiotic = dddOfAntibiotics; // DDD值
            goodsInfo.unitOfAntibiotic = unitOfAntibiotics; // DDD单位
            goodsInfo.baseMedicineType = baseMedicineType; // 基药
            goodsInfo.mha = mha; // 上市许可持有人
            goodsInfo.maintainType = maintainType; // 养护分类
            goodsInfo.storageType = storageType; // 存储条件
            goodsInfo.shelfLife = shelfLife; // 保质期
            goodsInfo.dosageFormType = dosageFormType; // 剂型

            if (goodsInfo.typeId) this._loadCustomTypesTrigger.next();
        } else {
            innerState.goodsInfo.barCode = event.qrCode;
        }
        yield innerState.clone();
    }

    private async *_mapEventUpdateCadn(event: _EventUpdateCadn): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.goodsInfo.medicineCadn = event.cadn;
        innerState.hasChanged = true;
        yield innerState.clone();
    }

    private async *_mapEventUpdateName(event: _EventUpdateName): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.goodsInfo.name = event.name;
        innerState.hasChanged = true;
        yield innerState.clone();
    }

    private async *_mapEventSearchName(/*event: _EventSearchName*/): AsyncGenerator<State> {
        const innerState = this.innerState;
        let client;
        let cadn;

        switch (innerState.createDirectoryType) {
            case CreateDirectoryTypeEnum.CreateMedicine:
                client = "medicine-tradename";
                cadn = innerState.goodsInfo.medicineCadn ?? "";
                break;
            case CreateDirectoryTypeEnum.CreateSupplies:
                client = "material-registration-name";
        }
        const result = await MedicineCreateSearchName.show({
            client: client,
            key_word: "",
            cadn: cadn,
        });

        if (!result) return;
        innerState.goodsInfo.name =
            result?.fields?.medicine_cadn?.[0] ?? result?.fields?.tradeName?.[0] ?? result?.fields?.registered_name_keyword?.[0] ?? "";
        innerState.goodsInfo.certificateName = result?.registered_name ?? "";
        innerState.hasChanged = true;
        yield innerState.clone();
    }

    private async *_mapEventSearchCadn(/*event: _EventSearchCadn*/): AsyncGenerator<State> {
        const innerState = this.innerState;

        const result = await MedicineCreateSearchName.show({
            client: "medicine-cadn",
            key_word: "",
        });
        if (!result) return;
        innerState.goodsInfo.medicineCadn = String(result?.fields?.medicine_cadn?.[0]) ?? "";
        innerState.goodsInfo.subType = result.medicineSubType ?? innerState.goodsInfo.subType;
        innerState.goodsInfo.typeId = result.typeId ?? innerState.goodsInfo.typeId;
        if (innerState.goodsInfo.typeId) this._loadCustomTypesTrigger.next(); // 搜索通用名后拉取二级分类列表
        innerState.hasChanged = true;
        yield innerState.clone();
    }

    private async *_mapEventUpdateNmpn(event: _EventUpdateNmpn): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.goodsInfo.medicineNmpn = event.name;
        innerState.hasChanged = true;

        yield innerState.clone();
    }

    private async *_mapEventUpdateManufacturer(event: _EventUpdateManufacturer): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.goodsInfo.manufacturerFull = event.name;
        innerState.hasChanged = true;

        yield innerState.clone();
    }

    private async *_mapEventSearchManufacturerFull(event: _EventSearchManufacturerFull): AsyncGenerator<State> {
        const innerState = this.innerState;
        let client;
        let cadn;
        let tradeName;
        let registered_name;
        switch (innerState.createDirectoryType) {
            case CreateDirectoryTypeEnum.CreateMedicine:
                client = "medicine-manufacturer";
                cadn = innerState.goodsInfo.medicineCadn ?? "";
                tradeName = innerState.goodsInfo.name ?? "";
                break;
            case CreateDirectoryTypeEnum.CreateSupplies:
                client = "material-manufacturer";
                registered_name = innerState.goodsInfo.name ?? "";
        }

        const result = await MedicineCreateSearchName.show({
            client: client,
            key_word: "",
            cadn: cadn,
            tradeName: tradeName,
            registered_name: registered_name,
        });

        if (!result) return;
        const str =
            result?.fields?.medicine_cadn?.[0] ??
            result?.fields?.medicine_manufacturer?.[0] ??
            result.fields?.manufacturer_keyword?.[0] ??
            "";
        if (event.type === "manufacturerFull") {
            innerState.goodsInfo.manufacturerFull = str;
            innerState.goodsInfo.mha = innerState.goodsInfo.manufacturerFull;
        } else {
            innerState.goodsInfo.mha = str;
        }

        innerState.hasChanged = true;

        yield innerState.clone();
    }

    private async *_mapEventUpdateMedicineSubtype(event: _EventUpdateMedicineSubtype): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.goodsInfo.subType = event.subType;
        innerState.goodsInfo.typeId = event.typeId;
        yield innerState.clone();
    }

    private _constructionGoodsInfo(): void {
        const innerState = this.innerState;
        switch (innerState.createDirectoryType) {
            case CreateDirectoryTypeEnum.CreateMedicine: {
                this._subType =
                    clinicSharedPreferences.getObject(SAVE_INVENTORY_MEDICINE_TYPE_STATUS)?.subType ?? GoodsSubType.medicineWestern;
                this._typeId = clinicSharedPreferences.getObject(SAVE_INVENTORY_MEDICINE_TYPE_STATUS)?.typeId ?? GoodsTypeId.medicineWest;
                break;
            }
            case CreateDirectoryTypeEnum.CreateSupplies: {
                this._subType =
                    clinicSharedPreferences.getObject(SAVE_INVENTORY_MATERIAL_TYPE_STATUS)?.subType ?? GoodsSubType.materialMedical;
                this._typeId =
                    clinicSharedPreferences.getObject(SAVE_INVENTORY_MATERIAL_TYPE_STATUS)?.typeId ?? GoodsTypeId.materialMedical;
                break;
            }
            case CreateDirectoryTypeEnum.CreateGoods: {
                this._subType =
                    clinicSharedPreferences.getObject(SAVE_INVENTORY_GOODS_TYPE_STATUS)?.subType ??
                    (userCenter.clinic?.isDrugstoreButler ? GoodsSubType.healthMedicine : GoodsSubType.goodsHomemade);
                this._typeId =
                    clinicSharedPreferences.getObject(SAVE_INVENTORY_GOODS_TYPE_STATUS)?.typeId ??
                    (userCenter.clinic?.isDrugstoreButler ? GoodsTypeId.healthMedicine : GoodsTypeId.goodsHomemade);
            }
        }

        innerState.goodsInfo = JsonMapper.deserialize(GoodsInfo, {
            barCode: this.innerState.goodsInfo.barCode ?? "",
            dismounting: 0,
            isSell: 1,
            defaultInOutTax: innerState.goodsInfo.defaultInOutTax ? innerState.goodsInfo.defaultInOutTax : 1,
            type: innerState.createDirectoryType, // 新建档案类型
            subType: this._subType, // 读取已选择药品类型缓存
            typeId: this._typeId, // 读取已选择药品子类型缓存
            unitOfAntibiotic: GoodsSubType.medicineWestern || this._subType == GoodsSubType.medicineChinesePatent ? "g" : undefined, // 默认DDD值单位
            pieceUnit: GoodsSubType.medicineChinese ? "g" : undefined, // 中药默认计价单位g
            priceType: SubClinicPricePriceMode.sellingPrice, // 默认定价模式
            eqCoefficient: 1, // 换算当量默认 1
        });
        this.initBusinessScopeList();
    }
    private async _updateGoodsInfo(): Promise<void> {
        if (!!this.innerState.goodsInfo.barCode) {
            const createGoods = await MedicineBaseInfoAgent.getCreateGoodsList({
                key_word: this.innerState.goodsInfo.barCode,
                client: "medicine-code",
            }).catchIgnore();
            if (!!createGoods?.hits?.length) {
                const goodsInfo = this.innerState.goodsInfo;
                const {
                    medicineCadn,
                    tradeName,
                    typeId,
                    manufacturerFull,
                    medicineNmpn,
                    barCode,
                    medicineDosageNum,
                    medicineDosageUnit,
                    medicineDosageFormNum,
                    medicineDosageFormUnit,
                    medicinePackageUnit,
                    medicineSubType,
                    storage,
                    medicineNmpnStartExpiryDate,
                    medicineNmpnEndExpiryDate,
                    businessScopeList,
                } = createGoods?.hits[0];

                goodsInfo.medicineCadn = medicineCadn; // 通用名
                goodsInfo.name = tradeName; // 商品名
                goodsInfo.barCode = barCode; // 条形码
                goodsInfo.typeId = typeId; // 药品类型ID
                goodsInfo.subType = medicineSubType; // 药品类型
                // goodsInfo.customTypeId = customTypeId; // 药品二级分类ID
                // goodsInfo.customTypeName = customTypeName; // 药品二级分类名称
                goodsInfo.medicineNmpn = medicineNmpn; // 国药准字
                goodsInfo.manufacturerFull = manufacturerFull; // 生产厂家
                goodsInfo.medicineDosageNum = medicineDosageNum; // 剂量（成分含量）数量
                goodsInfo.medicineDosageUnit = medicineDosageUnit; // 剂量（成分含量）单位
                goodsInfo.pieceNum = medicineDosageFormNum; // 制剂
                goodsInfo.pieceUnit = medicineDosageFormUnit; // 制剂单位
                goodsInfo.packageUnit = medicinePackageUnit; // 包装
                goodsInfo.storage = storage; // 存储条件
                goodsInfo.medicineNmpnStartExpiryDate = medicineNmpnStartExpiryDate; // 批准文号有效期开始
                goodsInfo.medicineNmpnEndExpiryDate = medicineNmpnEndExpiryDate; // 批准文号有效期结束
                goodsInfo.businessScopeList = businessScopeList; // 经营范围
                this.innerState.useUnit = medicinePackageUnit; // 入库数量单位
                // goodsInfo.packagePrice = packagePrice; // 总价
                // goodsInfo.dismounting = dismounting; // 是否允许拆零
                // goodsInfo.piecePrice = piecePrice; // 拆零销售价格
                // goodsInfo.inTaxRat = inTaxRat; // 进项税率
                // goodsInfo.outTaxRat = outTaxRat; // 销项税率
                // goodsInfo.shortId = shortId; // 药品编码
                // goodsInfo.shebaoNationalCode = shebaoNationalView?.shebaoCode; // 国家码
            }
        }
    }
    private async _initPageConfig(): Promise<void> {
        const innerState = this.innerState;
        innerState.pharmacyInfoConfig = await userCenter.getInventoryChainConfig(false).catchIgnore(); //多药房相关配置
        innerState.list = await InventoryGoodsAgent.getGoodsTagTypes().catchIgnore();
        if (!!innerState.pharmacyInfoConfig) {
            this.innerState.chainReview = innerState.pharmacyInfoConfig?.chainReview;
            this.innerState.priceMode = innerState.pharmacyInfoConfig?.subClinicPrice?.priceMode;
        }
        this.innerState.feeTypesList = ((await GoodsAgent.getGoodsFeeTypeList({ scopeId: 1 }).catchIgnore()) ?? [])
            .filter((item) => InventoryUtils.checkScopeOptionIsInScope(1, Number(item.scopeId)))
            .filter((item) => !item.disable);
        // 调价申请
        const approvalSettingList = await userCenter.getApprovalSettingList(false).catchIgnore();
        this.innerState.isOpenPriceAdjustmentApplication = !!approvalSettingList?.find((item) => item.isOpenAdjustmentPrice);

        this._constructionGoodsInfo();
        this._goodsTypeToDefaultType();
        await this._updateGoodsInfo();
        const westernConfig = WesternMedicineConfigProvider.getConfig();
        innerState.formUnits = westernConfig!.medicineDosageForm!.map((item) => item.name!)!;

        innerState.specifications = ["剂量*制剂/包装", "容量:成分含量*制剂/包装"];

        innerState.inOutTaxList = innerState.pharmacyInfoConfig?.inOutTaxList;
        if (innerState.goodsInfo.typeId && innerState.goodsInfo.defaultInOutTax == 1) {
            const inOutTax = innerState.inOutTaxList?.find((item) => item.typeId == innerState.goodsInfo.typeId);
            innerState.goodsInfo.inTaxRat = inOutTax?.inTaxRat ?? 0;
            innerState.goodsInfo.outTaxRat = inOutTax?.outTaxRat ?? 0;
        }
        // 初始化医保对码栏文本显示
        innerState.defaultMedicalInsuranceCodeStr = "选择医保对码";
        this.innerState.currentPharmacy = InventoryAgent.getCurrentPharmacy();
        // 山东济南--中药饮片（配方饮片、非配方饮片）单位为克、g时，默认单X重量为1
        if (
            userCenter.shebaoConfig?.isShandongJinan &&
            (innerState.goodsInfo?.isMedicineChinesePiece || innerState.goodsInfo?.isMedicineChineseNonFormula) &&
            (innerState.goodsInfo.pieceUnit == "g" || innerState.goodsInfo.pieceUnit == "克")
        ) {
            innerState.goodsInfo.pieceUnitWeight = !!innerState.goodsInfo.pieceUnitWeight ? innerState.goodsInfo.pieceUnitWeight : 1;
        }
    }
    private _initPageTrigger(): void {
        const innerState = this.innerState;
        this._loadCustomTypesTrigger
            .pipe(
                switchMap(() => {
                    return InventoryGoodsAgent.getGoodsSecondaryClassificationCustomTypes(innerState.goodsInfo.typeId!)
                        .catch((error) => new ABCError(error))
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                if (!(rsp instanceof ABCError)) {
                    this.innerState.customTypesList = rsp;
                }
                this.update();
            })
            .addToDisposableBag(this);
    }
    private async *_mapEventInit(/*ignore: _EventInit*/): AsyncGenerator<State> {
        await this._initPageConfig();
        this._initPageTrigger();
        if (this.innerState.goodsInfo.typeId) this._loadCustomTypesTrigger.next();
        this.update();
    }

    private async *_mapEventMedicineSelectSpecification(): AsyncGenerator<State> {
        const innerState = this.innerState;
        const selects = await showOptionsBottomSheet({
            title: "请选择规格",
            options: innerState.specifications,
            optionDescs: ["示例：50mg * 10片 / 盒", "示例： 1ml : 0.5mg * 8支 / 盒"],
            showCloseButton: true,
            showTopRadius: true,
            titlePosition: "center",
            titleStyle: TextStyles.t18MT1,
        });

        if (_.isEmpty(selects)) return;
        innerState.goodsInfo.specType = _.first(selects)!;
        yield innerState.clone();
    }

    private async *_mapEventSelectDosageForm(/*ignore: _EventSelectDosageForm*/): AsyncGenerator<State> {
        const innerState = this.innerState;
        const selects = await showOptionsBottomSheet({
            title: "请选择剂型",
            emptyText: "未设置剂型",
            options: innerState.formUnits,
            showCloseButton: true,
            showTopRadius: true,
            titlePosition: "center",
            titleStyle: TextStyles.t18MT1,
        });
        if (_.isEmpty(selects)) return;
        innerState.goodsInfo.medicineDosageForm = innerState.formUnits![_.first(selects)!];
        yield innerState.clone();
    }

    private async *_mapEventSelectCustomTypesForm(/*ignore: _EventSelectCustomTypesForm*/): AsyncGenerator<State> {
        const innerState = this.innerState;

        const initIndex: Set<number> = new Set();
        const initItem = innerState.customTypesList.findIndex((item) => item.id == innerState.goodsInfo.customTypeId);
        if (!_.isUndefined(initItem) && initItem > -1) {
            initIndex.add(initItem);
        }
        const selects = await showOptionsBottomSheet({
            title: "二级分类",
            emptyText: "未设置二级分类",
            initialSelectIndexes: initIndex,
            options: innerState.customTypesList?.map((item) => item.name!),
            showCloseButton: true,
            showTopRadius: true,
            titlePosition: "center",
            titleStyle: TextStyles.t18MT1,
            clearOnClick: () => {
                if (innerState.goodsInfo.customTypeId !== undefined) {
                    innerState.goodsInfo.customTypeId = undefined;
                    innerState.goodsInfo.customTypeName = undefined;
                }
                ABCNavigator.pop();
                this.update();
            },
            canOnClickClear: !!innerState.goodsInfo.customTypeId,
        });
        if (_.isEmpty(selects)) return;
        innerState.goodsInfo.customTypeId = Number(innerState.customTypesList![_.first(selects)!].id);
        innerState.goodsInfo.customTypeName = innerState.customTypesList![_.first(selects)!].name;
        innerState.hasChanged = true;

        yield innerState.clone();
    }

    // 用药助手(1 西药-药理作用分类  2 中成药-科室分类  3 中药-功效分类
    private async *_mapEventSelectPharmacologic(event: _EventSelectPharmacologic): AsyncGenerator<State> {
        const innerState = this.innerState;

        const selects = await SelectPharmacologicalActionFormDialog.show({
            type: event.type,
            pharmacologicId: innerState.goodsInfo.pharmacologicId,
        });
        if (_.isEmpty(selects)) return;
        innerState.goodsInfo.pharmacologicId = selects.subTypeId;
        innerState.goodsInfo.pharmacologicsName = `${selects.sourceTypeName}/${selects.subTypeName}`;
        innerState.hasChanged = true;

        yield innerState.clone();
    }

    private async *_mapEventSelectPharmaceuticalForm(/*ignore: _EventSelectPharmaceuticalForm*/): AsyncGenerator<State> {
        const innerState = this.innerState;
        //  南京、南宁-中药类型-剂型
        const isChineseMedicineDosage =
            (userCenter.shebaoConfig?.isRegionNanjing || userCenter.shebaoConfig?.isRegionNanning) &&
            innerState.goodsInfo?.isChineseMedicine;
        const selectId: string = await showBottomPanel(
            <InventoryGoodsSelectDosageDialog
                selectId={innerState.goodsInfo.dosageFormType}
                isChineseMedicineDosage={isChineseMedicineDosage}
            />,
            { topMaskHeight: pxToDp(315) }
        );
        if (!selectId) return;
        innerState.goodsInfo.dosageFormType = selectId;

        innerState.hasChanged = true;

        yield innerState.clone();
    }

    private async *_mapEventSelectOTCForm(/*ignore: _EventSelectOTCForm*/): AsyncGenerator<State> {
        const innerState = this.innerState;

        const initIndex: Set<number> = new Set();
        const otcList = [
            { otcType: OtcTypeEnum.prescription, OtcTypeName: "处方药" },
            { otcType: OtcTypeEnum.CLASS_A_OTC, OtcTypeName: "甲类非处方药" },
            { otcType: OtcTypeEnum.CLASS_B_OTC, OtcTypeName: "乙类非处方药" },
        ];
        const initItem = otcList.findIndex((item) => item.otcType == innerState.goodsInfo.otcType);
        if (!_.isUndefined(initItem) && initItem > -1) {
            initIndex.add(initItem);
        }
        const selects = await showOptionsBottomSheet({
            title: "处方/OTC",
            emptyText: "请选择处方OTC",
            initialSelectIndexes: initIndex,
            options: otcList.map((item) => item.OtcTypeName),
            showCloseButton: true,
            showTopRadius: true,
            titlePosition: "center",
            titleStyle: TextStyles.t18MT1,
        });

        if (_.isEmpty(selects)) return;
        const selectedOtcType = otcList[selects![0]].otcType;

        innerState.goodsInfo.otcType = selectedOtcType;
        innerState.hasChanged = true;
        this.dispatch(new _EventScrollToFocusItemEvent());
    }

    private async *_mapEventSelectNarcoticsDangerousDrugsRelease(/*ignore: _EventSelectNarcoticsDangerousDrugsRelease*/): AsyncGenerator<State> {
        const innerState = this.innerState;
        const initIndexs: Set<number> = new Set();

        const ingredientList = [
            { label: "精I", ingredient: IngredientEnum.JING_1 },
            { label: "精II", ingredient: IngredientEnum.JING_2 },
            { label: "麻", ingredient: IngredientEnum.MA },
            { label: "毒", ingredient: IngredientEnum.DU },
            { label: "放", ingredient: IngredientEnum.FANG },
            { label: "黄麻碱", ingredient: IngredientEnum.MA_HUANG_JIAN },
        ];

        for (const dangerIngredient of innerState.goodsInfo.getIngredientArray ?? []) {
            const initItem = ingredientList.findIndex((item) => item.ingredient == dangerIngredient);
            if (!_.isUndefined(initItem) && initItem > -1) {
                initIndexs.add(initItem);
            }
        }

        const selects = await showNarcoticsDangerousDrugsReleaseSheet({
            title: "选择精麻毒放",
            options: ingredientList?.map((item) => item.label),
            initialSelectIndexes: initIndexs,
            crossAxisCount: 4,
            height: pxToDp(375),
            multiSelect: true,
            showCloseButton: true,
            showConfirmBtn: true,
        });

        if (_.isEmpty(selects)) return;

        const _dangerIngredient: number[] = [];

        // 当选择“精一”后再选择“精二”时，会先删除已选中的“精二”，然后添加新选中的“精二”。同样选择“精一”同理。
        for (const index of selects!) {
            const ingredient = ingredientList[index].ingredient;
            if (ingredient === IngredientEnum.JING_1) {
                // Remove "精二" if it exists
                const jing2Index = _dangerIngredient.indexOf(IngredientEnum.JING_2);
                if (jing2Index > -1) {
                    _dangerIngredient.splice(jing2Index, 1);
                }
            } else if (ingredient === IngredientEnum.JING_2) {
                // Remove "精一" if it exists
                const jing1Index = _dangerIngredient.indexOf(IngredientEnum.JING_1);
                if (jing1Index > -1) {
                    _dangerIngredient.splice(jing1Index, 1);
                }
            }

            _dangerIngredient.push(ingredient);
        }

        innerState.goodsInfo.dangerIngredient = innerState.goodsInfo.setIngredientArray(_dangerIngredient);

        innerState.hasChanged = true;

        yield innerState.clone();
    }

    private async *_mapEventSelectAntibiotics(/*ignore: _EventSelectAntibiotics*/): AsyncGenerator<State> {
        const innerState = this.innerState;

        const initIndex: Set<number> = new Set();
        const antibioticsList = [
            { antibiotic: AntibioticEnum.no, title: "非限制使用级" },
            { antibiotic: AntibioticEnum.yes, title: "限制使用级" },
            { antibiotic: AntibioticEnum.special, title: "特殊使用级" },
        ];
        const initItem = antibioticsList.findIndex((item) => item.antibiotic == innerState.goodsInfo.antibiotic);
        if (!_.isUndefined(initItem) && initItem > -1) {
            initIndex.add(initItem);
        }
        const selects = await showOptionsBottomSheet({
            title: "抗菌药物级别",
            emptyText: "选择抗菌级别",
            initialSelectIndexes: initIndex,
            options: antibioticsList?.map((item) => item.title!),
            showCloseButton: true,
            showTopRadius: true,
            titlePosition: "center",
            titleStyle: TextStyles.t18MT1,
            clearOnClick: () => {
                if (innerState.goodsInfo.antibiotic !== undefined) {
                    innerState.goodsInfo.antibiotic = undefined;
                    ABCNavigator.pop();
                }
                this.update();
            },
            canOnClickClear: !!innerState.goodsInfo.antibiotic,
        });
        if (_.isEmpty(selects)) return;
        const selectedAntibiotic = antibioticsList[selects![0]].antibiotic;
        innerState.goodsInfo.antibiotic = selectedAntibiotic;
        innerState.hasChanged = true;

        yield innerState.clone();
    }

    private async *_mapEventUpdateDefinedDailyDose(event: _EventUpdateDefinedDailyDose): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.goodsInfo.dddOfAntibiotic = event.dddOfAntibiotic;
        innerState.goodsInfo.unitOfAntibiotic = event.unitOfAntibiotic;
        innerState.hasChanged = true;

        yield innerState.clone();
    }

    private async *_mapEventSelectBaseMedicineType(/*ignore: _EventSelectBaseMedicineType*/): AsyncGenerator<State> {
        const innerState = this.innerState;

        const initIndex: Set<number> = new Set();
        const baseMedicineTypeList = [
            { baseMedicineType: BaseMedicineTypeEnum.national, title: "国家基药" },
            { baseMedicineType: BaseMedicineTypeEnum.landmarkBased, title: "地标基药" },
        ];
        const initItem = baseMedicineTypeList.findIndex((item) => item.baseMedicineType == innerState.goodsInfo.baseMedicineType);
        if (!_.isUndefined(initItem) && initItem > -1) {
            initIndex.add(initItem);
        }
        const selects = await showOptionsBottomSheet({
            title: "基药",
            emptyText: "选择基药",
            initialSelectIndexes: initIndex,
            options: baseMedicineTypeList?.map((item) => item.title!),
            showCloseButton: true,
            showTopRadius: true,
            titlePosition: "center",
            titleStyle: TextStyles.t18MT1,
            clearOnClick: () => {
                if (innerState.goodsInfo.baseMedicineType !== undefined) {
                    innerState.goodsInfo.baseMedicineType = undefined;
                    ABCNavigator.pop();
                }
                this.update();
            },
            canOnClickClear: !!innerState.goodsInfo.baseMedicineType,
        });
        if (_.isEmpty(selects)) return;
        const selectedBaseMedicineType = baseMedicineTypeList[selects![0]].baseMedicineType;
        innerState.goodsInfo.baseMedicineType = selectedBaseMedicineType;
        innerState.hasChanged = true;

        yield innerState.clone();
    }

    private async *_mapEventSelectMedicalMaterialDeviceType(/*ignore: _EventSelectMedicalMaterialDeviceType*/): AsyncGenerator<State> {
        const innerState = this.innerState;

        const initIndex: Set<number> = new Set();
        const deviceTypeList = [
            { deviceType: MedicalMaterialDeviceType.LEVEL_A, title: "一类器械耗材" },
            { deviceType: MedicalMaterialDeviceType.LEVEL_B, title: "二类器械耗材" },
            { deviceType: MedicalMaterialDeviceType.LEVEL_C, title: "三类器械耗材" },
        ];
        const initItem = deviceTypeList.findIndex((item) => item.deviceType == innerState.goodsInfo.deviceType);
        if (!_.isUndefined(initItem) && initItem > -1) {
            initIndex.add(initItem);
        }
        const selects = await showOptionsBottomSheet({
            title: "器械耗材分类",
            emptyText: "选择器械耗材分类",
            initialSelectIndexes: initIndex,
            options: deviceTypeList?.map((item) => item.title!),
            showCloseButton: true,
            showTopRadius: true,
            titlePosition: "center",
            titleStyle: TextStyles.t18MT1,
            clearOnClick: () => {
                if (innerState.goodsInfo.deviceType !== undefined) {
                    innerState.goodsInfo.deviceType = undefined;
                    ABCNavigator.pop();
                }
                this.update();
            },
            canOnClickClear: !!innerState.goodsInfo.deviceType,
        });
        if (_.isEmpty(selects)) return;
        const selectedDeviceType = deviceTypeList[selects![0]].deviceType;
        innerState.goodsInfo.deviceType = selectedDeviceType;

        this.update();
    }

    private async *_mapEventSelectMaintenanceClassification(/*ignore: _EventSelectMaintenanceClassification*/): AsyncGenerator<State> {
        const innerState = this.innerState;

        const initIndex: Set<number> = new Set();
        const maintenanceClassificationList = [
            { maintainType: MaintainTypeEnum.NORMAL, maintainTypeName: "普通养护" },
            { maintainType: MaintainTypeEnum.VIP, maintainTypeName: "重点养护" },
            { maintainType: MaintainTypeEnum.NO, maintainTypeName: "无需养护" },
        ];
        const initItem = maintenanceClassificationList.findIndex((item) => item.maintainType == innerState.goodsInfo.maintainType);
        if (!_.isUndefined(initItem) && initItem > -1) {
            initIndex.add(initItem);
        }
        const selects = await showOptionsBottomSheet({
            title: "养护分类",
            showCloseButton: true,
            initialSelectIndexes: initIndex,
            options: maintenanceClassificationList.map((item) => item.maintainTypeName),
            showTopRadius: true,
            titlePosition: "center",
            titleStyle: TextStyles.t18MT1,
            clearOnClick: () => {
                if (innerState.goodsInfo.maintainType !== undefined) {
                    innerState.goodsInfo.maintainType = undefined;
                    ABCNavigator.pop();
                }
                this.update();
            },
            canOnClickClear: !!innerState.goodsInfo.maintainType,
        });
        if (_.isEmpty(selects)) return;
        const selectedMaintainType = _.first(selects?.map((index) => maintenanceClassificationList[index].maintainType));
        innerState.goodsInfo.maintainType = selectedMaintainType;
        innerState.hasChanged = true;

        yield innerState.clone();
    }

    private async *_mapEventSelectStorageConditions(/*ignore: _EventSelectStorageConditions*/): AsyncGenerator<State> {
        const innerState = this.innerState;
        const result = await InventoryStorageConditionDialog.show({ storage: innerState.goodsInfo.storage });
        if (!result) return;
        innerState.goodsInfo.storage = result;
        innerState.hasChanged = true;

        yield innerState.clone();
    }

    private async *_mapEventSelectExpirationDate(event: _EventSelectExpirationDate): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.goodsInfo.shelfLife = event.monthNum;
        innerState.hasChanged = true;

        yield innerState.clone();
    }

    private async *_mapEventSelectSelectTag(/*ignore: _EventSelectTag*/): AsyncGenerator<State> {
        const innerState = this.innerState;
        const selectTags = await InventoryInTagDialog.show({
            selectTagList: innerState.goodsInfo.goodsTagList,
            list: innerState.list,
        });

        if (!selectTags) return;
        innerState.goodsInfo.goodsTagList = selectTags.selectTagList;
        innerState.goodsInfo.goodsTagIdList = (innerState.goodsInfo.goodsTagList ?? [])?.map((item) => item.tagId ?? "");
        innerState.hasChanged = true;
        yield innerState.clone();
    }

    private async *_mapEventModifyPosition(event: _EventModifyPosition): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.goodsInfo.position = event.positionNum;
        this.update();
    }

    private async *_mapEventSelectPriceType(/*ignore: _EventSelectPriceType*/): AsyncGenerator<State> {
        const innerState = this.innerState;
        const priceMode = innerState.goodsInfo.priceType;
        const initIndex: Set<number> = new Set();
        const pricingModelList = [
            { priceType: SubClinicPricePriceMode.purchaseMarkup, priceTypeName: "进价加成" },
            { priceType: SubClinicPricePriceMode.sellingPrice, priceTypeName: "固定售价" },
        ];
        const initItem = pricingModelList.findIndex((item) => item.priceType == priceMode);
        if (!_.isUndefined(initItem) && initItem > -1) {
            initIndex.add(initItem);
        }
        const selects = await showOptionsBottomSheet({
            title: "定价模式",
            initialSelectIndexes: initIndex,
            options: pricingModelList.map((item) => item.priceTypeName),
            showCloseButton: true,
            showTopRadius: true,
            titlePosition: "center",
            titleStyle: TextStyles.t18MT1,
        });
        if (_.isEmpty(selects)) return;
        const selectedPricingModel = _.first(selects?.map((index) => pricingModelList[index]));

        innerState.goodsInfo.priceType = selectedPricingModel?.priceType;
        if (innerState.goodsInfo.priceType == SubClinicPricePriceMode.purchaseMarkup) {
            innerState.goodsInfo.packagePrice = undefined;
            innerState.goodsInfo.piecePrice = undefined;
        }
        innerState.focusKey = innerState.priceTypeKey;
        innerState.hasChanged = true;
        this.dispatch(new _EventScrollToFocusItemEvent());
        // yield innerState.clone();
    }

    private async *_mapEventSelectMarkupRate(event: _EventSelectMarkupRate): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.goodsInfo.priceMakeupPercent = event.markupRate;
        innerState.hasChanged = true;
        yield innerState.clone();
    }

    // 暂时注释掉这个方法，这个profitCategoryType的类型变化了，这个方法也没有调用，所以没有影响
    // private async *_mapEventSelectProfitCategorization(/*event: _EventSelectProfitCategorization*/): AsyncGenerator<State> {
    // const innerState = this.innerState;
    //
    // const initIndex: Set<number> = new Set();
    // // 毛利类型列表
    // const storageConditionsList = [
    //     { profitCategoryType: ProfitCategoryTypeEnum.high, title: "异常高毛利", content: "120%以上（含）" },
    //     { profitCategoryType: ProfitCategoryTypeEnum.leve_A, title: "A类", content: "90%以上（含）~ 120% " },
    //     { profitCategoryType: ProfitCategoryTypeEnum.leve_B, title: "B类", content: "60%以上（含）~ 90%" },
    //     { profitCategoryType: ProfitCategoryTypeEnum.leve_C, title: "C类", content: "30%以上（含）~ 60%" },
    //     { profitCategoryType: ProfitCategoryTypeEnum.leve_D, title: "D类", content: "30%以下" },
    //     { profitCategoryType: ProfitCategoryTypeEnum.leve_E, title: "E类", content: "0%以下" },
    // ];
    //
    // const initItem = storageConditionsList.findIndex((item) => item.profitCategoryType == innerState.goodsInfo.profitCategoryType);
    // if (!_.isUndefined(initItem) && initItem > -1) {
    //     initIndex.add(initItem);
    // }
    // const selects = await showOptionsBottomSheet({
    //     title: "利润分类",
    //     titlePosition: "center",
    //     showTopRadius: true,
    //     showCloseButton: true,
    //     titleStyle: TextStyles.t18MT1,
    //     options: storageConditionsList.map((item) => item.title),
    //     optionsWidgets: storageConditionsList.map((item, index) => (
    //         <AbcView
    //             key={index}
    //             style={[
    //                 ABCStyles.rowAlignCenter,
    //                 {
    //                     flex: 1,
    //                     paddingVertical: Sizes.dp16,
    //                     paddingHorizontal: Sizes.dp24,
    //                     backgroundColor: Colors.S2,
    //                 },
    //             ]}
    //         >
    //             <Text style={[TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp22 }), { textAlign: "left" }]} numberOfLines={1}>
    //                 {item.title ?? ""}
    //             </Text>
    //             <Text
    //                 style={[
    //                     TextStyles.t16NT3.copyWith({ lineHeight: Sizes.dp22, color: Colors.t3 }),
    //                     { textAlign: "left", marginLeft: Sizes.dp9 },
    //                 ]}
    //                 numberOfLines={1}
    //             >
    //                 {item.content ?? ""}
    //             </Text>
    //         </AbcView>
    //     )),
    //     initialSelectIndexes: initIndex,
    // });
    // if (_.isEmpty(selects)) return;
    // const selectedProfitCategoryType = _.first(selects?.map((index) => storageConditionsList[index].profitCategoryType));
    // innerState.goodsInfo.profitCategoryType = selectedProfitCategoryType;
    // innerState.hasChanged = true;
    // yield innerState.clone();
    // }

    private async *_mapEventUpdatePackagePrice(event: _EventUpdatePackagePrice): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.goodsInfo.packagePrice = event.price;
        innerState.hasChanged = true;

        this._calculatePiecePrice();
        yield innerState.clone();
    }

    private async *_mapEventUpdateDismounting(event: _EventUpdateDismounting): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.goodsInfo.dismounting = event.dismounting;
        innerState.hasChanged = true;

        yield innerState.clone();
    }

    private _replaceChineseHerbalSlice(item: any, parentName?: string): void {
        // 替换当前节点
        ["name", "displayName", "_displayName"].forEach((key) => {
            if (item[key]?.includes("中药饮片")) {
                item[key] = item[key].replace(/中药饮片/g, "配方饮片");
            }
        });

        // 拼接 _displayName（如果有父级）
        if (parentName) {
            item._displayName = `${parentName}/${item.name}`;
        } else {
            item._displayName = item.name;
        }

        // 递归处理所有子节点
        if (Array.isArray(item.children)) {
            item.children.forEach((child: any) => {
                this._replaceChineseHerbalSlice(child, item.name);
            });
        }
    }
    /**
     * 拼接父级所属经营范围的名称
     */
    private _combineBusinessScopeName(businessScopeList?: BusinessScopeList[]): void {
        if (userCenter.clinic?.isDrugstoreButler) {
            businessScopeList?.forEach((item) => {
                this._replaceChineseHerbalSlice(item);
            });
        } else {
            businessScopeList?.forEach((item) => {
                item._displayName = item.name;
                item.children?.forEach((child: BusinessScopeList) => {
                    child._displayName = `${item.name}/${child.name}`;
                });
            });
        }
    }

    private initBusinessScopeList(): void {
        const innerState = this.innerState;
        const goodsInfo = innerState.goodsInfo;
        // 药店-如果选择了配方饮片，那么所属经营范围默认选中【配方饮片/含配方】，如果选择了非配方饮片，默认选中【配方饮片/不含配方】
        if (userCenter.clinic?.isDrugstoreButler) {
            const businessScopeList = userCenter.clinicDictionaryInfo?.businessScopeDict?.goodsBusinessScopeList;
            this._combineBusinessScopeName(businessScopeList);
            if (goodsInfo.typeId == GoodsTypeId.medicineChinesePiece) {
                const parent = businessScopeList?.find((t) => t.id == "4");
                goodsInfo.businessScopeList = parent?.children?.filter((k) => k.id === "4000");
                // 配方饮片默认选择处方OTC为【处方药】
                goodsInfo.otcType = OtcTypeEnum.prescription;
            } else if (goodsInfo.typeId == GoodsTypeId.medicineChineseNonFormula) {
                const parent = businessScopeList?.find((t) => t.id == "4");
                goodsInfo.businessScopeList = parent?.children?.filter((k) => k.id === "4001");
                goodsInfo.otcType = undefined;
            } else {
                goodsInfo.businessScopeList = undefined;
                goodsInfo.otcType = undefined;
            }
        }
    }

    // 选择药品类型
    private async *_mapEventUpdateMedicineGoodsTypeId(/*event: _EventUpdateMedicineGoodsTypeId*/): AsyncGenerator<State> {
        const innerState = this.innerState;
        const goodsInfo = innerState.goodsInfo;
        let medicineTypeList: Array<{ name: string; subType: number; typeId: number }> = [];
        let selectsTitleStr = "";

        switch (innerState.createDirectoryType) {
            case CreateDirectoryTypeEnum.CreateMedicine: {
                selectsTitleStr = "药品";
                medicineTypeList = userCenter.clinic?.isDrugstoreButler ? DrugClassifyList.slice(0, -1) : DrugClassifyList;
                if (userCenter.clinic?.isDrugstoreButler) {
                    medicineTypeList.push({
                        name: "非配方饮片",
                        subType: GoodsSubType.medicineChinese,
                        typeId: GoodsTypeId.medicineChineseNonFormula,
                    });
                }
                break;
            }
            case CreateDirectoryTypeEnum.CreateSupplies: {
                selectsTitleStr = "物资";
                medicineTypeList = userCenter.clinic?.isDrugstoreButler
                    ? MaterialClassifyList.slice(0, 2)
                    : MaterialClassifyList.filter((item) => !(item?.onlyDrugstoreButler || false));
                break;
            }
            case CreateDirectoryTypeEnum.CreateGoods: {
                selectsTitleStr = "商品";
                medicineTypeList = userCenter.clinic?.isDrugstoreButler
                    ? GoodsClassifyList.slice(1)
                    : GoodsClassifyList.filter((item) => !(item?.onlyDrugstoreButler || false));
            }
        }
        const initIndex = medicineTypeList?.findIndex((t) => t.name == goodsInfo?.displayTypeName) ?? -1;

        const selects = await showOptionsBottomSheet({
            title: `请选择${selectsTitleStr}类型`,
            options: medicineTypeList?.map((item) => item.name),
            showCloseButton: true,
            showTopRadius: true,
            titlePosition: "center",
            titleStyle: TextStyles.t18MT1,
            initialSelectIndexes: new Set<number>([initIndex]),
        });
        if (_.isEmpty(selects)) return;
        let _changedTypeId;
        let _changedSubType;
        goodsInfo.pharmacologicId = undefined; // 清空药理作用分类
        goodsInfo.pharmacologicsName = undefined; // 清空药理作用分类名
        if (!!selects?.length) {
            _changedSubType = medicineTypeList[selects[0]]?.subType;
            _changedTypeId = medicineTypeList[selects[0]]?.typeId;
        }

        goodsInfo.subType = _changedSubType;
        goodsInfo.typeId = _changedTypeId;

        if (
            !(
                goodsInfo.typeId == GoodsTypeId.medicineChinesePiece ||
                goodsInfo.typeId == GoodsTypeId.medicineChineseGranule ||
                goodsInfo.typeId == GoodsTypeId.medicineChineseNonFormula
            )
        ) {
            goodsInfo.pieceUnit = undefined; // 选择非中药类型清除计价单位
        } else if (
            (goodsInfo.typeId == GoodsTypeId.medicineChinesePiece || goodsInfo.typeId == GoodsTypeId.medicineChineseGranule) &&
            !goodsInfo.pieceUnit
        ) {
            goodsInfo.pieceUnit = "g"; // 中药类型计价单位默认单位为g
        }

        // 山东济南--中药饮片（配方饮片、非配方饮片）单位为克、g时，默认单X重量为1
        if (
            userCenter.shebaoConfig?.isShandongJinan &&
            (goodsInfo?.isMedicineChinesePiece || goodsInfo?.isMedicineChineseNonFormula) &&
            ["g", "克"].includes(goodsInfo?.pieceUnit ?? "")
        ) {
            goodsInfo.pieceUnitWeight = !!goodsInfo.pieceUnitWeight ? goodsInfo.pieceUnitWeight : 1;
        } else {
            goodsInfo.pieceUnitWeight = undefined;
        }

        this._goodsTypeToDefaultType();
        this.initBusinessScopeList();

        if (goodsInfo.typeId) this._loadCustomTypesTrigger.next();

        if (goodsInfo.defaultInOutTax == 1) {
            const inOutTax = innerState.inOutTaxList?.find((item) => item.typeId == innerState.goodsInfo.typeId);
            goodsInfo.inTaxRat = inOutTax?.inTaxRat ?? 0;
            goodsInfo.outTaxRat = inOutTax?.outTaxRat ?? 0;
        }

        switch (innerState.createDirectoryType) {
            case CreateDirectoryTypeEnum.CreateMedicine: {
                clinicSharedPreferences.setObject(SAVE_INVENTORY_MEDICINE_TYPE_STATUS, {
                    subType: goodsInfo.subType,
                    typeId: goodsInfo.typeId,
                });
                break;
            }
            case CreateDirectoryTypeEnum.CreateSupplies: {
                clinicSharedPreferences.setObject(SAVE_INVENTORY_MATERIAL_TYPE_STATUS, {
                    subType: goodsInfo.subType,
                    typeId: goodsInfo.typeId,
                });
                break;
            }
            case CreateDirectoryTypeEnum.CreateGoods: {
                clinicSharedPreferences.setObject(SAVE_INVENTORY_GOODS_TYPE_STATUS, {
                    subType: goodsInfo.subType,
                    typeId: goodsInfo.typeId,
                });
            }
        }

        innerState.hasChanged = true;
        yield innerState.clone();
    }

    private async *_mapEventModifyGoodsInfoFeeType(): AsyncGenerator<State> {
        const state = this.innerState;
        const initIndex: Set<number> = new Set();
        const initItem = state.feeTypesList.findIndex((item) => item.feeTypeId == state.goodsInfo.feeTypeId);
        if (!_.isUndefined(initItem) && initItem > -1) {
            initIndex.add(initItem);
        }
        const result = await showOptionsBottomSheet({
            title: "请选择费用类型",
            emptyText: "未设置费用类型",
            initialSelectIndexes: initIndex,
            options: state.feeTypesList?.map((item) => item.name!),
            showCloseButton: true,
            showTopRadius: true,
            titlePosition: "center",
            titleStyle: TextStyles.t18MT1,
        });
        if (result && result.length) {
            state.goodsInfo.feeTypeId = state.feeTypesList[result[0]].feeTypeId;
            state.goodsInfo.feeTypeName = state.feeTypesList[result[0]].name;
            this.update();
        }
        this.update();
    }

    private async *_mapEventUpdateIsSell(event: _EventUpdateIsSell): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.goodsInfo.isSell = event.isSell;

        if (event.isSell == 0) {
            innerState.goodsInfo.dismounting = event.isSell; // 当不允许对外销售时需将是否拆零也设为不允许
            innerState.goodsInfo.packagePrice = undefined; // 当不允许对外销售时需将包装价清空
        }
        innerState.hasChanged = true;

        yield innerState.clone();
    }

    private async *_mapEventUpdateInOutTax(event: _EventUpdateInOutTax): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.goodsInfo.defaultInOutTax = event.defaultInOutTax;

        if (innerState.goodsInfo.typeId && innerState.goodsInfo.defaultInOutTax == 1) {
            const inOutTax = innerState.inOutTaxList?.find((item) => item.typeId == innerState.goodsInfo.typeId);
            innerState.goodsInfo.inTaxRat = inOutTax?.inTaxRat ?? 0;
            innerState.goodsInfo.outTaxRat = inOutTax?.outTaxRat ?? 0;
        }

        innerState.hasChanged = true;

        yield innerState.clone();
    }

    private async *_mapEventUpdatePiecePrice(event: _EventUpdatePiecePrice): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.goodsInfo.piecePrice = event.price;
        innerState.hasChanged = true;

        yield innerState.clone();
    }

    private async *_mapEventScrollToFocusItemEvent(/*ignore: _EventScrollToFocusItemEvent*/): AsyncGenerator<State> {
        yield ScrollToFocusItemState.fromState(this.innerState);
    }

    private async *_mapEventSelectComponentUnit(/*ignore: _EventSelectDosageUnit*/): AsyncGenerator<State> {
        const innerState = this.innerState;
        const westernConfig = WesternMedicineConfigProvider.getConfig();
        const units = westernConfig?.dosageUnit?.map((item) => item.name!) ?? [];
        const selects = await AbcDialog.showOptionsBottomSheet({
            title: "选择单位",
            options: units!,
            initialSelectIndexes: new Set([units!.indexOf(innerState.goodsInfo.componentContentUnit!)]),
            height: pxToDp(400),
            crossAxisCount: 4,
        });
        if (_.isEmpty(selects)) return;

        innerState.goodsInfo.componentContentUnit = units[_.first(selects)!];
        innerState.hasChanged = true;
        yield innerState.clone();
    }

    private async *_mapEventSelectDosageUnit(/*ignore: _EventSelectDosageUnit*/): AsyncGenerator<State> {
        const innerState = this.innerState;
        const westernConfig = WesternMedicineConfigProvider.getConfig();
        const units = westernConfig?.dosageUnit?.map((item) => item.name!) ?? [];
        const selects = await AbcDialog.showOptionsBottomSheet({
            title: "选择单位",
            options: units!,
            initialSelectIndexes: new Set([units!.indexOf(innerState.goodsInfo.medicineDosageUnit!)]),
            height: pxToDp(400),
            crossAxisCount: 4,
        });
        if (_.isEmpty(selects)) return;

        innerState.goodsInfo.medicineDosageUnit = units[_.first(selects)!];
        innerState.hasChanged = true;
        yield innerState.clone();
    }

    private async *_mapEventSelectPieceUnit(/*ignore: _EventSelectPieceUnit*/): AsyncGenerator<State> {
        const innerState = this.innerState;

        let title = "选择计价单位";
        let units = WesternMedicine.materialUnit?.map((item) => item.name!) ?? [];
        const goodsInfo = innerState.goodsInfo;

        // 中药单位
        if (goodsInfo.isChineseMedicine) {
            units = WesternMedicine.medicinePricingUnit?.map((item) => item.name!) ?? [];
        }

        switch (innerState.createDirectoryType) {
            case CreateDirectoryTypeEnum.CreateSupplies:
                title = "选择最小单位";
                units = WesternMedicine.materialAndCommodityUnit?.map((item) => item.name!) ?? [];
                break;
            case CreateDirectoryTypeEnum.CreateGoods:
                title = "选择最小单位";
                units = WesternMedicine.materialAndCommodityUnit?.map((item) => item.name!) ?? [];
        }
        const selects = await AbcDialog.showOptionsBottomSheet({
            title: title,
            options: units,
            initialSelectIndexes: new Set([units.indexOf(innerState.goodsInfo.pieceUnit!)]),
            crossAxisCount: 4,
            height: pxToDp(500),
        });
        if (_.isEmpty(selects)) return;

        innerState.goodsInfo.pieceUnit = units[_.first(selects)!];
        if (!["g", "克"].includes(innerState.goodsInfo.pieceUnit)) {
            innerState.goodsInfo.eqCoefficient = 1;
        }

        innerState.useUnit = innerState.goodsInfo?.unitPreferPackage;
        innerState.hasChanged = true;

        // 山东济南-中药饮片（配方饮片、非配方饮片），如果pieceUnit为g、克，pieceUnitWeight默认为1
        if (
            // userCenter.shebaoConfig?.isShandongJinan &&
            (goodsInfo.isMedicineChinesePiece || goodsInfo?.isMedicineChineseNonFormula) &&
            ["g", "克"].includes(goodsInfo?.pieceUnit ?? "")
        ) {
            goodsInfo.pieceUnitWeight = !!goodsInfo?.pieceUnitWeight ? goodsInfo?.pieceUnitWeight : 1;
        }

        yield innerState.clone();
    }

    private async *_mapEventSelectPackageUnit(/*ignore: _EventSelectPackageUnit*/): AsyncGenerator<State> {
        const innerState = this.innerState;
        const westernConfig = WesternMedicineConfigProvider.getConfig();
        let units = westernConfig.materialUnit!.map((item) => item.name!);

        switch (innerState.createDirectoryType) {
            case CreateDirectoryTypeEnum.CreateSupplies:
                units = WesternMedicine.materialAndCommodityUnit?.map((item) => item.name!) ?? [];
                break;
            case CreateDirectoryTypeEnum.CreateGoods:
                units = WesternMedicine.materialAndCommodityUnit?.map((item) => item.name!) ?? [];
        }
        const selects = await AbcDialog.showOptionsBottomSheet({
            title: "选择单位",
            options: units,
            initialSelectIndexes: new Set([units.indexOf(innerState.goodsInfo.packageUnit!)]),
            height: pxToDp(400),
            crossAxisCount: 4,
        });
        if (_.isEmpty(selects)) return;

        innerState.goodsInfo.packageUnit = units[_.first(selects)!];

        innerState.useUnit = innerState.goodsInfo?.unitPreferPackage;
        innerState.hasChanged = true;

        yield innerState.clone();
    }

    private async *_mapEventUpdatePieceNum(event: _EventUpdatePieceNum): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.goodsInfo.pieceNum = event.num;
        innerState.hasChanged = true;
        this._calculatePiecePrice();
        yield innerState.clone();
    }
    private async *mapEventUpdateEqCoefficient(event: _EventUpdateEqCoefficient): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.goodsInfo.eqCoefficient = event.val;
        innerState.hasChanged = true;
        yield innerState.clone();
    }

    private async *_mapEventUpdateBarCode(event: _EventUpdateBarCode): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.goodsInfo.barCode = event.barCode;
        innerState.hasChanged = true;
        yield innerState.clone();
    }

    private async *_mapEventScanQRCode(/*event: _EventScanQRCode*/): AsyncGenerator<State> {
        const innerState = this.innerState;
        BarcodeScanner.scan().then(async (barcode) => {
            if (!barcode?.length) return;
            MedicineBaseInfoAgent.getGoodsSearchDomainMedicineCreate({ keywords: barcode, type: innerState.createDirectoryType }).then(
                (rsp) => {
                    if (rsp.list?.length) {
                        const goodsInfo = innerState.goodsInfo;
                        const {
                            //基础
                            medicineCadn,
                            typeId,
                            subType,
                            pharmacologicId,
                            pharmacologicFullName,
                            name,
                            manufacturerFull,
                            medicineNmpn,
                            barCode,
                            packageUnit,
                            pieceNum,
                            pieceUnit,
                            //扩展
                            otcType,
                            dangerIngredient,
                            antibiotics,
                            dddOfAntibiotics,
                            unitOfAntibiotics,
                            shelfLife,
                            baseMedicineType,
                            mha,
                            maintainType,
                            storageType,
                            dosageFormType,
                            businessScopes,
                        } = rsp.list[0];

                        // 基础
                        goodsInfo.medicineCadn = medicineCadn; // 通用名
                        goodsInfo.typeId = typeId; // 药品类型ID
                        goodsInfo.subType = subType; // 药品类型
                        goodsInfo.pharmacologicId = pharmacologicId; // 药理作用分类ID
                        goodsInfo.pharmacologicsName = pharmacologicFullName; // 药理作用分类名
                        goodsInfo.name = name; // 商品名
                        goodsInfo.manufacturerFull = manufacturerFull; // 生产厂家
                        goodsInfo.medicineNmpn = medicineNmpn; // 批准文号
                        goodsInfo.barCode = barCode; // 条形码
                        goodsInfo.pieceNum = pieceNum; // 制剂
                        goodsInfo.pieceUnit = pieceUnit; // 制剂单位
                        goodsInfo.packageUnit = packageUnit; // 包装
                        //扩展
                        goodsInfo.otcType = otcType; // 处方药/OTC
                        goodsInfo.dangerIngredient = dangerIngredient; // 精麻毒放
                        goodsInfo.antibiotic = antibiotics; // 抗菌级别
                        goodsInfo.dddOfAntibiotic = dddOfAntibiotics; // DDD值
                        goodsInfo.unitOfAntibiotic = unitOfAntibiotics; // DDD单位
                        goodsInfo.baseMedicineType = baseMedicineType; // 基药
                        goodsInfo.mha = mha; // 上市许可持有人
                        goodsInfo.maintainType = maintainType; // 养护分类
                        goodsInfo.storageType = storageType; // 存储条件
                        goodsInfo.shelfLife = shelfLife; // 保质期
                        goodsInfo.dosageFormType = dosageFormType; // 剂型
                        goodsInfo.businessScopeList = businessScopes; //所属经营范围

                        if (goodsInfo.typeId) this._loadCustomTypesTrigger.next();
                    } else {
                        innerState.goodsInfo.barCode = barcode;
                    }
                    innerState.hasChanged = true;
                    this.update();
                }
            );
        });
    }

    async _createMedicine(): Promise<GoodsInfo | undefined> {
        let goodsInfo: GoodsInfo | undefined = undefined;
        let error: any;
        this.innerState.isUploading = true;
        try {
            goodsInfo = await this.requestCreateMedicine();
        } catch (e) {
            error = e;
        }
        this.innerState.isUploading = false;
        if (goodsInfo) {
            return goodsInfo;
        }

        await showConfirmDialog("", "添加药品失败:" + errorSummary(error));

        return undefined;
    }

    private async *_mapEventContinueScan(/*ignore: _EventContinueScan*/): AsyncGenerator<State> {
        const innerState = this.innerState;
        if (innerState.isUploading) return;

        if (innerState.hasChanged) if (!this.validate()) return;

        const goodsInfo = await this._createMedicine();
        if (goodsInfo == null) return;
        innerState.goodsInfo = new GoodsInfo();
        innerState.hasChanged = false;

        switch (innerState.createDirectoryType) {
            case CreateDirectoryTypeEnum.CreateMedicine: {
                this._subType =
                    clinicSharedPreferences.getObject(SAVE_INVENTORY_MEDICINE_TYPE_STATUS)?.subType ?? GoodsSubType.medicineWestern;
                this._typeId = clinicSharedPreferences.getObject(SAVE_INVENTORY_MEDICINE_TYPE_STATUS)?.typeId ?? GoodsTypeId.medicineWest;
                break;
            }
            case CreateDirectoryTypeEnum.CreateSupplies: {
                this._subType =
                    clinicSharedPreferences.getObject(SAVE_INVENTORY_MATERIAL_TYPE_STATUS)?.subType ?? GoodsSubType.materialMedical;
                this._typeId =
                    clinicSharedPreferences.getObject(SAVE_INVENTORY_MATERIAL_TYPE_STATUS)?.typeId ?? GoodsTypeId.materialMedical;
                break;
            }
            case CreateDirectoryTypeEnum.CreateGoods: {
                this._subType = clinicSharedPreferences.getObject(SAVE_INVENTORY_GOODS_TYPE_STATUS)?.subType ?? GoodsSubType.goodsHomemade;
                this._typeId = clinicSharedPreferences.getObject(SAVE_INVENTORY_GOODS_TYPE_STATUS)?.typeId ?? GoodsTypeId.goodsHomemade;
            }
        }

        innerState.goodsInfo = JsonMapper.deserialize(GoodsInfo, {
            dismounting: 0,
            defaultInOutTax: 1,
            type: innerState.createDirectoryType,
            subType: this._subType, // 读取已选择药品类型缓存
            typeId: this._typeId, // 读取已选择药品子类型缓存
        });

        this._loadCustomTypesTrigger.next();

        yield innerState.clone();
    }

    private async *_mapEventFinishCreate(/*event: _EventFinishCreate*/): AsyncGenerator<State> {
        const innerState = this.innerState;
        if (innerState.isUploading) return;
        if (!innerState.goodsInfo.dddOfAntibiotic) innerState.goodsInfo.unitOfAntibiotic = undefined; // 未输入DDD值清除DDD单位

        if (!this.validate()) return;
        innerState.isUploading = true;
        const goodsInfo = await GoodsAgent.createGoods(innerState.goodsInfo, CreateGoodsScene.MANUAL).catch((error) => {
            showConfirmDialog("", "添加药品失败:" + errorSummary(error));
        });
        innerState.isUploading = false;
        if (goodsInfo != null) ABCNavigator.pop(true);

        return undefined;
    }

    private async *_mapEventUpdateInTaxRat(event: _EventUpdateInTaxRat): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.goodsInfo.inTaxRat = event.inTax;
        innerState.hasChanged = true;

        yield innerState.clone();
    }

    private async *_mapEventUpdateOutTaxRat(event: _EventUpdateOutTaxRat): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.goodsInfo.outTaxRat = event.outTax;
        innerState.hasChanged = true;

        yield innerState.clone();
    }

    private async *_mapEventUpdateComponentNum(event: _EventUpdateComponentNum): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.goodsInfo.componentContentNum = event.num;
        innerState.hasChanged = true;
        yield innerState.clone();
    }

    private async *_mapEventUpdateDosageNum(event: _EventUpdateDosageNum): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.goodsInfo.medicineDosageNum = event.num;
        innerState.hasChanged = true;
        yield innerState.clone();
    }

    private _calculatePiecePrice(): void {
        const innerState = this.innerState;
        if (innerState.goodsInfo.packagePrice != null && (innerState.goodsInfo.pieceNum ?? 0.0) > 0) {
            innerState.goodsInfo.piecePrice = innerState.goodsInfo.packagePrice / innerState.goodsInfo.pieceNum!;
            innerState.goodsInfo.piecePrice = Math.round(innerState.goodsInfo.piecePrice * 100) / 100;
        }
    }

    private _fillTaxRat(): void {
        const innerState = this.innerState;
        const config = userCenter.clinicConfig;
        if (config?.manageTaxRat ?? false) {
            innerState.goodsInfo.inTaxRat = config!.medicineInTaxRat;
            innerState.goodsInfo.outTaxRat = config!.medicineOutTaxRat;
        }
    }

    private _setErrorHint(key: any): void {
        const innerState = this.innerState;
        innerState.focusKey = key;
        innerState.showErrorHint = true;

        this.dispatch(new _EventScrollToFocusItemEvent());
    }

    private async *_mapEventUpdateMedicineShortId(event: _EventUpdateMedicineShortId): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.goodsInfo.shortId = event.shortId;
        innerState.hasChanged = true;

        yield innerState.clone();
    }

    private async *_mapEventUpdateExtendSpec(event: _EventUpdateExtendSpec): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.goodsInfo.extendSpec = event.extendSpec;
        innerState.hasChanged = true;

        yield innerState.clone();
    }

    private async *_mapEventUpdateMedicineSheBaoCode(/*event: _EventUpdateMedicineSheBaoCode*/): AsyncGenerator<State> {
        const innerState = this.innerState;
        const goodsInfo = innerState.goodsInfo;

        if (goodsInfo.isWesternMedicine) {
            innerState.sheBaoCodeSearchGoodsSubType = GoodsSubType.medicineWestern; // 西药 1
        } else if (goodsInfo.isChineseWesternMedicine) {
            innerState.sheBaoCodeSearchGoodsSubType = GoodsSubType.medicineChinesePatent; // 中成药 3
        } else if (goodsInfo.isChineseMedicine) {
            innerState.sheBaoCodeSearchGoodsSubType = GoodsSubType.medicineChinese; // 中药 2
        } else if (goodsInfo.isMedicalMaterial) {
            innerState.sheBaoCodeSearchGoodsSubType = GoodsSubType.materialMedical; // 器械耗材 1
        }

        const result: SheBaoCodeSearchGoodsListItem = await ABCNavigator.navigateToPage<SheBaoCodeSearchGoodsListItem>(
            <SheBaoSearchNationalCodeSearchPage
                goodsType={innerState.createDirectoryType}
                goodsSubType={innerState.sheBaoCodeSearchGoodsSubType}
                shebaoCodeType={SheBaoCodeTypeEnum.nationalCode}
                name={goodsInfo.medicineCadn}
                manufacturer={goodsInfo.manufacturerFull}
                shebaoCode={goodsInfo.shebao?.nationalCode}
                medicalFeeGradeStr={innerState?.medicalFeeGradeStr}
                covidDictFlagStr={innerState?.covidDictFlagStr}
                nationalCodeId={goodsInfo.shebao?.nationalCodeId}
            />
        );

        if (_.isEmpty(result)) return;

        goodsInfo.shebao = JsonMapper.deserialize(GoodsInfoShebao, {
            nationalCode: result.shebaoCode,
            nationalCodeId: result._popMedicalInsuranceCodeTypeStr == "null" ? undefined : result.shebaoCodeId,
        });

        if (result._popMedicalInsuranceCodeTypeStr == "null") {
            innerState.defaultMedicalInsuranceCodeStr = "不刷医保/暂无编码";
        } else if (result._popMedicalInsuranceCodeTypeStr == "save") {
            innerState.sheBaoCodeSearchGoodsSelectItem!.shebaoCode = result.shebaoCode;
            innerState.defaultMedicalInsuranceCodeStr = result.shebaoCode;
        } else if (result._popMedicalInsuranceCodeTypeStr == "card") {
            innerState.sheBaoCodeSearchGoodsSelectItem = result;
            innerState.defaultMedicalInsuranceCodeStr = result.medicineCadn;
        } else {
            if (!!result?.shebaoCode?.length) {
                innerState.defaultMedicalInsuranceCodeStr = result?.shebaoCode;
            } else {
                innerState.defaultMedicalInsuranceCodeStr = "选择医保对码";
            }
        }

        //  总部且地区为杭州:选择对码后，若有剂型dosageFrom值，且档案【剂型】无值时，自动填入档案【剂型】中，西药、中成药类型才有剂型
        if (
            (userCenter.clinic?.isChainAdminClinic || userCenter.clinic?.isNormalClinic) &&
            userCenter.shebaoConfig?.isRegionHangzhou &&
            goodsInfo.isMedicineWestAndChinesePatent &&
            !!result?.dosageForm &&
            result.goodsType == GoodsType.medicine &&
            (result.goodsSubType == GoodsSubType.medicineChinesePatent || result.goodsSubType == GoodsSubType.medicineWestern) &&
            !goodsInfo?.dosageFormType
        ) {
            const dosageFormTypeList = userCenter.clinicDictionaryInfo?.businessScopeDict?.westDrugDosageFormTypeList ?? [];
            goodsInfo.dosageFormType = dosageFormTypeList?.find((t) => t.name == result?.dosageForm)?.typeId;
        }

        innerState.hasChanged = true;

        innerState.focusKey = innerState.sheBaoCodeKey;
        this.dispatch(new _EventScrollToFocusItemEvent());
    }

    private async *_mapEventSelectMedicalInsurancePayment(/*event: _EventSelectMedicalInsurancePayment*/): AsyncGenerator<State> {
        const innerState = this.innerState;
        let initialOption: Set<number> = new Set();
        const _options = [
            { payMode: SheBaoPayModeEnum.OVERALL, text: "优先统筹支付" },
            { payMode: SheBaoPayModeEnum.SELF, text: "优先个账支付" },
            { payMode: SheBaoPayModeEnum.NO_USE, text: "不使用医保支付" },
        ];
        _options.forEach((payInfo) => {
            if (payInfo.payMode == innerState.goodsInfo.shebaoPayMode) {
                initialOption = new Set([payInfo.payMode]);
            }
        });

        const selects = await showOptionsBottomSheet({
            title: "选择医保支付",
            options: _options.map((item) => item.text),
            initialSelectIndexes: initialOption,
            showCloseButton: true,
            showTopRadius: true,
            titlePosition: "center",
            titleStyle: TextStyles.t18MT1,
        });
        if (!selects) return;
        innerState.goodsInfo.shebaoPayMode = _.first(selects);
        innerState.hasChanged = true;
        yield innerState.clone();
    }

    private async *_mapEventUpdateOrigin(/*event: _EventUpdateOrigin*/): AsyncGenerator<State> {
        const innerState = this.innerState;

        const _options = ["进口", "国产"];
        const selects = await showOptionsBottomSheet({
            title: "选择进口/国产",
            options: _options,
            initialSelectIndexes: new Set([_options.indexOf(innerState.goodsInfo.origin ?? "")]),
            showCloseButton: true,
            showTopRadius: true,
            titlePosition: "center",
            titleStyle: TextStyles.t18MT1,
        });
        if (!selects) return;
        innerState.goodsInfo.origin = _.first(selects) == 0 ? "进口" : "国产";
        innerState.hasChanged = true;
        yield innerState.clone();
    }

    private async *_mapEventUpdateCertificateName(event: _EventUpdateCertificateName): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.goodsInfo.certificateName = event.certificateName;
        innerState.hasChanged = true;
        yield innerState.clone();
    }

    private async *_mapEventUpdateMaterialSpec(event: _EventUpdateMaterialSpec): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.goodsInfo.materialSpec = event.materialSpec;
        innerState.hasChanged = true;
        yield innerState.clone();
    }

    private async *_mapEventSelectBusinessScope(): AsyncGenerator<State> {
        const innerState = this.innerState;

        const selects = await InventoryBusinessScopeDialog.show({ businessScopeItem: innerState.goodsInfo.businessScopeList });
        if (!selects?.length) return;
        innerState.goodsInfo.businessScopeList = selects;
        innerState.hasChanged = true;
        this.update();
    }
    async *_mapEventUpdateMark(event: _EventUpdateMark): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.goodsInfo.remark = event?.remark;
        innerState.hasChanged = true;
        yield innerState.clone();
    }

    private async *_mapEventSelectNpmnExpiryDate(): AsyncGenerator<State> {
        const innerState = this.innerState;
        const initRange = new Range<Date>(
                innerState.goodsInfo.medicineNmpnStartExpiryDate ?? new Date(),
                innerState.goodsInfo.medicineNmpnEndExpiryDate
            ),
            maxRange = new Range<Date>(undefined, undefined);
        const timeRange = await RangePicker.show(initRange, maxRange);
        if (!timeRange) return;
        innerState.goodsInfo.medicineNmpnStartExpiryDate = timeRange.start;
        innerState.goodsInfo.medicineNmpnEndExpiryDate = timeRange.end;
        this.update();
    }

    private async *_mapEventSelectMemberPrice(): AsyncGenerator<State> {
        const result: GoodsMultiPriceView[] = await ABCNavigator.navigateToPage(
            <InventorySettingMemberPricePage
                goodsInfo={this.innerState.goodsInfo}
                memberPriceList={this.innerState.goodsInfo.multiPriceList}
                canAdjustPrice={true}
                canSetPrice={true}
            />
        );
        if (!result) return;
        this.innerState.goodsInfo.multiPriceList = result;
        this.update();
    }

    private async *_EventModifyDosageUnit(event: _EventModifyDosageUnit): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.goodsInfo.medicineDosageUnit = event.dosageUnit;
        innerState.hasChanged = true;
        this.update();
    }

    private async *_mapEventModifyPlaceOfOrigin(event: _EventModifyPlaceOfOrigin): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.goodsInfo.origin = event.placeOrigin;
        innerState.hasChanged = true;
        this.update();
    }

    private async *_mapEventUpdatePieceWeight(event: _EventUpdatePieceWeight): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.goodsInfo.pieceUnitWeight = event.value;
        innerState.hasChanged = true;
        this.update();
    }

    public update(state?: State): void {
        this.dispatch(new _EventUpdate(state));
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}

class _EventUpdateComponentNum extends _Event {
    constructor(num?: number) {
        super();
        this.num = num;
    }

    num?: number;
}

class _EventUpdateDosageNum extends _Event {
    constructor(num?: number) {
        super();
        this.num = num;
    }

    num?: number;
}

class _EventUpdateOutTaxRat extends _Event {
    constructor(outTax?: number) {
        super();
        this.outTax = outTax;
    }

    outTax?: number;
}

class _EventUpdateInTaxRat extends _Event {
    constructor(inTax?: number) {
        super();
        this.inTax = inTax;
    }

    inTax?: number;
}

class _EventUpdateBarCode extends _Event {
    constructor(barCode?: string) {
        super();
        this.barCode = barCode;
    }

    barCode?: string;
}

class _EventScanQRCode extends _Event {}

class _EventContinueScan extends _Event {}

class _EventFinishCreate extends _Event {}

class _EventUpdatePieceNum extends _Event {
    num?: number;

    constructor(num?: number) {
        super();
        this.num = num;
    }
}

class _EventUpdateEqCoefficient extends _Event {
    val?: number;

    constructor(num?: number) {
        super();
        this.val = num;
    }
}

class _EventSelectPackageUnit extends _Event {}

class _EventSelectPieceUnit extends _Event {}

class _EventSelectDosageUnit extends _Event {}

class _EventSelectComponentUnit extends _Event {}

class _EventScrollToFocusItemEvent extends _Event {}

class _EventUpdatePiecePrice extends _Event {
    constructor(price?: number) {
        super();
        this.price = price;
    }

    price?: number;
}

class _EventModifyGoodsInfoFeeType extends _Event {}

class _EventUpdateDismounting extends _Event {
    constructor(dismounting?: number) {
        super();
        this.dismounting = dismounting;
    }

    dismounting?: number;
}

class _EventUpdateInOutTax extends _Event {
    defaultInOutTax?: number;

    constructor(defaultInOutTax?: number) {
        super();
        this.defaultInOutTax = defaultInOutTax;
    }
}

class _EventUpdateExtendSpec extends _Event {
    extendSpec?: string;

    constructor(extendSpec?: string) {
        super();
        this.extendSpec = extendSpec;
    }
}

class _EventUpdatePackagePrice extends _Event {
    constructor(price?: number) {
        super();
        this.price = price;
    }

    price?: number;
}
class _EventUpdateIsSell extends _Event {
    isSell?: number;

    constructor(isSell?: number) {
        super();
        this.isSell = isSell;
    }
}

class _EventMedicineSelectSpecification extends _Event {}

class _EventUpdateMaterialSpec extends _Event {
    materialSpec?: string;

    constructor(materialSpec?: string) {
        super();
        this.materialSpec = materialSpec;
    }
}

class _EventSelectDosageForm extends _Event {}

class _EventSelectCustomTypesForm extends _Event {}

class _EventSelectPharmacologic extends _Event {
    type?: PharmacologicSourceType;
    constructor(type?: PharmacologicSourceType) {
        super();
        this.type = type;
    }
}

class _EventSelectPharmaceuticalForm extends _Event {}

class _EventSelectOTCForm extends _Event {}

class _EventSelectNarcoticsDangerousDrugsRelease extends _Event {}

class _EventSelectAntibiotics extends _Event {}

class _EventUpdateDefinedDailyDose extends _Event {
    dddOfAntibiotic?: number;
    unitOfAntibiotic?: string;

    constructor(dddOfAntibiotic?: number, unitOfAntibiotic?: string) {
        super();
        this.dddOfAntibiotic = dddOfAntibiotic;
        this.unitOfAntibiotic = unitOfAntibiotic;
    }
}

class _EventSelectBaseMedicineType extends _Event {}

class _EventSelectMedicalMaterialDeviceType extends _Event {}

class _EventSelectMaintenanceClassification extends _Event {}

class _EventSelectStorageConditions extends _Event {}

class _EventSelectTag extends _Event {}

class _EventSelectExpirationDate extends _Event {
    monthNum?: number;

    constructor(monthNum?: number) {
        super();
        this.monthNum = monthNum;
    }
}

class _EventModifyPosition extends _Event {
    positionNum?: string;

    constructor(positionNum?: string) {
        super();
        this.positionNum = positionNum;
    }
}

class _EventSelectPriceType extends _Event {}

class _EventSelectMarkupRate extends _Event {
    markupRate?: number;
    constructor(markupRate?: number) {
        super();
        this.markupRate = markupRate;
    }
}

// class _EventSelectProfitCategorization extends _Event {}

class _EventUpdateMedicineSubtype extends _Event {
    constructor(subType?: number, typeId?: number) {
        super();
        this.subType = subType;
        this.typeId = typeId;
    }

    subType?: number;
    typeId?: number;
}

class _EventUpdateMedicineGoodsTypeId extends _Event {}

class _EventUpdateManufacturer extends _Event {
    constructor(name?: string) {
        super();
        this.name = name;
    }

    name?: string;
}

class _EventSearchManufacturerFull extends _Event {
    type?: string;

    constructor(type?: string) {
        super();
        this.type = type;
    }
}
class _EventUpdateOrigin extends _Event {}

class _EventUpdateCertificateName extends _Event {
    constructor(certificateName?: string) {
        super();
        this.certificateName = certificateName;
    }

    certificateName?: string;
}

class _EventUpdateMedicineShortId extends _Event {
    shortId?: string;
    constructor(shortId?: string) {
        super();
        this.shortId = shortId;
    }
}

class _EventUpdateMedicineSheBaoCode extends _Event {}

class _EventSelectMedicalInsurancePayment extends _Event {}

class _EventUpdateNmpn extends _Event {
    constructor(name?: string) {
        super();
        this.name = name;
    }

    name?: string;
}

class _EventUpdateName extends _Event {
    constructor(name?: string) {
        super();
        this.name = name;
    }

    name?: string;
}

class _EventSearchName extends _Event {}

class _EventUpdateCadn extends _Event {
    constructor(cadn?: string) {
        super();
        this.cadn = cadn;
    }

    cadn?: string;
}

class _EventSearchCadn extends _Event {}

class _EventUpdateQRCode extends _Event {
    qrCode?: string;

    constructor(qrCode?: string) {
        super();
        this.qrCode = qrCode;
    }
}

class _EventUpdateProductionDate extends _Event {
    constructor(date?: Date) {
        super();
        this.date = date;
    }

    date?: Date;
}

class _EventUpdateBatchNum extends _Event {
    constructor(batchNum?: string) {
        super();
        this.batchNum = batchNum;
    }

    batchNum?: string;
}

class _EventUpdateUseNum extends _Event {
    constructor(packageNum?: number) {
        super();
        this.packageNum = packageNum;
    }

    packageNum?: number;
}

class _EventUpdateUseUnit extends _Event {
    constructor(packageUnit?: string) {
        super();
        this.packageUnit = packageUnit;
    }

    packageUnit?: string;
}

class _EventUpdateCostPrice extends _Event {
    constructor(price?: number) {
        super();
        this.price = price;
    }

    price?: number;
}

class _EventUpdateExpiredDate extends _Event {
    time?: Date;

    constructor(time?: Date) {
        super();
        this.time = time;
    }
}
class _EventSelectBusinessScope extends _Event {}
class _EventUpdateMark extends _Event {
    remark?: string;
    constructor(remark?: string) {
        super();
        this.remark = remark;
    }
}
class _EventSelectNpmnExpiryDate extends _Event {}
class _EventSelectMemberPrice extends _Event {}

class _EventModifyDosageUnit extends _Event {
    dosageUnit: string;
    constructor(dosageUnit: string) {
        super();
        this.dosageUnit = dosageUnit;
    }
}

class _EventModifyPlaceOfOrigin extends _Event {
    placeOrigin: string;
    constructor(placeOrigin: string) {
        super();
        this.placeOrigin = placeOrigin;
    }
}

class _EventUpdatePieceWeight extends _Event {
    value: number;
    constructor(value: number) {
        super();
        this.value = value;
    }
}
export { InventoryInMedicineCreateViewBloc, State };
