/**
 * create by <PERSON><PERSON>
 * desc:
 * create date 2021/2/1
 */

import { Bloc, BlocEvent } from "../../bloc";
import React from "react";
import { actionEvent, EventName } from "../../bloc/bloc";
import { BaseLoadingState } from "../../bloc/bloc-helper";
import {
    InventoryCheckCreatePageType,
    InventoryCheckDetail,
    InventoryCheckDetailComment,
    InventoryCheckDraft,
    InventoryCheckEnterType,
    InventoryCheckReq,
    PostGoodsStocksCheckOrdersRep,
} from "./data/inventory-check-bean";
import { Subject } from "rxjs";
import { switchMap } from "rxjs/operators";
import { InventoryAgent } from "../data/inventory-agent";
import { ABCError } from "../../common-base-module/common-error";
import { userCenter } from "../../user-center";
import { JsonMapper } from "../../common-base-module/json-mapper/json-mapper";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { InventoryUtils } from "../utils/inventory-utils";
import { InventoryMedicineSearchPage } from "../Inventory-medicine-search-page";
import {
    CheckTaskGoodItem,
    InventoryApiErrorCode,
    InventoryCheckDraftReq,
    InventoryClinicConfig,
    SearchParams,
} from "../data/inventory-bean";
import { GoodsInfo } from "../../base-business/data/beans";
import { Toast } from "../../base-ui/dialog/toast";
import { DialogIndex, showConfirmDialog, showQueryDialog } from "../../base-ui/dialog/dialog-builder";
import { InventoryCheckAgent } from "./data/inventory-check-agent";
import { LoadingDialog } from "../../base-ui/dialog/loading-dialog";
import _ from "lodash";
import { ABCApiError } from "../../net";
import { AnyType } from "../../common-base-module/common-types";
import { InventoryTaskPopupDialog } from "./full-check/inventory-task-popup-dialog";
import { InventorySubTaskNotCheckDialog, InventorySubTaskNotCheckDialogAction } from "./full-check/inventory-subtask-not-check-dialog";
import { InventoryCheckReviewDialog } from "./views/inventory-check-review-dialog";
import { errorSummary, errorToStr } from "../../common-base-module/utils";
import { UniqueKey } from "../../base-ui";
import { ClinicAgent, EmployeesMeConfig } from "../../base-business/data/clinic-agent";
import { ScrollView, View } from "@hippy/react";
import { runFuncBeforeCheckExpired } from "../../views/clinic-edition";
import { GoodsAgent, PharmacyListItem } from "../../data/goods/goods-agent";
import { showOptionsBottomSheet } from "../../base-ui/dialog/bottom_sheet";
import { InventoryCheckDraftManage } from "./data/inventory-check-draft-manage";
import { InventoryPharmacyAgent } from "../data/inventory-pharmacy-agent";
import { ApprovalInstDetail } from "../data/inventory-pharmacy-bean";
import { clinicSharedPreferences } from "../../base-business/preferences/scoped-shared-preferences";
import { showBottomPanel } from "../../base-ui/abc-app-library/panel";
import { InventoryCheckMultipleBatchDialog } from "./views/inventory-check-multiple-batch-dialog";
import { DraftSaveQueueService } from "../data/draft-save-queue-service";
import {
    createMedicineDeleteRequest,
    createMedicineQueryRequest,
    createMedicineUpdateRequest,
    createOrderRemarkQueryRequest,
    createOrderRemarkUpdateRequest,
} from "../data/inventory-draft-bean";

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}

class _EventFinishCheck extends _Event {}

class _EventChangeComment extends _Event {
    comment: string;

    constructor(comment: string) {
        super();
        this.comment = comment;
    }
}

class _EventModifyMedicineCount extends _Event {
    goodItem: CheckTaskGoodItem;

    constructor(data: CheckTaskGoodItem) {
        super();
        this.goodItem = data;
    }
}

class _EventDeleteMedicineCount extends _Event {
    goodsInfo: CheckTaskGoodItem;

    constructor(data: CheckTaskGoodItem) {
        super();
        this.goodsInfo = data;
    }
}

class _EventAddGood extends _Event {
    qrScan: boolean;

    constructor(qrScan: boolean) {
        super();
        this.qrScan = qrScan;
    }
}

class _EventContinueSubmit extends _Event {
    goodsInfoList: GoodsInfo[];
    title: string;

    constructor(title: string, goodsInfo: GoodsInfo[]) {
        super();
        this.goodsInfoList = goodsInfo;
        this.title = title;
    }
}

class _EventAddGoodToList extends _Event {
    goodsInfo: GoodsInfo;

    constructor(goodsInfo: GoodsInfo) {
        super();
        this.goodsInfo = goodsInfo;
    }
}

class _EventBackPage extends _Event {
    fromEdgeGesture?: boolean;
    constructor(fromEdgeGesture?: boolean) {
        super();
        this.fromEdgeGesture = fromEdgeGesture;
    }
}

class _EventLoadMore extends _Event {}

class _EventReload extends _Event {}

class _EventReviewCheckHandle extends _Event {
    pass: boolean;

    constructor(status: boolean) {
        super();
        this.pass = status;
    }
}

class _EventRevokeTransHandle extends _Event {}

class _EventEditCheckHandle extends _Event {}
class _EventSelectMultiplePharmacy extends _Event {}

export class State extends BaseLoadingState {
    type: InventoryCheckCreatePageType = InventoryCheckCreatePageType.create;
    detail: InventoryCheckDetail = new InventoryCheckDetail();
    _detail?: InventoryCheckDetail;

    hasChange = false;
    scrollKeyIndex = 0;

    stockCheckChainReview?: boolean;

    stockCheckAll = false;

    // 保存云草稿的状态
    saveDraftStatus = "idle";
    limit = 100;

    // 分页相关状态
    hasMore = true;
    currentOffset = 0;
    isLoadingMore = false;

    //goods配置相关信息--此处关注多药房
    pharmacyInfoConfig?: InventoryClinicConfig;
    /**
     * 筛选项的药房
     */
    currentPharmacy?: PharmacyListItem;
    //盘点药房(存储新建盘点单时选择的药房)
    entryPharmacy?: PharmacyListItem;
    pharmacyList?: PharmacyListItem[];
    employeesMeConfig?: EmployeesMeConfig;
    //能否查看盘点药品价格
    get canViewPrice(): boolean {
        return !!this.employeesMeConfig?.employeeDataPermission?.inventory?.isCanSeeCheckGoodsPrice;
    }

    _isEditDraft?: boolean;
    showHint = false;
    approvalInstDetail?: ApprovalInstDetail; //药店审批详情
    get isHasApprovalAuthority(): boolean {
        return !!this.approvalInstDetail?.isHasApprovalAuthority;
    }
    //  拥有驳回权限
    get isHasRejectAuthority(): boolean {
        return !!this.approvalInstDetail?.isHasRejectAuthority;
    }
    //  拥有能重新提交已驳回的审批权限
    get isHasResubmitAuthority(): boolean {
        return !!this.approvalInstDetail?.isHasResubmitAuthority;
    }

    clone(): State {
        return Object.assign(new State(), this);
    }

    get isCreate(): boolean {
        return this.type == InventoryCheckCreatePageType.create || this.isReEdit;
    }

    get isReEdit(): boolean {
        return this.type == InventoryCheckCreatePageType.reEdit;
    }

    get isDraft(): boolean {
        return !!this.detail.__draftId;
    }
}

export class ScrollSameViewState extends State {
    static fromState(state: State): ScrollSameViewState {
        const newState = new ScrollSameViewState();
        Object.assign(newState, state);
        return newState;
    }
}

export class InventoryCheckCreatePageBloc extends Bloc<_Event, State> {
    static Context = React.createContext<InventoryCheckCreatePageBloc | undefined>(undefined);

    private _loadDataTrigger: Subject<number> = new Subject<number>();
    private _id?: string;
    _searchParams?: SearchParams;
    private _goodsInfo?: GoodsInfo;

    private readonly orderClientUniqKey: string;
    private readonly draftManager: InventoryCheckDraftManage;
    private _draftId?: string;
    private _createType: InventoryCheckEnterType;
    private saveQueue = DraftSaveQueueService.getInstance();

    static fromContext(context: InventoryCheckCreatePageBloc): InventoryCheckCreatePageBloc {
        return context;
    }

    constructor(options: {
        id?: string;
        searchParams?: SearchParams;
        goodsInfo?: GoodsInfo;
        draftId?: string;
        createType?: InventoryCheckEnterType;
    }) {
        super();
        if (options.id) {
            this._id = options.id;
            this.innerState.type = InventoryCheckCreatePageType.detail;
        }
        this._goodsInfo = options.goodsInfo;
        this._searchParams = options.searchParams
            ? _.assign(options.searchParams ?? {}, {
                  withStock: "1",
                  onlyStock: "1",
              })
            : undefined;
        this.orderClientUniqKey = UniqueKey();
        this._draftId = options?.draftId;
        this.draftManager = InventoryCheckDraftManage.inventoryCheckDraftManageInstance;
        if (this._draftId) {
            this.innerState._isEditDraft = true;
        }
        this._createType = options?.createType ?? InventoryCheckEnterType.manual;
        this.dispatch(new _EventInit()).then();
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    get isOnlineDraft(): boolean {
        return this._createType == InventoryCheckEnterType.onlineDraft;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        return new Map<EventName, Function>();
    }

    public update(state?: State): void {
        this.dispatch(new _EventUpdate(state)).then();
    }

    private _saveDraft(): void {
        if (!this.innerState.isDraft) return;
        this.innerState.detail.lastModifiedDate = new Date();
        this.draftManager.saveDraft(JsonMapper.deserialize(InventoryCheckDraft, { ...this.innerState.detail }));
        ``;
    }

    private _createDetail(): void {
        this.innerState.detail = new InventoryCheckDetail();
        this.innerState.detail.list = [];

        this.innerState.detail.organ = {
            id: userCenter.clinic?.clinicId,
            parentId: userCenter.clinic?.chainId,
            name: userCenter.clinic?.name,
            shortName: !userCenter.clinic?.isNormalClinic && userCenter.clinic?.isChainAdminClinic ? "总部" : userCenter.clinic?.shortName,
        };

        this._draftId = InventoryCheckDraftManage.generateDraftId();
        this.innerState.detail.__searchParams = this._searchParams;
        this.innerState.detail.__draftId = this._draftId;
    }
    private async getPharmacyApprovalAuthority(): Promise<void> {
        if (userCenter.clinic?.isDrugstoreButler) {
            const instId = this.innerState.detail?.gspInstId;
            if (!!instId) {
                this.innerState.approvalInstDetail = await InventoryPharmacyAgent.getApprovalInstDetail(instId).catchIgnore();
                this.update();
            }
        }
    }

    private async _initPageConfig(): Promise<void> {
        //多药房相关配置
        this.innerState.pharmacyInfoConfig = userCenter.inventoryClinicConfig;
        /**
         * 获取药房type
         */
        this.innerState.currentPharmacy = InventoryAgent.getCurrentPharmacy();
        this.innerState.entryPharmacy = _.cloneDeep(this.innerState.currentPharmacy);
        const pharmacyConfig = await GoodsAgent.getPharmacyList().catchIgnore();
        this.innerState.pharmacyList = pharmacyConfig?.rows;
        //拉取诊所配置
        const chainConfig = await ClinicAgent.getInventoryChainConfig().catchIgnore();
        this.innerState.stockCheckChainReview = !!chainConfig?.chainReview?.stockCheckChainReview && !userCenter.clinic?.isChainAdminClinic; // 盘点-总部是否审核（单店没有该功能）
        this.innerState.employeesMeConfig = await ClinicAgent.getEmployeesMeConfig().catchIgnore();
    }
    private _initTrigger(): void {
        this._loadDataTrigger
            .pipe(
                switchMap(() => {
                    return InventoryAgent.getGoodsStocksCheckOrdersDetail({ id: this._id, batchs: 1 })
                        .then((rsp) => {
                            if (!userCenter.clinic?.isNormalClinic && userCenter.clinic?.chainId == rsp.organ?.id) {
                                rsp.organ!.shortName = "总部";
                            }
                            return rsp;
                        })
                        .catch((e) => new ABCError(e))
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                this.innerState.stopLoading();
                if (rsp instanceof ABCError) {
                    this.innerState.setLoadingError(rsp);
                } else {
                    this.innerState.detail = rsp;
                    if (!!rsp?.stockCheckScope) {
                        this.innerState.detail.__searchParams = {
                            typeId: rsp?.stockCheckScope?.typeIdList,
                            customTypeId: rsp?.stockCheckScope?.customTypeIdList,
                        };
                    }
                    this.getPharmacyApprovalAuthority().then();
                }
                this.update();
            })
            .addToDisposableBag(this);

        // 获取当前保存草稿的状态
        this.saveQueue.saveStatus$
            .subscribe((info) => {
                this.innerState.saveDraftStatus = info?.status;
                this.update();
            })
            .addToDisposableBag(this);
    }
    @actionEvent(_EventInit)
    private async *_mapEventInit(/*event: _EventInit*/): AsyncGenerator<State> {
        await this._initPageConfig();
        this._initTrigger();
        if (this.innerState.type == InventoryCheckCreatePageType.detail) {
            this.innerState.startLoading();
            this._loadDataTrigger.next();
        } else {
            if (this._draftId && !this.isOnlineDraft) {
                const _draftDetail = await this.draftManager.loadInventoryCheckDraft(this._draftId).catchIgnore();
                const pharmacyList = this.innerState?.pharmacyInfoConfig
                    ?.inventoryPharmacyList()
                    .filter((t) => t?.status == 1 && !!t?.enableCheck);
                const isExistPharmacy = pharmacyList?.find(
                    (t) => t?.type == _draftDetail?.pharmacy?.type && t?.no == _draftDetail?.pharmacy?.no
                );
                //解决：在保存草稿的时候库房信息还在，再次进入时，可能库房信息已经被删除或者被修改
                //更新药房信息，解决库房名字修改
                if (!!isExistPharmacy) {
                    _draftDetail!.pharmacy = isExistPharmacy;
                } else {
                    // 如果药房信息不存在，则清空药房信息以及对应的药品数据
                    if (!!clinicSharedPreferences.getBool("INVENTORY_DEBUGGER")) {
                    } else {
                        _draftDetail!.pharmacy = undefined;
                        _draftDetail!.list = [];
                    }
                }
                if (_draftDetail) this.innerState.detail = JsonMapper.deserialize(InventoryCheckDetail, { ..._draftDetail });
            } else {
                if (!!this._draftId && this.isOnlineDraft) {
                    // 重置分页状态
                    this.innerState.currentOffset = 0;
                    this.innerState.hasMore = true;

                    const queryDetailParams: InventoryCheckDraftReq = {
                        instructions: [
                            createOrderRemarkQueryRequest({
                                checkOrderId: this._draftId,
                            }),
                            createMedicineQueryRequest({
                                checkOrderId: this._draftId,
                                offset: this.innerState.currentOffset,
                                limit: this.innerState.limit,
                            }),
                        ],
                    };
                    const result = await InventoryCheckAgent.syncStocksCheckOrderByDraftId(this._draftId, queryDetailParams).catchIgnore();

                    if (Array.isArray(result)) {
                        // result已经是处理过的数组，直接根据顺序获取数据
                        const medicineResult = result[1]; // 药品查询结果包含所有需要的数据

                        if (medicineResult?.data) {
                            // 直接使用medicineResult的数据作为详情信息
                            this.innerState.detail = JsonMapper.deserialize(InventoryCheckDetail, {});

                            // TODO 目前还不清楚接口是否含有stockCheckScope字段
                            // if (!!medicineResult.data?.stockCheckScope) {
                            //     this.innerState.detail.__searchParams = {
                            //         typeId: medicineResult.data?.stockCheckScope?.typeIdList,
                            //         customTypeId: medicineResult.data?.stockCheckScope?.customTypeIdList,
                            //     };
                            // }
                            this.innerState.detail.__draftId = this._draftId;

                            // 处理药品列表的分页数据
                            if (medicineResult.data?.rows) {
                                if (!this.innerState.detail?.list?.length) this.innerState.detail.list = [];
                                const returnedItems = medicineResult.data.rows || [];
                                this.innerState.detail.list = returnedItems;

                                // 使用返回的分页信息更新状态
                                const { total = 0, offset = 0, limit = 100 } = medicineResult.data || {};
                                this.innerState.currentOffset = offset + limit;
                                // 判断是否还有更多数据：当前页数据量等于limit 且 已加载数据总数 < total总数
                                const currentPageDataCount = returnedItems.length;
                                this.innerState.hasMore = currentPageDataCount === limit && this.innerState.detail.list.length < total;
                            } else {
                                // 如果没有药品数据，初始化空数组
                                this.innerState.detail.list = [];
                                this.innerState.hasMore = false;
                            }
                        }
                    }

                    //判断当前草稿是否有本地草稿
                    const list = await InventoryCheckDraftManage.inventoryCheckDraftManageInstance.getAllDrafts();
                    const localDraft = list.find((it) => it.__draftId == this._draftId);
                    if (!!list.length && !!localDraft) {
                        const result = await showQueryDialog("提示", "当前存在未处理草稿，是否恢复？");
                        if (result == DialogIndex.positive) {
                            this.innerState.detail = JsonMapper.deserialize(InventoryCheckDetail, { ...localDraft });
                        }
                    }
                } else {
                    this._createDetail();
                }
            }
            const newList: CheckTaskGoodItem[] = [];
            this.innerState.detail.list?.forEach((item) => {
                item.batchs?.forEach((batchInfo) => {
                    newList.push(
                        JsonMapper.deserialize(CheckTaskGoodItem, {
                            ...item,
                            batchId: batchInfo?.batchId,
                            batchs: [batchInfo],
                            packageCount: batchInfo?.packageCount,
                            pieceCount: batchInfo?.pieceCount,
                            beforePackageCount: batchInfo?.beforePackageCount,
                            beforePieceCount: batchInfo?.beforePieceCount,
                            _draftBeforePackageCount: batchInfo?.draftBeforePackageCount,
                            _draftBeforePieceCount: batchInfo?.draftBeforePieceCount,
                            totalCostPriceChange: batchInfo?.totalCostPriceChange,
                            totalPriceChange: batchInfo?.totalPriceChange,
                        })
                    );
                });
            });
            this.innerState.detail!.list = newList;
            this.innerState._detail = _.cloneDeep(this.innerState.detail);
        }
        if (this._goodsInfo) {
            this.dispatch(new _EventAddGoodToList(this._goodsInfo)).then(() => (this._goodsInfo = undefined));
        }
        this.innerState.stopLoading();
        this.update();
    }

    @actionEvent(_EventUpdate)
    private async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
        if (event.state) {
            yield event.state;
            return;
        }
        yield this.innerState.clone();
    }

    @actionEvent(_EventFinishCheck)
    private async *_mapEventFinishCheck(/*event: _EventFinishCheck*/): AsyncGenerator<State> {
        if (!this.innerState.detail.list?.length) return;
        if (this.innerState.stockCheckChainReview) {
            const result = await showQueryDialog("", "提交后将进入审批流程，审批通过后完成盘点，确认提交吗？");
            if (result != DialogIndex.positive) return;
        } else {
            const result = await showQueryDialog("", "确认提交盘点单吗？");
            if (result != DialogIndex.positive) return;
        }
        // 药店管家必须填写批次信息
        if (userCenter.clinic?.isDrugstoreButler) {
            const isExistUnselectBatch = this.innerState.detail.list?.some((item) => !item?.batchId);
            const existUnselectBatchCount = this.innerState.detail.list?.filter((item) => !item?.batchId);
            if (isExistUnselectBatch) {
                this.innerState.showHint = true;
                yield this.innerState.clone();
                await Toast.show(`有${existUnselectBatchCount?.length}种商品未选择批次`);
                return;
            }
        }

        const req = InventoryCheckDetail.PostCheckOrdersRep(this.innerState.detail, this._searchParams, this.orderClientUniqKey);
        if (this.isOnlineDraft) {
            req.checkOrderDraftId = this._draftId;
            // 处理全量盘点保存为草稿时，再次提交时，变成临时盘点问题
            if (!!this.innerState.detail.stockCheckScope?.typeIdList?.length || !!this._searchParams?.typeId?.length) {
                req.isCheckScope = 1;
            }
        }
        req.pharmacyNo = this.innerState.entryPharmacy?.no;
        const loadingDialog = new LoadingDialog();
        loadingDialog.show(100);
        InventoryCheckAgent.postGoodsStocksCheckOrders(req)
            .then(async () => {
                InventoryAgent.taskStatusPublisher.next();
                InventoryAgent.medicineRefreshPublisher.next();
                if (this._draftId) await this.draftManager.removeDraft(this._draftId);
                loadingDialog.success("提交成功").then(() => ABCNavigator.pop());
            })
            .catch((error) => {
                loadingDialog.hide().then(() => {
                    if (error instanceof ABCApiError && error?.detail?.error?.code == "12238") {
                        const list: GoodsInfo[] = [];
                        error.detail.error.detail.forEach((item: AnyType) => {
                            list.push(
                                JsonMapper.deserialize(GoodsInfo, {
                                    ...item,
                                    id: item.goodsId,
                                    goodsBatchInfoList: item?.batches,
                                    stockPackageCount: item.packageCount,
                                    stockPieceCount: item.pieceCount,
                                    manufacturer: item?.goods?.manufacturer,
                                })
                            );
                        });

                        this.dispatch(new _EventContinueSubmit(error?.detail?.error?.message, list));
                    } else if (error instanceof ABCApiError && error?.detail?.error?.code == InventoryApiErrorCode.goodsStockChecking) {
                        showConfirmDialog("提交失败", error?.detail?.error?.message).then(() => {
                            InventoryAgent.taskStatusPublisher.next();
                            InventoryAgent.medicineRefreshPublisher.next();
                            ABCNavigator.pop();
                        });
                    } else if (
                        error instanceof ABCApiError &&
                        error?.detail?.error?.code == InventoryApiErrorCode.goodsStockSummaryErrorCode
                    ) {
                        //处理12100异常code
                        const { detail } = error?.detail?.error ?? {};
                        showConfirmDialog(
                            `提交失败：${detail?.errorTitle ?? ""}`,
                            <ScrollView>
                                <View>
                                    {detail.errorList
                                        .map((it: { name: string; msgs: string[] }) => `${it.name}:${it.msgs.join("，")}`)
                                        .join("\n")}
                                </View>
                            </ScrollView>
                        );
                    } else if (error instanceof ABCApiError && error?.detail?.error?.code == InventoryApiErrorCode.submitted) {
                        showQueryDialog("提示", error?.detail?.error?.message).then((index) => {
                            if (index == DialogIndex.positive) {
                                req.forceSubmit = 1;
                                InventoryCheckAgent.postGoodsStocksCheckOrders(req)
                                    .then(async () => {
                                        InventoryAgent.taskStatusPublisher.next();
                                        InventoryAgent.medicineRefreshPublisher.next();
                                        if (this._draftId) await this.draftManager.removeDraft(this._draftId);
                                        loadingDialog.success("提交成功").then(() => ABCNavigator.pop());
                                    })
                                    .catch((error) => {
                                        showConfirmDialog("提交失败", error?.detail?.error?.message);
                                    });
                            } else {
                                InventoryAgent.taskStatusPublisher.next();
                                InventoryAgent.medicineRefreshPublisher.next();
                                if (this._draftId) this.draftManager.removeDraft(this._draftId);
                                ABCNavigator.pop();
                            }
                        });
                    } else {
                        showConfirmDialog("提交失败", error?.detail?.error?.message);
                    }
                });
            });
    }
    private async handleNotCheckGoods(notCheckList: CheckTaskGoodItem[]): Promise<void> {
        const innerState = this.innerState;
        const cloneData = _.cloneDeep(innerState.detail);
        cloneData.list = notCheckList;
        cloneData.comment = [
            {
                content: `${new Date().format("yyyy-MM-dd")} 全量盘点中未盘点的商品`,
            },
        ];
        const switchReqData = InventoryCheckDetail.PostCheckOrdersRep(cloneData, undefined, this.orderClientUniqKey);
        const req = JsonMapper.deserialize(InventoryCheckReq, {
            ...switchReqData,
            lastModifiedDate: cloneData?.lastModifiedDate,
            pharmacyNo: innerState.entryPharmacy?.no,
        });
        InventoryCheckAgent.createCheckOrderDraft(req)
            .then(async (rsp) => {
                //保存线上草稿成功,删除本地草稿
                if (rsp?.code == 200) {
                    await this.draftManager.removeDraft(this._draftId!);
                }
                ABCNavigator.pop();
            })
            .catch((e) => {
                showConfirmDialog("保存失败", errorSummary(e?.message));
            });
    }

    /**
     * 选中药品与未盘点药品处理
     * @param type
     * @param notCheckList
     * @private
     */
    private handleSelectAndNotCheckGoods(type: number, notCheckList: CheckTaskGoodItem[]): PostGoodsStocksCheckOrdersRep {
        const cloneData = _.cloneDeep(this.innerState.detail);
        if (type == InventorySubTaskNotCheckDialogAction.changZero) {
            notCheckList = notCheckList?.map((item) => {
                item.packageCount = 0;
                item.pieceCount = 0;
                return item;
            });
        }
        cloneData.list = cloneData.list?.concat(notCheckList);
        return InventoryCheckDetail.PostCheckOrdersRep(cloneData, this._searchParams, this.orderClientUniqKey);
    }

    @actionEvent(_EventContinueSubmit)
    private async *_mapEventContinueSubmit(event: _EventContinueSubmit): AsyncGenerator<State> {
        const notCheckList: CheckTaskGoodItem[] = [];
        event.goodsInfoList?.map((item) => {
            // 药店管家商品必须指定批次，所以需要将批次信息打平放出来
            if (userCenter.clinic?.isDrugstoreButler && !!item?.goodsBatchInfoList?.length) {
                item.goodsBatchInfoList?.map((batchItem) => {
                    notCheckList.push(
                        JsonMapper.deserialize(CheckTaskGoodItem, {
                            ...item,
                            ...batchItem,
                            goods: item,
                            goodsId: item.id,
                            beforePieceCount: batchItem.pieceCount,
                            beforePackageCount: batchItem.packageCount,
                            pieceNum: item.pieceNum,
                        })
                    );
                });
            } else {
                notCheckList.push(
                    JsonMapper.deserialize(CheckTaskGoodItem, {
                        ...item,
                        goods: item,
                        goodsId: item.id,
                        beforePieceCount: item.pieceCount,
                        beforePackageCount: item.packageCount,
                    })
                );
            }
        });

        const result = await InventorySubTaskNotCheckDialog.show({
            title: event.title,
            detail: notCheckList,
            showKeep: true,
            canGenerateDraft: true,
        });
        if (!result) return;
        let req = InventoryCheckDetail.PostCheckOrdersRep(this.innerState.detail, this._searchParams, this.orderClientUniqKey);
        if (result.action == InventorySubTaskNotCheckDialogAction.generatingDraft) {
            await this.handleNotCheckGoods(notCheckList);
        }
        if (result.action == InventorySubTaskNotCheckDialogAction.changZero || result.action == InventorySubTaskNotCheckDialogAction.keep) {
            req = this.handleSelectAndNotCheckGoods(result.action, notCheckList);
        }
        req.submitFlag = 0;
        req.forceSubmit = 1;
        const loadingDialog = new LoadingDialog();
        loadingDialog.show(100);
        InventoryCheckAgent.postGoodsStocksCheckOrders(req)
            .then(() => {
                InventoryAgent.taskStatusPublisher.next();
                InventoryAgent.medicineRefreshPublisher.next();
                loadingDialog.success("提交成功").then(() => ABCNavigator.pop());
            })
            .catch((error) => {
                if (error instanceof ABCApiError && error?.detail?.error?.code == InventoryApiErrorCode.goodsStockChecking) {
                    loadingDialog.hide().then(() => {
                        showConfirmDialog("提交失败", error?.detail?.error?.message).then(() => {
                            InventoryAgent.taskStatusPublisher.next();
                            InventoryAgent.medicineRefreshPublisher.next();
                            ABCNavigator.pop();
                        });
                    });
                } else if (error instanceof ABCApiError && error?.detail?.error?.code == InventoryApiErrorCode.goodsStockSummaryErrorCode) {
                    //处理12100异常code
                    const { detail } = error?.detail?.error ?? {};
                    loadingDialog.hide().then(() => {
                        showConfirmDialog(
                            `提交失败：${detail?.errorTitle ?? ""}`,
                            <ScrollView>
                                <View>
                                    {detail.errorList
                                        .map((it: { name: string; msgs: string[] }) => `${it.name}:${it.msgs.join("，")}`)
                                        .join("\n")}
                                </View>
                            </ScrollView>
                        );
                    });
                } else {
                    loadingDialog.hide().then(() => {
                        showConfirmDialog("提交失败", error?.detail?.error?.message);
                    });
                }
            });

        this.update();
    }

    @actionEvent(_EventChangeComment)
    private async *_mapEventChangeComment(event: _EventChangeComment): AsyncGenerator<State> {
        const detail = this.innerState.detail;
        const commentItem = JsonMapper.deserialize(InventoryCheckDetailComment, {
            content: event.comment,
        });
        if (!!this._draftId) {
            const req: InventoryCheckDraftReq = {
                instructions: [
                    createOrderRemarkUpdateRequest({
                        comment: event.comment,
                        checkOrderId: this._draftId,
                    }),
                ],
            };
            await InventoryCheckAgent.syncStocksCheckOrderByDraftId(this._draftId, req).catchIgnore();
        }
        if (detail.comment) {
            detail.comment[0] = commentItem;
        } else {
            detail.comment = [];
            detail.comment.push(commentItem);
        }
        this._saveDraft();
        this.innerState.hasChange = true;
        this.update();
    }

    @actionEvent(_EventAddGood)
    private async *_mapEventAddGood(event: _EventAddGood): AsyncGenerator<State> {
        let goodsInfo: GoodsInfo | undefined;
        const isOpenMultiplePharmacy = this.innerState.pharmacyInfoConfig?.isOpenMultiplePharmacy;
        const selectPharmacyNo =
            this.innerState.entryPharmacy?.name == "全部库房" && this.innerState.isDraft
                ? this.innerState.detail?.pharmacy?.no
                : this.innerState.entryPharmacy?.no;
        if (isOpenMultiplePharmacy && _.isUndefined(selectPharmacyNo)) {
            await Toast.show("请先选择盘点库房", { warning: true });
            return;
        }
        this._searchParams && (this._searchParams.pharmacyNo = selectPharmacyNo);
        if (event.qrScan) {
            goodsInfo = await InventoryUtils.BarScan2GoodsInfo(
                this._searchParams ?? { withStock: "1", onlyStock: "1", disable: 0, pharmacyNo: selectPharmacyNo },
                true,
                {
                    title: "扫描条形码/二维码",
                }
            );
        } else {
            goodsInfo = await ABCNavigator.navigateToPage<GoodsInfo | undefined>(
                <InventoryMedicineSearchPage
                    searchParams={this._searchParams ?? { withStock: "1", onlyStock: "1", disable: 0, pharmacyNo: selectPharmacyNo }}
                    callback={(goodsInfo) => ABCNavigator.pop(goodsInfo)}
                />
            );
        }
        if (!goodsInfo || !goodsInfo.id) return;
        goodsInfo = await GoodsAgent.getGoodsInfo(goodsInfo.id, {
            withStock: 1,
            clinicId: userCenter.clinic?.clinicId,
            pharmacyNo: goodsInfo?.pharmacyNo,
        });

        if (goodsInfo.noStocks) {
            await showConfirmDialog(`${goodsInfo.displayName}没有入库`, "需先将药品入库后才能执行盘点");
            return;
        }

        const lockList = await InventoryCheckAgent.lockMedicineCheck(goodsInfo.id!).catchIgnore();
        if (lockList?.rows.length) {
            await showConfirmDialog(`${goodsInfo.displayName}存在未确认的调拨单，需要调入方确认后可进行盘点`, "");
            return;
        }

        let index = 0;
        for (const item of this.innerState.detail?.list ?? []) {
            //判断药品是否相同
            if (item.goods?.id == goodsInfo.id) {
                if (item.batchId == undefined) {
                    this.innerState.scrollKeyIndex = index;
                    Toast.show(`盘点单已存在【${goodsInfo.displayName}】`, { warning: true });
                    yield ScrollSameViewState.fromState(this.innerState);
                    return;
                }
            }
            index++;
        }

        this.dispatch(new _EventAddGoodToList(goodsInfo));
    }

    // 加载线上草稿数据
    private async loadMoreOnlineDraftData(isReload = false): Promise<void> {
        if (!this._draftId || !this.isOnlineDraft || this.innerState.isLoadingMore) {
            return;
        }

        // 如果不是刷新操作，需要检查是否还有更多数据
        if (!isReload && !this.innerState.hasMore) {
            return;
        }

        this.innerState.isLoadingMore = true;
        this.update();

        try {
            const queryDetailParams: InventoryCheckDraftReq = {
                instructions: [
                    createOrderRemarkQueryRequest({
                        checkOrderId: this._draftId,
                    }),
                    createMedicineQueryRequest({
                        offset: this.innerState.currentOffset,
                        limit: this.innerState.limit,
                        checkOrderId: this._draftId,
                    }),
                ],
            };

            const result = await InventoryCheckAgent.syncStocksCheckOrderByDraftId(this._draftId, queryDetailParams);

            // 处理分页响应 - result已经是处理过的数组
            if (Array.isArray(result)) {
                // result是数组，第二个元素是药品查询结果
                const medicineResult = result[1]; // 药品查询结果

                if (medicineResult?.data?.rows) {
                    const newItems = medicineResult.data.rows || [];

                    // 将新数据添加到现有列表中
                    if (this.innerState.detail) {
                        if (!this.innerState.detail.list) {
                            this.innerState.detail.list = [];
                        }

                        if (isReload) {
                            // 如果是刷新，替换现有数据
                            this.innerState.detail.list = newItems;
                        } else {
                            // 如果是加载更多，追加数据
                            this.innerState.detail.list.push(newItems);
                        }

                        // 使用返回的分页信息更新状态
                        const { total = 0, offset = 0, limit = 100 } = medicineResult.data;
                        this.innerState.currentOffset = offset + limit;
                        // 判断是否还有更多数据：当前页数据量等于limit 且 已加载数据总数 < total总数
                        const currentPageDataCount = newItems.length;
                        this.innerState.hasMore = currentPageDataCount === limit && this.innerState.detail.list.length < total;
                    }
                }
            }
        } catch (error) {
            console.error("加载更多数据失败:", error);
        } finally {
            this.innerState.isLoadingMore = false;
            this.update();
        }
    }

    // 处理保存响应
    private handleSaveResponse(response: any): void {
        debugger;
        // 解析API响应结构
        const instructionResult = response?.[0];

        if (instructionResult) {
            if (instructionResult.success) {
                // 更新本地数据
                if (instructionResult.data?.rows) {
                    this.updateLocalDataAfterSave(instructionResult.data.rows);
                }

                // 更新状态
                Toast.show(`已保存云草稿`);
            } else {
                // 处理保存失败
                const errorMessage = instructionResult.error?.message || "保存失败";
                Toast.show(`保存失败: ${errorMessage}`);
            }
        } else {
            // 响应结构异常
            Toast.show(`保存失败: 响应格式异常`);
        }
    }

    // 保存成功后更新本地数据
    private updateLocalDataAfterSave(serverData: any[]): void {
        if (!this.innerState.detail) return;

        // 确保list数组存在
        if (!this.innerState.detail.list) {
            this.innerState.detail.list = [];
        }

        const currentList = this.innerState.detail.list;

        // 遍历服务器返回的数据，更新本地对应的数据
        serverData.forEach((serverItem) => {
            const serverId = serverItem.id;

            if (!serverId) {
                // 如果没有id，直接添加为新数据
                const newItem = JsonMapper.deserialize(CheckTaskGoodItem, {
                    ...serverItem,
                    batchId: !!serverItem.batchId ? Number(serverItem.batchId) : undefined,
                });
                currentList.push(newItem);
                return;
            }

            // 通过id查找本地对应的药品数据
            const localIndex = currentList.findIndex((localItem) => localItem.id === serverId);

            if (localIndex !== -1) {
                // 找到对应的数据，更新本地数据
                const localItem = currentList[localIndex];

                // 更新基本信息
                Object.assign(localItem, {
                    beforePieceCount: serverItem.beforePieceCount,
                    beforePackageCount: serverItem.beforePackageCount,
                    pieceCount: serverItem.pieceCount,
                    packageCount: serverItem.packageCount,
                    pieceCountChange: serverItem.pieceCountChange,
                    packageCountChange: serverItem.packageCountChange,
                    totalCostPriceChange: serverItem.totalCostPriceChange,
                    totalPriceChange: serverItem.totalPriceChange,
                });

                // 处理批次数据
                if (serverItem.batchs && serverItem.batchs.length > 0) {
                    this.updateBatchDataAfterSave(localItem, serverItem.batchs);
                }
            } else {
                // 没找到对应的数据，新增到列表
                const newItem = JsonMapper.deserialize(CheckTaskGoodItem, {
                    ...serverItem,
                    batchId: !!serverItem.batchId ? Number(serverItem.batchId) : undefined,
                });
                currentList.push(newItem);
            }
        });

        // 更新状态
        this.innerState.hasChange = true;
        this.update();
    }

    // 更新批次数据
    private updateBatchDataAfterSave(localItem: any, serverBatches: any[]): void {
        if (!localItem.batchs) {
            localItem.batchs = [];
        }

        serverBatches.forEach((serverBatch) => {
            const serverBatchId = serverBatch.id;

            if (!serverBatchId) {
                // 如果没有id，直接添加为新批次
                localItem.batchs!.push(serverBatch);
                return;
            }

            // 通过id查找本地对应的批次
            const localBatchIndex = localItem.batchs!.findIndex((localBatch) => localBatch.id === serverBatchId);

            if (localBatchIndex !== -1) {
                // 更新现有批次
                const localBatch = localItem.batchs![localBatchIndex];
                Object.assign(localBatch, {
                    beforePackageCount: serverBatch.beforePackageCount,
                    beforePieceCount: serverBatch.beforePieceCount,
                    packageCount: serverBatch.packageCount,
                    pieceCount: serverBatch.pieceCount,
                    totalCostPriceChange: serverBatch.totalCostPriceChange,
                    totalPriceChange: serverBatch.totalPriceChange,
                });
            } else {
                // 新增批次
                localItem.batchs!.push(serverBatch);
            }
        });
    }

    // 增删改增量保存草稿

    private async handleSaveOperate(params: {
        type: string;
        requestParams: {
            [key: string]: any;
        };
    }): Promise<void> {
        const { type, requestParams } = params;
        try {
            // 添加到保存队列并获取响应数据
            const response = await this.saveQueue.addSaveTask(async () => {
                // 构造请求
                const request: InventoryCheckDraftReq = {
                    instructions: [],
                };
                if (type == "UPDATE") {
                    request.instructions?.push(createMedicineUpdateRequest(requestParams));
                } else if (type == "DELETE") {
                    request.instructions?.push(createMedicineDeleteRequest(requestParams));
                }

                // 调用API并返回响应
                return await InventoryCheckAgent.syncStocksCheckOrderByDraftId(this._draftId!, request);
            });

            // 处理响应数据
            this.handleSaveResponse(response);
        } catch (error) {
            // 处理错误
        }
    }

    @actionEvent(_EventAddGoodToList)
    private async *_mapEventAddGoodToList(event: _EventAddGoodToList): AsyncGenerator<State> {
        const goodsInfo = event.goodsInfo;
        const isDrugstoreButler = userCenter.clinic?.isDrugstoreButler;

        const checkGoodItem = JsonMapper.deserialize(CheckTaskGoodItem, {
            goods: goodsInfo,
            beforePieceCount: !isDrugstoreButler ? goodsInfo.pieceCount : undefined, // 这个是所有批次的账面库存，药店需要对应批次，所以需要单独处理
            beforePackageCount: !isDrugstoreButler ? goodsInfo.packageCount : undefined,
            pieceNum: goodsInfo.pieceNum,
            goodsId: goodsInfo.id,
            pharmacyNo:
                this.innerState.entryPharmacy?.name == "全部库房" && this.innerState.isDraft
                    ? this.innerState.detail?.pharmacy?.no
                    : this.innerState.entryPharmacy?.no,
            _draftBeforePieceCount: !isDrugstoreButler ? goodsInfo?.pieceCount : undefined,
            _draftBeforePackageCount: !isDrugstoreButler ? goodsInfo?.packageCount : undefined,
        });
        let status = true;
        let editResult = undefined,
            drugBatchList;

        // 药店--需要将搜索药品现有的所有批次传入进去，否则如果当前已经选了这个药品的其他批次，再次添加时会导致这个批次数据丢失
        if (isDrugstoreButler) {
            const existCurrentGoods = this.innerState.detail?.list?.filter((item) => item?.goods?.id == goodsInfo.id);
            let drugSelectBatchList: CheckTaskGoodItem[] = []; // 当前已选药品批次
            if (existCurrentGoods?.length) {
                drugSelectBatchList = existCurrentGoods.map((item) => {
                    return JsonMapper.deserialize(CheckTaskGoodItem, {
                        ...item,
                        goods: goodsInfo,
                        beforePieceCount: item.beforePackageCount,
                        beforePackageCount: item.beforePieceCount,
                        pieceNum: goodsInfo.pieceNum,
                        goodsId: goodsInfo.id,
                        pharmacyNo:
                            this.innerState.entryPharmacy?.name == "全部库房" && this.innerState.isDraft
                                ? this.innerState.detail?.pharmacy?.no
                                : this.innerState.entryPharmacy?.no,
                        _draftBeforePieceCount: item?._draftBeforePackageCount,
                        _draftBeforePackageCount: item?._draftBeforePackageCount,
                    });
                });
            } else {
                drugSelectBatchList = [checkGoodItem];
            }
            drugBatchList = await showBottomPanel<CheckTaskGoodItem[]>(
                <InventoryCheckMultipleBatchDialog taskGoodItemList={drugSelectBatchList!} />
            );
            if (!drugBatchList?.length) return;
        } else {
            do {
                editResult = await InventoryTaskPopupDialog.show(checkGoodItem);
                if (!editResult) return;
                let index = 0;
                status = false;
                if (!_.isNumber(editResult?.pieceCount) && !_.isNumber(editResult.packageCount)) {
                    status = true;
                    await Toast.show("输入实际库存信息", { warning: true });
                    continue;
                }
                for (const item of this.innerState.detail?.list ?? []) {
                    //判断药品是否相同
                    if (item.goods?.id == goodsInfo.id) {
                        if (editResult.batchId == undefined || item.compareKey() == editResult.compareKey()) {
                            status = true;
                            this.innerState.scrollKeyIndex = index;
                            yield ScrollSameViewState.fromState(this.innerState);
                            if (editResult.batchId == undefined) await Toast.show("当前商品未选择批次");
                            if (item.compareKey() == editResult.compareKey()) await Toast.show("当前商品批次已存在");
                            break;
                        }
                    } else {
                        this.innerState.detail.kindCount = (this.innerState.detail.kindCount ?? 0) + 1;
                    }

                    index++;
                }
            } while (status);
        }

        this.innerState.hasChange = true;
        this.innerState.detail.list = this.innerState.detail?.list ?? [];

        const requestParams = {};

        if (isDrugstoreButler) {
            debugger;
            const batchesList: any[] = [];
            // TODO 如果原来存在这个药品，那么需要传checkOrderItemId（可能是有相同批次、也可能是新增批次）
            drugBatchList?.forEach((batch) => {
                batchesList.push({
                    beforePackageCount: batch?.beforePackageCount,
                    beforePieceCount: batch?.beforePieceCount,
                    packageCount: batch.packageCount ?? 0,
                    pieceCount: batch.pieceCount ?? 0,
                    goodsId: batch?.goodsId,
                    batchId: batch?.batchId,
                    batchNo: batch?.batchNo,
                });
            });
            Object.assign(requestParams, {
                goodsId: goodsInfo?.id ?? goodsInfo?.goodsId,
                beforePackageCount: goodsInfo.packageCount,
                beforePieceCount: goodsInfo?.pieceCount,
                packageCount: goodsInfo?.packageCount ?? 0,
                pieceCount: goodsInfo?.pieceCount ?? 0,
                batchs: batchesList,
                checkOrderId: this._draftId,
            });
            // 查找当前药品是否存在，存在需要删除已有的批次，再追加重新选择的
            // this.innerState.detail.list = this.innerState.detail.list?.filter((item) => (item?.goodsId ?? item.goods?.id) != goodsInfo.id);
            // this.innerState.detail.list = this.innerState.detail.list?.concat(drugBatchList ?? []);
        } else {
            if (editResult) {
                const goodsId = editResult?.goods?.id ?? editResult?.goodsId ?? "",
                    beforePackageCount = !!editResult.batchId ? editResult.beforePackageCount : editResult.goods?.stockPackageCount,
                    beforePieceCount = !!editResult.batchId ? editResult.beforePieceCount : editResult.goods?.stockPieceCount;
                const batches = [
                    {
                        beforePackageCount: beforePackageCount,
                        beforePieceCount: beforePieceCount,
                        packageCount: editResult.packageCount ?? 0,
                        pieceCount: editResult.pieceCount ?? 0,
                        goodsId: goodsId,
                        batchId: editResult?.batchId,
                        batchNo: editResult?.batchNo,
                    },
                ];

                Object.assign(requestParams, {
                    goodsId: goodsId,
                    beforePackageCount: beforePackageCount,
                    beforePieceCount: beforePieceCount,
                    packageCount: editResult.packageCount ?? 0,
                    pieceCount: editResult.pieceCount ?? 0,
                    batchs: batches,
                    checkOrderId: this._draftId,
                });
            }
            // if (editResult) this.innerState.detail.list?.push(editResult);
        }
        debugger;
        // TODO  调用增加增量保存云草稿的接口及对应后续的数据
        await this.handleSaveOperate({ type: "UPDATE", requestParams: requestParams });
        this.innerState.detail.kindCount = this.innerState.detail.kindCount ? this.innerState.detail.kindCount : 1;
        this.asyncCheckGoodsCount();

        this.innerState.scrollKeyIndex = (this.innerState.detail?.list?.length ?? 1) - 1;
        this._saveDraft();

        yield ScrollSameViewState.fromState(this.innerState);
    }

    @actionEvent(_EventModifyMedicineCount)
    private async *_mapEventModifyMedicineCount(event: _EventModifyMedicineCount): AsyncGenerator<State> {
        const index = this.innerState.detail?.list?.findIndex((item) => item.compareKey() == event.goodItem.compareKey());

        let status = true;
        let result: CheckTaskGoodItem | undefined = undefined,
            drugBatchList: CheckTaskGoodItem[] = [],
            drugSelectBatchList: CheckTaskGoodItem[] = []; // 当前已选药品批次
        if (userCenter.clinic?.isDrugstoreButler) {
            // 查找出当前选中药品种类的所有批次
            const existCurentGoods = this.innerState.detail.list?.filter(
                (item) => (item.goodsId ?? item.goods?.id) == (event.goodItem.goodsId ?? event.goodItem?.goods?.id)
            );
            if (existCurentGoods?.length) {
                drugSelectBatchList = existCurentGoods.map((item) => {
                    return JsonMapper.deserialize(CheckTaskGoodItem, {
                        ...item,
                        beforePieceCount: item.beforePackageCount,
                        beforePackageCount: item.beforePieceCount,
                        pieceNum: event.goodItem.pieceNum,
                        goodsId: event.goodItem.id,
                    });
                });
            } else {
                drugSelectBatchList = [event.goodItem];
            }
            drugBatchList = await showBottomPanel<CheckTaskGoodItem[]>(
                <InventoryCheckMultipleBatchDialog
                    taskGoodItemList={drugSelectBatchList}
                    currentSelectBatchId={!!event.goodItem?.batchId ? event.goodItem.batchId.toString() : undefined}
                />
            );
            if (!drugBatchList) return;
        } else {
            do {
                result = await InventoryTaskPopupDialog.show(event.goodItem);
                if (!result) return;
                let index = 0;
                status = false;
                for (const item of this.innerState.detail?.list ?? []) {
                    //判断药品是否相同
                    if (item.goods?.id == event.goodItem.goods?.id) {
                        if (
                            (result.batchId == undefined || item.compareKey() == result.compareKey()) &&
                            item.compareKey() != event.goodItem.compareKey()
                        ) {
                            status = true;
                            this.innerState.scrollKeyIndex = index;
                            yield ScrollSameViewState.fromState(this.innerState);
                            if (result.batchId == undefined) await Toast.show("当前商品未选择批次");
                            if (item.compareKey() == result.compareKey()) await Toast.show("当前商品批次已存在");
                            break;
                        }
                    } else {
                        this.innerState.detail.kindCount = (this.innerState.detail.kindCount ?? 0) + 1;
                    }

                    index++;
                }
            } while (status);
        }
        const requestParams = {};

        if (userCenter.clinic?.isDrugstoreButler) {
            const batchesList: any[] = [];
            // 药店模式下，当前药品的batchs一定存在，批次信息可能新增了或修改了
            drugBatchList?.forEach((batch) => {
                // 在drugSelectBatchList中查找相同batchId的批次
                const existingBatch = drugSelectBatchList.find((selectBatch) => selectBatch.batchId === batch.batchId);

                batchesList.push({
                    beforePackageCount: batch?.beforePackageCount,
                    beforePieceCount: batch?.beforePieceCount,
                    packageCount: batch.packageCount ?? 0,
                    pieceCount: batch.pieceCount ?? 0,
                    goodsId: batch?.goodsId,
                    batchId: batch?.batchId,
                    batchNo: batch?.batchNo,
                    // 如果在drugSelectBatchList中找到相同batchId的批次，则使用其id，否则为undefined（新增批次）
                    checkOrderItemId: existingBatch?.id,
                });
            });
            Object.assign(requestParams, {
                goodsId: event.goodItem?.id ?? event.goodItem?.goodsId,
                beforePackageCount: event.goodItem.packageCount,
                beforePieceCount: event.goodItem?.pieceCount,
                packageCount: event.goodItem?.packageCount ?? 0,
                pieceCount: event.goodItem?.pieceCount ?? 0,
                batchs: batchesList,
                checkOrderId: this._draftId,
                // 传入当前药品项的id，用于标识是更新操作
                checkOrderItemId: event.goodItem?.id,
            });
        } else {
            if (result) {
                const goodsId = result?.goods?.id ?? result?.goodsId ?? "",
                    beforePackageCount = !!result.batchId ? result.beforePackageCount : result.goods?.stockPackageCount,
                    beforePieceCount = !!result.batchId ? result.beforePieceCount : result.goods?.stockPieceCount;

                // 判断是否为批次变更操作
                const isBatchChanged = event.goodItem?.batchId !== result?.batchId;

                // 查找是否存在相同批次的药品项（用于判断是新增批次还是更新批次）
                let checkOrderItemId: string | undefined;
                if (!isBatchChanged && event.goodItem?.batchId === result?.batchId) {
                    // 批次未变，只改数量 - 使用原项id
                    checkOrderItemId = event.goodItem?.id;
                } else if (result?.batchId) {
                    // 批次有变或不指定批次改为指定批次 - 在list中查找是否存在相同批次的项
                    const existingBatchItem = this.innerState.detail.list?.find(
                        (item) => item.goods?.id === result?.goods?.id && item.batchId === result?.batchId
                    );
                    checkOrderItemId = existingBatchItem?.id;
                }
                // 不指定批次的情况，checkOrderItemId保持undefined（新增）

                const batches = [
                    {
                        beforePackageCount: beforePackageCount,
                        beforePieceCount: beforePieceCount,
                        packageCount: result.packageCount ?? 0,
                        pieceCount: result.pieceCount ?? 0,
                        goodsId: goodsId,
                        batchId: result?.batchId,
                        batchNo: result?.batchNo,
                        // 根据批次变化情况传入相应的id
                        checkOrderItemId: checkOrderItemId,
                    },
                ];
                Object.assign(requestParams, {
                    goodsId: goodsId,
                    beforePackageCount: beforePackageCount,
                    beforePieceCount: beforePieceCount,
                    packageCount: result.packageCount ?? 0,
                    pieceCount: result.pieceCount ?? 0,
                    batchs: batches,
                    checkOrderId: this._draftId,
                    // 根据批次变化情况传入相应的id
                    checkOrderItemId: checkOrderItemId,
                });
            }
        }
        // TODO  调用编辑增量保存云草稿的接口及对应后续的数据
        await this.handleSaveOperate({ type: "UPDATE", requestParams: requestParams });

        if (userCenter.clinic?.isDrugstoreButler) {
            // 药店一定是改批次，或者新增了批次，需要对比当前药品的批次，如果list中存在，需要多传一个id，然后用drugBatchList最新的数据替换
            // 查找当前药品是否存在，存在需要删除已有的批次，再追加重新选择的
            // 使用 filter 方法过滤掉要删除的元素
            this.innerState.detail.list = this.innerState.detail.list?.filter(
                (item) => (item?.goodsId ?? item.goods?.id) != (event.goodItem.goodsId ?? event.goodItem?.goods?.id)
            );
            this.innerState.detail.list = this.innerState.detail.list?.concat(drugBatchList ?? []);
        } else {
            if (result) {
                // 如果药品或者批次list中存在的话，传参会多一个当前的id,但是数据用最新的
                if (index != undefined && index >= 0) {
                    result.packageCountChange = (result.packageCount ?? 0) - (result.beforePackageCount ?? 0);
                    result.pieceCountChange = (result.pieceCount ?? 0) - (result.beforePieceCount ?? 0);
                    result.batchs?.forEach((item) => {
                        if (item.batchId == result?.batchId) {
                            item.draftBeforePackageCount = result?._draftBeforePackageCount;
                            item.draftBeforePieceCount = result?._draftBeforePieceCount;
                        }
                    });
                    (this.innerState.detail?.list ?? [])[index] = result;
                }
            }
        }
        this.innerState.hasChange = true;
        this._saveDraft();
        this.update();
    }
    // 同步盘点药品种类数量
    asyncCheckGoodsCount(): void {
        const goodsList = this.innerState.detail.list?.map((item) => item.goods?.id);
        this.innerState.detail.kindCount = [...new Set(goodsList).values()].length;
    }

    @actionEvent(_EventDeleteMedicineCount)
    private async *_mapEventDeleteMedicineCount(event: _EventDeleteMedicineCount): AsyncGenerator<State> {
        const goodsInfo = event.goodsInfo;
        //删除提示框
        const result = await showQueryDialog(
            "是否删除？",
            `${goodsInfo.goods?.displayName}${goodsInfo.batchId ? ` 批次：${goodsInfo.batchId}` : ""}`
        );
        if (result != DialogIndex.positive) return;
        // TODO 删除草稿
        await this.handleSaveOperate({
            type: "DELETE",
            requestParams: {
                checkOrderId: this._draftId,
                checkOrderItemId: goodsInfo.id,
            },
        });

        if (userCenter.clinic?.isDrugstoreButler) {
            const batchIndex = this.innerState.detail.list?.findIndex((item) => item?.batchId === goodsInfo?.batchId);
            this.innerState.detail.list?.splice(batchIndex!, 1);
        } else {
            const index = this.innerState.detail.list!.findIndex((item) => item?.compareKey() === goodsInfo.compareKey());
            this.innerState.detail.list!.splice(index, 1);
        }
        this.asyncCheckGoodsCount();
        this.innerState.hasChange = true;
        this._saveDraft();
        this.update();
    }

    @actionEvent(_EventBackPage)
    private async *_mapEventBackPage(): AsyncGenerator<State> {
        const innerState = this.innerState;
        if (!!this._draftId && innerState.hasChange) {
            if (!!this._searchParams) {
                innerState.detail.stockCheckScope = {
                    typeIdList: this._searchParams?.typeId,
                    customTypeIdList: this._searchParams?.customTypeId,
                    cMSpec: this._searchParams?.cMSpec?.join(","),
                };
            }
            const switchReqData = InventoryCheckDetail.PostCheckOrdersRep(innerState.detail, this._searchParams, this.orderClientUniqKey);
            const req = JsonMapper.deserialize(InventoryCheckReq, {
                ...switchReqData,
                lastModifiedDate: innerState.detail?.lastModifiedDate,
                pharmacyNo: innerState.entryPharmacy?.no,
                stockCheckScope: innerState.detail.stockCheckScope,
            });
            if (!this.innerState._isEditDraft || !this.isOnlineDraft) {
                const result = await showQueryDialog("是否保存", "是否需要保存成草稿");
                if (result == DialogIndex.negative && !!this._draftId) {
                    this.draftManager.removeDraft(this._draftId).then(() => {
                        ABCNavigator.pop();
                    });
                } else if (result == DialogIndex.positive) {
                    InventoryCheckAgent.createCheckOrderDraft(req)
                        .then(async (rsp) => {
                            //保存线上草稿成功,删除本地草稿
                            if (rsp?.code == 200) {
                                await this.draftManager.removeDraft(this._draftId!);
                            }
                            ABCNavigator.pop();
                        })
                        .catch((e) => {
                            showConfirmDialog("保存失败", errorSummary(e));
                        });
                }
            } else {
                if (innerState.hasChange) {
                    const result = await showQueryDialog("提示", "草稿信息发生变动，是否保存？");
                    if (result == DialogIndex.positive) {
                        InventoryCheckAgent.updateCheckOrderDraft(this._draftId!, req)
                            .then(async (rsp) => {
                                //保存线上草稿成功,删除本地草稿
                                if (rsp?.code == 200) {
                                    await this.draftManager.removeDraft(this._draftId!);
                                }
                                ABCNavigator.pop();
                            })
                            .catch((error) => {
                                if (error instanceof ABCApiError && error?.detail?.error?.code == InventoryApiErrorCode.saved) {
                                    showQueryDialog("提示", error?.detail?.error?.message).then((operateResult) => {
                                        if (operateResult == DialogIndex.positive) {
                                            req.forceSubmit = 1;
                                            InventoryCheckAgent.createCheckOrderDraft(req)
                                                .then(async (rsp) => {
                                                    //保存线上草稿成功,删除本地草稿
                                                    if (rsp?.code == 200) {
                                                        await this.draftManager.removeDraft(this._draftId!);
                                                    }
                                                })
                                                .catch((e) => {
                                                    showConfirmDialog("保存失败", errorSummary(e));
                                                });
                                        } else {
                                            this.draftManager.removeDraft(this._draftId!);
                                        }
                                        ABCNavigator.pop();
                                    });
                                } else {
                                    showConfirmDialog("保存失败", errorSummary(error));
                                }
                            });
                    } else {
                        this.draftManager.removeDraft(this._draftId!);
                        ABCNavigator.pop();
                    }
                }
            }
        } else {
            ABCNavigator.pop();
        }
    }

    @actionEvent(_EventReviewCheckHandle)
    private async *_mapEventReviewCheckHandle(event: _EventReviewCheckHandle): AsyncGenerator<State> {
        const { detail } = this.innerState;
        const isDrugstoreButler = userCenter.clinic?.isDrugstoreButler;
        const result = await InventoryCheckReviewDialog.show({
            pass: event.pass,
            needReason: isDrugstoreButler,
            needReasonContTips: isDrugstoreButler && event.pass ? "请输入审核原因" : undefined,
        });
        if (result) {
            if (userCenter.clinic?.isDrugstoreButler) {
                const instId = detail?.gspInstId;
                if (!instId) return;
                const reqMethod = event.pass ? InventoryPharmacyAgent.postApprovalInstPass : InventoryPharmacyAgent.postApprovalInstNotPass;
                reqMethod(instId, {
                    comments: result.comment,
                })
                    .then(() => {
                        InventoryAgent.taskStatusPublisher.next();
                        Toast.show("提交成功", { success: true }).then(() => ABCNavigator.pop());
                    })
                    .catch((e) => {
                        InventoryAgent.taskStatusPublisher.next();
                        showConfirmDialog("", errorToStr(e)).then(() => ABCNavigator.pop());
                    });
            } else {
                //审核请求
                InventoryCheckAgent.putCheckOrderReview(detail.id!, {
                    pass: event.pass ? 1 : 0,
                    comment: result.comment ?? "",
                    lastModifiedDate: detail?.lastModifiedDate,
                })
                    .then(() => {
                        Toast.show("提交成功", { success: true }).then(() => ABCNavigator.pop());
                    })
                    .catch((e) => {
                        showConfirmDialog("", errorToStr(e)).then(() => ABCNavigator.pop());
                    });
            }
        }
    }

    @actionEvent(_EventEditCheckHandle)
    private async *_mapEventEditCheckHandle(/*event: _EventEditCheckHandle*/): AsyncGenerator<State> {
        const innerState = this.innerState,
            id = innerState.detail.id;
        if (!id) return;
        this.innerState.startLoading();
        this.update();
        InventoryCheckAgent.getCheckOrderReEditDetail(id)
            .toObservable()
            .subscribe(
                (rsp) => {
                    this.innerState.stopLoading();
                    this.innerState.type = InventoryCheckCreatePageType.reEdit;

                    this.innerState.detail = rsp.checkOrderDetailWithBatchesToInventoryCheckDetail();

                    /**
                     * @description 【*********】【盘点审核】app 重新编辑【已拒绝，多人全量】的单子时，未能按照单人全量的方式进行盘点。（未提示剩余药品处理方式）
                     */
                    if (rsp.stockCheckScope) {
                        this.innerState.stockCheckAll = true;
                        this._searchParams = {
                            ...this._searchParams,
                            types: _.isNumber(rsp.stockCheckScope.type) ? [rsp.stockCheckScope.type] : undefined,
                            subTypes: _.isNumber(rsp.stockCheckScope.subType) ? [rsp.stockCheckScope.subType] : undefined,
                            cMSpec: _.isString(rsp.stockCheckScope.cMSpec) ? [rsp.stockCheckScope.cMSpec] : undefined,
                            typeId: _.isArray(rsp.stockCheckScope.typeIdList) ? rsp.stockCheckScope.typeIdList : undefined,
                            customTypeId: _.isArray(rsp.stockCheckScope.customTypeIdList)
                                ? rsp.stockCheckScope.customTypeIdList
                                : undefined,
                        };
                    }

                    /**
                     * @hotfixID
                     * @description 清空备注信息
                     * <AUTHOR> jie
                     * @hotfixDate 2021/5/27
                     */
                    this.innerState.detail.comment = [];

                    this.update();
                },
                (error) => {
                    this.innerState.setLoadingError(error);
                    this.update();
                }
            )
            .addToDisposableBag(this);
    }

    @actionEvent(_EventRevokeTransHandle)
    private async *_mapEventRevokeTransHandle(): AsyncGenerator<State> {
        const { detail } = this.innerState;
        const result = await showQueryDialog("", "是否确认撤回该申请，撤回后单据将失效");
        if (result == DialogIndex.positive) {
            InventoryCheckAgent.putCheckOrderRevoke(detail!.id!)
                .then(() => {
                    ABCNavigator.pop();
                })
                .catch((e) => {
                    showConfirmDialog("提示", errorToStr(e));
                });
        }
        this.update();
    }

    @actionEvent(_EventSelectMultiplePharmacy)
    private async *_mapEventSelectMultiplePharmacy(): AsyncGenerator<State> {
        const innerState = this.innerState,
            pharmacyList = innerState?.pharmacyInfoConfig?.inventoryPharmacyList().filter((t) => t?.status == 1 && !!t?.enableCheck);
        const initIndex =
            pharmacyList?.findIndex(
                (item) => item.no == (innerState.isDraft ? innerState?.detail?.pharmacy?.no : innerState.entryPharmacy?.no)
            ) ?? -1;
        const result = await showOptionsBottomSheet({
            title: "选择库房",
            options: pharmacyList?.map((item) => item?.name ?? ""),
            initialSelectIndexes: new Set<number>([initIndex]),
        });
        if (_.isUndefined(result)) return;
        this.innerState.entryPharmacy = JsonMapper.deserialize(PharmacyListItem, pharmacyList?.[result[0]]);
        if (innerState.isDraft) {
            this.innerState.detail.pharmacy = this.innerState.entryPharmacy;
        }
        this._saveDraft();
        this.innerState.hasChange = true;
        this.update();
    }

    @actionEvent(_EventLoadMore)
    private async *_mapEventLoadMore(): AsyncGenerator<State> {
        // 只对线上草稿且还有更多数据时进行加载
        if (this.isOnlineDraft && this.innerState.hasMore && !this.innerState.isLoadingMore) {
            await this.loadMoreOnlineDraftData(false); // 加载更多，不刷新
        }
        this.update();
    }

    @actionEvent(_EventReload)
    private async *_mapEventReload(): AsyncGenerator<State> {
        // 只对线上草稿时进行刷新
        if (this.isOnlineDraft) {
            // 重置分页状态并重新加载
            this.innerState.currentOffset = 0;
            this.innerState.hasMore = true;
            await this.loadMoreOnlineDraftData(true); // 刷新，替换现有数据
        }
        yield this.innerState.clone();
    }

    /**
     * 完成盘点
     * desc: 单人盘点 和 多人盘点
     */
    @runFuncBeforeCheckExpired()
    public requestFinishCheck(): void {
        this.dispatch(new _EventFinishCheck());
    }

    /**
     * 修改盘点单 备注
     * @param comment
     */
    @runFuncBeforeCheckExpired()
    public requestChangeComment(comment: string): void {
        this.dispatch(new _EventChangeComment(comment));
    }

    /**
     * 扫码添加商品
     */
    @runFuncBeforeCheckExpired()
    public requestQRScan(): void {
        this.dispatch(new _EventAddGood(true));
    }

    /**
     * 直接搜索
     */
    @runFuncBeforeCheckExpired()
    public requestSearchMedicine(): void {
        this.dispatch(new _EventAddGood(false));
    }

    /**
     * 修改商品库存数量和批次
     * @param info
     */
    @runFuncBeforeCheckExpired()
    public requestModifyMedicineCount(info: CheckTaskGoodItem): void {
        this.dispatch(new _EventModifyMedicineCount(info));
    }

    /**
     * 删除商品
     * @param info
     */
    @runFuncBeforeCheckExpired()
    public requestDeleteMedicineCount(info: CheckTaskGoodItem): void {
        this.dispatch(new _EventDeleteMedicineCount(info));
    }

    public requestBackPage(fromEdgeGesture?: boolean): void {
        this.dispatch(new _EventBackPage(fromEdgeGesture));
    }

    @runFuncBeforeCheckExpired()
    public requestReviewCheckHandle(status: boolean): void {
        this.dispatch(new _EventReviewCheckHandle(status));
    }

    public requestRevokeTransHandle(): void {
        this.dispatch(new _EventRevokeTransHandle());
    }

    @runFuncBeforeCheckExpired()
    public requestEditCheckHandle(): void {
        this.dispatch(new _EventEditCheckHandle());
    }

    requestSelectMultiplePharmacy(): void {
        this.dispatch(new _EventSelectMultiplePharmacy());
    }

    public requestLoadMore(): void {
        this.dispatch(new _EventLoadMore());
    }

    public requestReload(): void {
        this.dispatch(new _EventReload());
    }
}
