/**
 * create by <PERSON><PERSON>
 * desc: 全量盘点任务单页面
 * create date 2021/2/3
 */
import React from "react";
import { ScrollView, View } from "@hippy/react";
import { BaseBlocNetworkPage } from "../../base-ui/base-page";
import { InventoryFullCheckTaskInvoicePageBloc, ScrollToFocusItemState } from "./inventory-full-check-task-invoice-page-bloc";
import { BlocHelper } from "../../bloc/bloc-helper";
import { ListSettingEditItem, ListSettingItem, ListSettingItemStyle } from "../../base-ui/views/list-setting-item";
import { BaseComponent } from "../../base-ui/base-component";
import { ExecuteCountInventoryItemStatus, StockCheckTaskInfo } from "../data/inventory-bean";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ToolBarButtonStyle1 } from "../../base-ui";
import { AbcTextInput } from "../../base-ui/views/abc-text-input";
import { DeviceUtils } from "../../base-ui/utils/device-utils";
import { AbcCardHeader } from "../../base-ui/abc-app-library/common/abc-card-header";
import { AbcText } from "../../base-ui/views/abc-text";
import { BlocBuilder } from "../../bloc";
import { pxToDp } from "../../base-ui/utils/ui-utils";
import { StringUtils } from "../../base-ui/utils/string-utils";
import { GoodsAgent } from "../../data/goods/goods-agent";
import { GoodsType } from "../../base-business/data/beans";

export interface InventoryFullCheckTaskInvoicePageProps {
    id?: string;
    type?: number;
}

export class InventoryFullCheckTaskInvoicePage extends BaseBlocNetworkPage<
    InventoryFullCheckTaskInvoicePageProps,
    InventoryFullCheckTaskInvoicePageBloc
> {
    private _listView?: ScrollView | null;

    constructor(props: InventoryFullCheckTaskInvoicePageProps) {
        super(props);
        this.bloc = new InventoryFullCheckTaskInvoicePageBloc({ id: props.id, type: props.type });
    }

    getAppBarTitle(): string {
        return "多人盘点任务";
    }

    getAppBar(): JSX.Element | undefined {
        return <BlocBuilder bloc={this.bloc} condition={() => true} build={() => super.getAppBar()} />;
    }

    getRightAppBarIcons(): JSX.Element[] {
        const state = this.bloc.currentState;

        if (state.isMissionCreator || state.isCreate)
            return [
                <AbcText
                    key={"addSubTask"}
                    style={TextStyles.t16NM.copyWith({ lineHeight: Sizes.dp24 })}
                    onClick={() => {
                        this.bloc.requestAddSubTask();
                    }}
                >
                    新增
                </AbcText>,
            ];
        return super.getRightAppBarIcons();
    }

    componentDidMount(): void {
        super.componentDidMount();
        BlocHelper.connectLoadingStatus(this.bloc, this);

        this.bloc.state.subscribe((state) => {
            if (state instanceof ScrollToFocusItemState) {
                this._listView?.scrollToWithDuration(0, (state.focusKey ?? 0) * pxToDp(144), 300);
            }
        });
    }

    private _renderTaskDetailView(): JSX.Element {
        const state = this.bloc.currentState,
            detail = state.detail;
        const checkRangeDisplay = state.checkRangeDisplay;

        const { pharmacyInfoConfig, entryPharmacy, currentPharmacy } = state;
        const isOpenMultiplePharmacy = pharmacyInfoConfig?.isOpenMultiplePharmacy;
        const isCanSelectPharmacy = currentPharmacy?.name == "全部库房";

        if (this.bloc.currentState.isCreate) {
            return (
                <View style={{ paddingLeft: Sizes.listHorizontalMargin }}>
                    <ListSettingItem
                        title={"盘点任务"}
                        bottomLine={true}
                        contentBuilder={() => (
                            <AbcTextInput
                                defaultValue={detail?.taskInfo?.taskName}
                                placeholder={state.isCreate ? "可通过盘点地点命名" : ""}
                                placeholderTextColor={Colors.T4}
                                multiline={false}
                                maxLength={20}
                                editable={state.isCreate}
                                style={{
                                    ...TextStyles.t16NB,
                                    flex: 1,
                                    height: Sizes.dp20,
                                    backgroundColor: Colors.white,
                                    underlineColorAndroid: Colors.white,
                                    paddingLeft: DeviceUtils.isAndroid() ? -6 : 0,
                                }}
                                formatter={[
                                    (oldValue, newValue) => {
                                        if (!oldValue.length) {
                                            return newValue.trim();
                                        } else {
                                            return newValue;
                                        }
                                    },
                                    (oldStr, newStr) => (StringUtils.containsEmoji(newStr) ? oldStr : newStr),
                                ]}
                                onChangeText={(val: string) => {
                                    this.bloc.requestChangeTaskName(val.trim());
                                }}
                            />
                        )}
                    />
                    {isOpenMultiplePharmacy && (
                        <ListSettingItem
                            itemStyle={isCanSelectPharmacy ? ListSettingItemStyle.expandIcon : undefined}
                            bottomLine={true}
                            title={"盘点库房"}
                            style={{ paddingRight: Sizes.dp16 }}
                            content={entryPharmacy?.name == "全部库房" ? undefined : entryPharmacy?.name}
                            onClick={() => state.isCreate && isCanSelectPharmacy && this.bloc.requestSelectMultiplePharmacy()}
                        />
                    )}
                    <ListSettingItem
                        itemStyle={ListSettingItemStyle.expandIcon}
                        style={{ paddingRight: Sizes.listHorizontalMargin }}
                        title={"盘点范围"}
                        content={checkRangeDisplay ?? "全部类型"}
                        onClick={() => {
                            this.bloc.requestChangeTaskCheckScope();
                        }}
                    />
                </View>
            );
        }
        return (
            <View style={{ paddingLeft: Sizes.listHorizontalMargin }}>
                <ListSettingItem title={"盘点任务"} bottomLine={true} content={detail?.taskInfo?.taskName} />
                {isOpenMultiplePharmacy && (
                    <ListSettingItem title={"盘点药房"} bottomLine={true} content={detail?.taskInfo?.pharmacy?.name} />
                )}
                <ListSettingItem title={"盘点范围"} content={checkRangeDisplay ?? "全部范围"} />
            </View>
        );
    }

    private _renderSubTaskListView(): JSX.Element {
        const state = this.bloc.currentState,
            detail = state.detail;
        return (
            <View style={{ flex: 1 }}>
                <DividerLine lineHeight={Sizes.dp8} color={Colors.window_bg} />
                <ScrollView style={{ flex: 1 }} ref={(ref) => (this._listView = ref)}>
                    {detail?.subTaskInfoItems?.map((data, index) => (
                        <_SubTaskItemEditView key={index} isCreate={state.isCreate} taskInfo={data} index={index!} />
                    ))}
                </ScrollView>
            </View>
        );
    }

    private _renderBtn(): JSX.Element {
        const state = this.bloc.currentState;
        if (state.isCreate)
            return (
                <ToolBar hideWhenKeyboardShow={true}>
                    <ToolBarButtonStyle1
                        text={"完成"}
                        onClick={() => {
                            this.bloc.requestSubmitCreateTask();
                        }}
                    />
                </ToolBar>
            );
        if (!state.isMissionCreator) return <View />;
        return (
            <ToolBar hideWhenKeyboardShow={true}>
                <ToolBarButtonStyle1
                    text={"查看汇总表并完成"}
                    onClick={
                        state.disabledCollect
                            ? undefined
                            : () => {
                                  this.bloc.requestFinishCheck();
                              }
                    }
                />
                <ToolBarButtonStyle1
                    text={"终止盘点"}
                    onClick={() => {
                        this.bloc.requestStopCheckTask();
                    }}
                />
            </ToolBar>
        );
    }

    renderContent(): JSX.Element | undefined {
        return (
            <View style={{ flex: 1 }}>
                <View style={{ backgroundColor: Colors.white }}>{this._renderTaskDetailView()}</View>
                {this._renderSubTaskListView()}
                {this._renderBtn()}
            </View>
        );
    }
}

interface _SubTaskItemViewProps {
    index: number;
    taskInfo: StockCheckTaskInfo;
    isCreate?: boolean;
}

class _SubTaskItemEditView extends BaseComponent<_SubTaskItemViewProps> {
    static contextType = InventoryFullCheckTaskInvoicePageBloc.Context;

    constructor(props: _SubTaskItemViewProps) {
        super(props);
    }

    private _renderBtn(): JSX.Element {
        const { taskInfo, isCreate } = this.props;
        const bloc = InventoryFullCheckTaskInvoicePageBloc.fromContext(this.context),
            state = bloc.currentState;
        if (isCreate)
            return (
                <AbcText
                    style={state.detail?.subTaskInfoItems?.length == 1 ? TextStyles.t16NT2 : TextStyles.t16NR2}
                    onClick={() => {
                        bloc.requestDeleteSubTask(taskInfo);
                    }}
                >
                    删除
                </AbcText>
            );
        const buttons: JSX.Element[] = [];
        if (state.isMissionCreator) {
            if (taskInfo.status == ExecuteCountInventoryItemStatus.taskCompleted)
                buttons.push(
                    <AbcText
                        key={"detail"}
                        style={[TextStyles.t16NB1, { marginHorizontal: Sizes.dp4 }]}
                        onClick={() => {
                            bloc.requestShowSubTaskDetail(taskInfo);
                        }}
                    >
                        详情
                    </AbcText>
                );
            buttons.push(
                <AbcText
                    key={"delete"}
                    style={[TextStyles.t16NR2, { marginHorizontal: Sizes.dp4 }]}
                    onClick={() => bloc.requestDeleteSubTask(taskInfo)}
                >
                    删除
                </AbcText>
            );
        } else {
            buttons.push(
                <AbcText
                    key={"modify"}
                    style={[TextStyles.t16NB1, { marginHorizontal: Sizes.dp4 }]}
                    onClick={() => {
                        bloc.requestCheckSubTask(taskInfo);
                    }}
                >
                    {taskInfo.status == ExecuteCountInventoryItemStatus.taskCompleted ? "修改" : "执行"}
                </AbcText>
            );
        }
        return <View style={[ABCStyles.rowAlignCenter, { alignSelf: "flex-start" }]}>{buttons}</View>;
    }

    render() {
        const { taskInfo, index, isCreate } = this.props;
        const bloc = InventoryFullCheckTaskInvoicePageBloc.fromContext(this.context);
        let statusColor = Colors.Y2;
        if (taskInfo.status == ExecuteCountInventoryItemStatus.taskCompleted) {
            statusColor = Colors.T2;
        } else if (taskInfo.status == ExecuteCountInventoryItemStatus.pendingSales) {
            statusColor = Colors.mainColor;
        }
        return (
            <View>
                <AbcCardHeader
                    style={{ height: Sizes.listItemHeight, backgroundColor: Colors.white }}
                    title={`子任务${index + 1}`}
                    titleStyle={TextStyles.t16MM}
                    showTopDivider={!!index}
                    showCardLeftLine={true}
                    showBottomDivider={true}
                    titleSuffix={() =>
                        !isCreate ? (
                            <AbcText style={[TextStyles.t14NY2.copyWith({ color: statusColor }), { marginLeft: Sizes.dp8 }]}>
                                {taskInfo.statusName ?? ""}
                            </AbcText>
                        ) : undefined
                    }
                    rightRender={() => this._renderBtn()}
                />
                <View style={{ paddingHorizontal: Sizes.listHorizontalMargin, backgroundColor: Colors.white }}>
                    {isCreate ? (
                        <ListSettingEditItem
                            editable={isCreate}
                            title={`任务名`}
                            contentHint={"可通过盘点地点命名"}
                            content={taskInfo.taskName}
                            formatter={(oldStr, newStr) => (StringUtils.containsEmoji(newStr) ? oldStr : newStr)}
                            maxLength={30}
                            bottomLine={true}
                            onChanged={(text) => {
                                bloc.requestChangeSubTaskName(text.trim(), taskInfo);
                            }}
                        />
                    ) : (
                        <ListSettingItem
                            title={`任务名`}
                            contentStyle={{ paddingVertical: Sizes.dp12 }}
                            contentTextStyle={TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp24 })}
                            bottomLine={true}
                            contentHint={"可通过盘点地点命名"}
                            content={taskInfo.taskName}
                        />
                    )}
                    <ListSettingItem
                        title={"负责人"}
                        contentHint={"选择负责人"}
                        itemStyle={this.props.isCreate ? ListSettingItemStyle.expandIcon : undefined}
                        content={taskInfo.ownerName}
                        bottomLine={true}
                        onClick={
                            isCreate
                                ? () => {
                                      bloc.requestChangeSubTaskOwner(taskInfo);
                                  }
                                : undefined
                        }
                    />
                </View>
            </View>
        );
    }
}
