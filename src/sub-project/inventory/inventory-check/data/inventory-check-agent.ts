/**
 * create by <PERSON><PERSON>
 * desc:
 * create date 2021/2/2
 */
import {
    CheckOrderDetailWithBatches,
    InventoryCheckDetail,
    InventoryCheckDraftDetail,
    InventoryCheckDraftRsp,
    InventoryCheckReq,
    PostGoodsStocksCheckOrdersRep,
} from "./inventory-check-bean";
import { ABCApiNetwork } from "../../../net";
import {
    InventoryCheckDraftIncrementalItem,
    InventoryCheckDraftReq,
    PostStockCheckTaskReq,
    StockCheckScope,
    StockCheckTaskInfo,
} from "../../data/inventory-bean";
import { InventoryAgent } from "../../data/inventory-agent";

export interface CreateCheckTaskWorkReq {
    subTaskItems?: StockCheckTaskInfo[];
    stockCheckScope: StockCheckScope;
    taskName: string;
    type?: number;
    taskId?: string;
    pharmacyNo?: number;
}

interface LockMedicineCheckRsp {
    rows: [];
}

interface LockMedicineCheckRsp {
    rows: [];
}

export class putCheckOrderEditReq {
    id?: string;
}

export class InventoryCheckAgent {
    /**
     * 创建盘点单
     * @param params
     */
    static async postGoodsStocksCheckOrders(params: PostGoodsStocksCheckOrdersRep): Promise<InventoryCheckDetail> {
        return ABCApiNetwork.post("goods/stocks/check/orders", {
            body: params,
            clazz: InventoryCheckDetail,
            clearUndefined: true,
        });
    }

    /**
     * 盘点中判断当前药品是否处于锁库状态
     * @param goodsId
     */
    static lockMedicineCheck(goodsId: string): Promise<LockMedicineCheckRsp> {
        return ABCApiNetwork.get("goods/stocks/check/orders/check/lock", { queryParameters: { goodsId } });
    }

    /**
     * 删除盘点任务中的子任务
     * @param taskId
     */
    static deleteCheckSubTask(taskId: string): Promise<void> {
        return ABCApiNetwork.delete(`goods/stock/check/cowork/task/${taskId}`);
    }

    /**
     * 创建盘点任务
     * @param params
     */
    static createCheckTaskWork(params: CreateCheckTaskWorkReq): Promise<void> {
        if (!params.taskId) {
            delete params.taskId;
        }
        return ABCApiNetwork.post(`goods/stock/check/cowork/task`, { body: params, clearUndefined: true });
    }

    static postCheckCoworkJobDraftByTaskId(options: PostStockCheckTaskReq): Promise<void> {
        return ABCApiNetwork.post(`goods/stock/check/cowork/job/${options.taskId}`, { body: options });
    }

    static putCheckOrderReview(orderId: string, params: { pass: number; comment?: string; lastModifiedDate?: Date }): Promise<void> {
        return ABCApiNetwork.put(`goods/stocks/check/orders/${orderId}/review`, { body: params })
            .then(() => InventoryAgent.taskStatusPublisher.next())
            .catch(() => InventoryAgent.taskStatusPublisher.next());
    }

    static putCheckOrderRevoke(orderId: string): Promise<void> {
        return ABCApiNetwork.put(`goods/stocks/check/orders/${orderId}/revoke`);
    }

    static putCheckOrderEdit(params: putCheckOrderEditReq): Promise<void> {
        const { id } = params;
        return ABCApiNetwork.put(`goods/stocks/check/orders/${id}`);
    }

    static getCheckOrderReEditDetail(orderId: string): Promise<CheckOrderDetailWithBatches> {
        return ABCApiNetwork.get(`goods/stocks/check/orders/${orderId}/batches`, { clazz: CheckOrderDetailWithBatches });
    }

    /**
     * 查询所有库存盘点单草稿
     */
    static queryCheckOrdersDraft(): Promise<InventoryCheckDraftRsp> {
        return ABCApiNetwork.get(`goods/stocks/check/orders/draft`, { clazz: InventoryCheckDraftRsp });
    }

    /**
     * 新增库存盘点单草稿
     */
    static createCheckOrderDraft(req: InventoryCheckReq): Promise<{ code?: number; message?: string }> {
        return ABCApiNetwork.post(`goods/stocks/check/orders/draft`, { body: req, clearUndefined: true });
    }

    /**
     * 修改库存盘点单草稿
     */
    static updateCheckOrderDraft(id: string, req: InventoryCheckReq): Promise<{ code?: number; message?: string }> {
        return ABCApiNetwork.put(`goods/stocks/check/orders/draft/${id}`, { body: req, clearUndefined: true });
    }

    /**
     *查看库存盘点单草稿详情
     */
    static queryCheckOrderDraftDetail(id: string): Promise<InventoryCheckDraftDetail> {
        return ABCApiNetwork.get(`goods/stocks/check/orders/draft/${id}`, { clazz: InventoryCheckDraftDetail });
    }

    /**
     * 删除库存盘点单草稿
     */
    static deleteCheckOrderDraft(id: string): Promise<{ code?: number; message?: string }> {
        return ABCApiNetwork.delete(`goods/stocks/check/orders/draft/${id}`);
    }

    /**
     * 增量同步编辑盘点单草稿
     * @param draftId
     * @param params
     */
    static async syncStocksCheckOrderByDraftId(
        draftId: string,
        params: InventoryCheckDraftReq
    ): Promise<InventoryCheckDraftIncrementalItem[]> {
        const rsp: { instructionResults: InventoryCheckDraftIncrementalItem[] } = await ABCApiNetwork.post(
            `goods/stocks/check/orders/draft/${draftId}/sync`,
            {
                body: params,
                clearUndefined: true,
            }
        );
        return rsp?.instructionResults ?? [];
    }
}
