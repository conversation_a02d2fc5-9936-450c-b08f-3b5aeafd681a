/**
 * create by <PERSON><PERSON>
 * desc:
 * create date 2021/1/29
 */

import { fromJsonToDate, JsonMapper, JsonProperty } from "../../../common-base-module/json-mapper/json-mapper";
import {
    GoodsBatchInfo,
    GoodsCheckOrdersItem,
    GoodsCheckOrdersItemCreatedUser,
    GoodsCheckOrdersItemOrgan,
    InventoryPharmacyInfo,
    PostStockCheckTaskReqItemsItem,
    SearchParams,
    StockCheckTaskInfo,
    SymbolSign,
} from "../../data/inventory-bean";
import _ from "lodash";
import { ABCUtils } from "../../../base-ui/utils/utils";
import { GoodsInfo, TraceableCodeList } from "../../../base-business/data/beans";
import { UniqueKey } from "../../../base-ui";
import { userCenter } from "../../../user-center";

export enum InventoryCheckType {
    some = 1, // 临时
    all = 2, //  全量
}

export enum InventoryCheckCreatePageType {
    create = "create",
    detail = "detail",
    reEdit = "reEdit",
}

export enum InventoryCheckDetailStatus {
    create = 0,
    waitVerify = 10, // 待审核
    refused = 20, //审核拒绝
    finish = 30, //审核通过
    revoke = 40, //撤回
    onlineDraft = -30, //线上草稿
    draft = -20, //本地草稿
}

export enum InventoryCheckEnterType {
    manual = 1,
    onlineDraft = 2, //线上草稿
}

export interface InventoryCheckDetailOrgan {
    id?: string;
    parentId?: string;
    nodeType?: number;
    name?: string;
    shortName?: string;
}

export interface InventoryCheckDetailCreatedUser {
    id: string;
    name: string;
}

export class InventoryCheckDetailComment {
    @JsonProperty({ fromJson: fromJsonToDate })
    time?: Date;
    content?: string;
    employeeId?: string;
}

// 日志详情
export class InventoryCheckDetailLogs {
    id?: string;
    orderId?: string;
    action?: string; // 日志 动作名 （ 撤回 | 创建入库单 | 审核通过 | 审核不通过 ）
    comment?: string; // 日志 备注
    createdDate?: Date; // 日志动作创建时间
    createdUser?: InventoryCheckDetailCreatedUser;
}

class CheckBatchesItem {
    batchId?: number;
    //最新库存量
    beforePackageCount?: number;
    beforePieceCount?: number;
    //上次草稿的库存量
    draftBeforePackageCount?: number;
    draftBeforePieceCount?: number;
    goodsId?: string;
    id?: string;
    packageCount?: number;
    pieceCount?: number;
    pieceNum?: number;
    @JsonProperty({ type: Array, clazz: TraceableCodeList })
    traceableCodeList?: TraceableCodeList[];
    totalCostPriceChange?: number;
    totalPriceChange?: number;
}
/**
 * 盘点任务中，盘点的一个药品相关信息
 */
class CheckTaskGoodItem {
    id?: string;
    batchId?: number;
    goodsId?: string;
    pieceNum?: number;
    @JsonProperty({ type: GoodsInfo })
    goods?: GoodsInfo;
    beforePieceCount?: number;
    beforePackageCount?: number;
    pieceCount?: number;
    packageCount?: number;
    packageCountChange?: number;
    pieceCountChange?: number;
    packageCostPrice?: any;
    packagePrice?: number;
    piecePrice?: number;
    totalCostPriceChange?: any;
    totalPriceChange?: number;
    batchNo?: string;
    @JsonProperty({ type: Array, clazz: CheckBatchesItem })
    batchs?: CheckBatchesItem[];
    //终端自定义字段，用于请求参数
    draftBeforePackageCount?: number;
    draftBeforePieceCount?: number;
    _draftBeforePackageCount?: number;
    _draftBeforePieceCount?: number;
    @JsonProperty({ type: Array, clazz: TraceableCodeList })
    traceableCodeList?: TraceableCodeList[];

    compareKey(): string {
        return `${this.goods?.id ?? UniqueKey()}${this.batchId}`;
    }

    __batchInfo?: GoodsBatchInfo;

    pharmacyNo?: number;

    displayStockInfo(goodsInfo: GoodsInfo, batchInfo: GoodsBatchInfo): string {
        let stock = "";
        const packageCount = batchInfo.packageCount ?? 0;
        const pieceCount = batchInfo.pieceCount ?? 0;
        if (packageCount > 0) {
            stock = `${packageCount}${goodsInfo.packageUnit}`;
        }

        if (pieceCount > 0) {
            stock = `${stock} ${pieceCount} ${goodsInfo.pieceUnit}`;
        }
        if (!stock.length) {
            return `0${this.goods?.packageUnit}0${this.goods?.pieceUnit}`;
        }
        return stock;
    }

    static stockInfoChange(checkTask: CheckTaskGoodItem, oldCheckTask: CheckTaskGoodItem): CheckTaskGoodItem {
        const count = checkTask.changeCount;
        checkTask.packageCountChange = Math.floor(count / (checkTask.goods?.pieceNum ?? 0));
        checkTask.pieceCountChange = count % (checkTask.goods?.pieceNum ?? 0);
        checkTask.beforePieceCount = checkTask.__batchInfo?.pieceCount ?? oldCheckTask.beforePieceCount;
        checkTask.beforePackageCount = checkTask.__batchInfo?.packageCount ?? oldCheckTask.beforePackageCount;
        return checkTask;
    }

    get changeCount(): number {
        const _beforePackageCount = this.beforePackageCount ?? 0;
        const _beforePieceCount = this.beforePieceCount ?? 0;
        const _packageCount = this.packageCount ?? 0;
        const _pieceCount = this.pieceCount ?? 0;
        return (
            _packageCount * (this.goods?.pieceNum ?? 0) +
            _pieceCount -
            (_beforePackageCount * (this.goods?.pieceNum ?? 0) + _beforePieceCount)
        );
    }

    get beforeStockDisplay(): string {
        let stockStr = "";
        if (this.beforePackageCount) {
            stockStr = `${this.beforePackageCount}${this.goods?.packageUnit}`;
        }
        if (this.beforePieceCount) {
            stockStr += `${this.beforePieceCount}${this.goods?.pieceUnit}`;
        }
        if (!stockStr.length) {
            return `${this.goods?.packageUnit ? `0${this.goods?.packageUnit}` : ""}0${this.goods?.pieceUnit}`;
        }
        return stockStr;
    }

    get currentStockDisplay(): string {
        let stockStr = "";
        if (this.packageCount != undefined && this.goods?.packageUnit) {
            stockStr = `${this.packageCount}${this.goods?.packageUnit}`;
        }
        if (this.pieceCount != undefined && this.goods?.pieceUnit) {
            stockStr += `${this.pieceCount}${this.goods?.pieceUnit}`;
        }
        return stockStr;
    }

    // 返回当前盘点盈亏数量
    get stockChangeMessage(): { sign: SymbolSign; packageCountChange: number; pieceCountChange: number } {
        const obj = {
            sign: SymbolSign.nolossNogain,
            packageCountChange: 0,
            pieceCountChange: 0,
        };
        if (this.changeCount > 0) {
            obj.sign = SymbolSign.add;
        } else if (this.changeCount == 0) {
            obj.sign = SymbolSign.nolossNogain;
        } else if (this.changeCount < 0) {
            obj.sign = SymbolSign.negative;
        }

        let pieceCountChange = 0;
        let packageCountChange = 0;
        if (!this.goods?.isChineseMedicine) {
            packageCountChange = parseInt((this.changeCount / (this.goods?.pieceNum ?? 0)).toString());
            pieceCountChange = Number((this.changeCount % (this.goods?.pieceNum ?? 0)).toFixed(4));
        } else {
            pieceCountChange = Number(this.changeCount.toFixed(4));
        }

        obj.packageCountChange = packageCountChange;
        obj.pieceCountChange = pieceCountChange;

        return obj;
    }

    get stockChangeDisplay(): string {
        let stockStr = "";
        if (this.changeCount > 0) {
            stockStr = "+";
        } else if (this.changeCount == 0) {
            return `0${this.goods?.packageUnit?.length ? this.goods?.packageUnit : this.goods?.pieceUnit ?? ""}`;
        } else if (this.changeCount < 0) {
            stockStr = "-";
        }

        let pieceCountChange = 0;
        let packageCountChange = 0;
        if (!this.goods?.isChineseMedicine) {
            packageCountChange = parseInt((this.changeCount / (this.goods?.pieceNum ?? 0)).toString());
            pieceCountChange = Number(Math.abs(this.changeCount % (this.goods?.pieceNum ?? 0)).toFixed(4));
        } else {
            pieceCountChange = Number(this.changeCount.toFixed(4));
        }

        if (packageCountChange) {
            stockStr += `${Math.abs(packageCountChange)}${this.goods?.packageUnit}`;
        }
        if (pieceCountChange) {
            stockStr += `${Math.abs(pieceCountChange)}${this.goods?.pieceUnit}`;
        }
        return stockStr;
    }
}

export class InventoryCheckDetail {
    id?: string;
    organId?: string;
    orderNo?: string;

    @JsonProperty({ fromJson: fromJsonToDate })
    createdDate?: Date;
    kindCount?: number;
    beforeCount?: number;
    afterCount?: number;
    beforeCostAmount?: number;
    afterCostAmount?: number;
    beforeSaleAmount?: number;
    afterSaleAmount?: number;
    beforeCostAmountExcludingTax?: number;
    afterCostAmountExcludingTax?: number;
    beforeSaleAmountExcludingTax?: number;
    afterSaleAmountExcludingTax?: number;
    totalCostPriceChange?: number;
    totalPriceChange?: number;

    @JsonProperty({ type: Array, clazz: InventoryCheckDetailComment })
    comment?: InventoryCheckDetailComment[];

    createdUserId?: string;
    organ?: InventoryCheckDetailOrgan;

    @JsonProperty({ type: Array, clazz: CheckTaskGoodItem })
    list?: CheckTaskGoodItem[];
    createdUser?: InventoryCheckDetailCreatedUser;
    status?: InventoryCheckDetailStatus;
    statusName?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    lastModifiedDate?: Date;

    @JsonProperty({ type: Array, clazz: InventoryCheckDetailLogs })
    logs?: InventoryCheckDetailLogs[];

    @JsonProperty({ clazz: InventoryPharmacyInfo })
    pharmacy?: InventoryPharmacyInfo;
    gspInstId?: string; //药店相关

    __draftId?: string;
    //终端自定义字段，用于单人盘点中盘点范围的记录
    __searchParams?: {
        //二级分类需求
        typeId?: string[];
        customTypeId?: string[];
    };
    stockCheckScope?: StockCheckScope;

    /**
     * 生成盘点药品列表的summary
     * @param canViewPrice 是否能查看盘点药品成本价格
     * @returns
     */
    displayListSummary(canViewPrice?: boolean): string {
        const str: string[] = [];
        if (this.kindCount) {
            str.push(`品种：${this.kindCount}种`);
        }
        // 如果当前是药店，创建、待审核（临时盘点、全量盘点单）可查看盈亏进价金额，是否能查看盈亏进价受数据权限中有无进价查看权限控制
        // 具体计算是根据list中的packageCostPrice代表进价,packagePrice代表售价，然后将小单位转换为大单位来计算，最后采取向下保留两位小数
        // 是否是新建以及本地草稿
        const isCreate = this.status == undefined || this.status == InventoryCheckDetailStatus.draft;
        const isDrugstoreButler = userCenter.clinic?.isDrugstoreButler;
        if (isDrugstoreButler && isCreate) {
            const totalCostPrice = this.list?.reduce((prev, next) => {
                const profitAndLossInfo = next.stockChangeMessage;
                const { sign, packageCountChange, pieceCountChange } = profitAndLossInfo;
                let result = 0;
                const currentCostPrice =
                    ((pieceCountChange ?? 0) / (next.pieceNum ?? 1) + packageCountChange) * (next?.packageCostPrice ?? 0);
                if (sign == SymbolSign.add) {
                    result = prev + currentCostPrice;
                } else if (sign == SymbolSign.negative) {
                    result = prev - Math.abs(currentCostPrice);
                } else {
                    result = prev + currentCostPrice;
                }
                return Math.floor(result * 100) / 100;
            }, 0);
            const saleTotalPrice = this.list?.reduce((prev, next) => {
                const profitAndLossInfo = next.stockChangeMessage;
                const { sign, packageCountChange, pieceCountChange } = profitAndLossInfo;
                let result = 0;
                const currentCostPrice = ((pieceCountChange ?? 0) / (next.pieceNum ?? 1) + packageCountChange) * (next?.packagePrice ?? 0);
                if (sign == SymbolSign.add) {
                    result = prev + currentCostPrice;
                } else if (sign == SymbolSign.negative) {
                    result = prev - Math.abs(currentCostPrice);
                } else {
                    result = prev + currentCostPrice;
                }
                return Math.floor(result * 100) / 100;
            }, 0);
            str.push(
                `盈亏(进/售):${
                    _.isNumber(totalCostPrice) && !!canViewPrice ? ABCUtils.inventoryPriceWithRMB({ price: totalCostPrice }) : "--"
                } / ${_.isNumber(saleTotalPrice) ? ABCUtils.inventoryPriceWithRMB({ price: saleTotalPrice, showSymbols: false }) : "--"}`
            );
        } else if (this.status != InventoryCheckDetailStatus.refused) {
            /**
             * @description 已拒绝的单子不显示盈亏金额
             */
            str.push(
                `盈亏(进/售):${
                    _.isNumber(this.totalCostPriceChange) &&
                    (!isDrugstoreButler ? this.status != InventoryCheckDetailStatus.waitVerify : true) &&
                    !!canViewPrice
                        ? ABCUtils.inventoryPriceWithRMB({ price: this.totalCostPriceChange })
                        : "--"
                } / ${
                    _.isNumber(this.totalPriceChange)
                        ? ABCUtils.inventoryPriceWithRMB({ price: this.totalPriceChange, showSymbols: false })
                        : "--"
                }`
            );
        }

        return str.join("，");
    }

    static PostCheckOrdersRep(
        data: InventoryCheckDetail,
        searchParams?: SearchParams,
        orderClientUniqKey?: string
    ): PostGoodsStocksCheckOrdersRep {
        const postReq = new PostGoodsStocksCheckOrdersRep();
        postReq.list = [];

        const goodsInfoMap: Map<string, CheckTaskGoodItem[]> = new Map<string, CheckTaskGoodItem[]>();
        data.list?.forEach((item) => {
            const goodsId = item.goods?.id ?? item.goodsId ?? "";
            item.goodsId = goodsId;
            //批次的beforeChange内容为整个药品的stock(不指定批次是整个药品的库存)
            item.beforePackageCount = !!item.batchId ? item.beforePackageCount : item.goods?.stockPackageCount;
            item.beforePieceCount = !!item.batchId ? item.beforePieceCount : item.goods?.stockPieceCount;
            //
            if (goodsInfoMap.has(goodsId)) {
                goodsInfoMap.get(goodsId)?.push(item);
            } else {
                goodsInfoMap.set(goodsId, [item]);
            }
        });
        goodsInfoMap.forEach((item) => {
            const _item = _.cloneDeep(item);
            postReq.list!.push({
                batchs: _item.map((batchItem) => {
                    delete batchItem.goods;
                    delete batchItem.__batchInfo;
                    delete batchItem.packageCountChange;
                    delete batchItem.pieceCountChange;
                    batchItem.beforePackageCount = batchItem.beforePackageCount ?? 0;
                    batchItem.beforePieceCount = batchItem.beforePieceCount ?? 0;
                    batchItem.packageCount = batchItem.packageCount ?? 0;
                    batchItem.pieceCount = batchItem.pieceCount ?? 0;
                    return batchItem;
                }),
            });
        });
        //过滤掉batchs为空的数据
        postReq.list = postReq.list?.filter((item) => !!item.batchs?.length);

        postReq.comment = data.comment?.[0]?.content ?? "";
        postReq.clearType = "";
        if (searchParams) {
            postReq.isCheckScope = 1;
            //@ts-ignore
            postReq.type = searchParams.types?.[0] ?? "";
            //@ts-ignore
            postReq.subType = searchParams.subTypes?.[0] ?? "";
            postReq.cMSpec = searchParams.cMSpec?.[0] ?? "";
            postReq.typeId = searchParams.typeId;
            postReq.customTypeId = searchParams.customTypeId;
            postReq.typeIdList = searchParams.typeId;
            postReq.customTypeIdList = searchParams.customTypeId;
            postReq.pharmacyNo = searchParams.pharmacyNo;
        }
        if (data?.stockCheckScope) {
            postReq.customTypeIdList = data?.stockCheckScope?.customTypeIdList;
            postReq.typeIdList = data?.stockCheckScope?.typeIdList;
            postReq.cMSpec = data?.stockCheckScope?.cMSpec;
        }
        postReq.orderClientUniqKey = orderClientUniqKey;
        return postReq;
    }
}

export class PostGoodsStocksCheckOrdersRep {
    cMSpec?: string | string[];
    type?: number | number[];
    subType?: number | number[];
    clearType?: string;
    comment?: string;
    isCheckScope?: number; //0   临时盘点   ；1全量盘点
    @JsonProperty({ type: Array, clazz: PostStockCheckTaskReqItemsItem })
    list?: PostStockCheckTaskReqItemsItem[];
    submitFlag?: number; //盘点提交时 1-->全盘为0      2-->保持账面库存

    /**
     * @description 可选参数 前端用于标识请求 主要用于解决盘点请求超过25s后，第二次继续点确定按钮 再次提交产生第二个盘点单的情况
     * <AUTHOR> jie
     * @hotfixDate 2021/5/26
     */
    orderClientUniqKey?: string;

    /**
     * 二级分类 列表
     */
    typeId?: string[];
    customTypeId?: string[];
    typeIdList?: string[];
    customTypeIdList?: string[];
    pharmacyNo?: number;
    checkOrderDraftId?: string;
    forceSubmit?: number;
    // 收费；影响的锁库里表-产品上这个路径上的盘点直接兑不审核，所以也不需要落地，盘点完直接改锁库信息(目前只有收费选择医保支付方式的时候才有用,app暂时不支持医保)
    affectedLockIdList?: string[];
    opType?: number; // 盘点操作方式 0-, 1-盘点变更量
}

export class CheckTaskDetails {
    @JsonProperty({ type: StockCheckTaskInfo })
    taskInfo?: StockCheckTaskInfo;

    @JsonProperty({ type: Array, clazz: StockCheckTaskInfo })
    subTaskInfoItems?: StockCheckTaskInfo[];
}

export class CheckOrderDetailBatchListItem {
    id?: string;
    batchNo?: string;
    batchId?: number;
    stockId?: string;
    goodsId?: string;
    pieceNum?: number;
    beforePackageCount?: number;
    beforePieceCount?: number;
    packageCostPrice?: number;
    totalCostPriceChange?: number;
    totalPriceChange?: number;
    packageCount?: number;
    pieceCount?: number;
    packageCountChange?: number;
    pieceCountChange?: number;
}

export class CheckOrderDetailWithBatchesListItem {
    goods?: GoodsInfo;
    beforePieceCount?: number;
    beforePackageCount?: number;
    @JsonProperty({ type: Array, clazz: CheckOrderDetailBatchListItem })
    batchs?: CheckOrderDetailBatchListItem[];
}

class StockCheckScope {
    type?: number;
    subType?: number;
    cMSpec?: string;

    typeIdList?: string[];
    customTypeIdList?: string[];
}

export class CheckOrderDetailWithBatches {
    id?: string;
    orderNo?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    createdDate?: Date;
    createdUser?: InventoryCheckDetailCreatedUser;
    kindCount?: number;
    status?: number;
    statusName?: string;

    @JsonProperty({ type: Array, clazz: InventoryCheckDetailComment })
    comment?: InventoryCheckDetailComment[];

    organ?: InventoryCheckDetailOrgan;

    @JsonProperty({ type: Array, clazz: CheckOrderDetailWithBatchesListItem })
    list?: CheckOrderDetailWithBatchesListItem[];

    @JsonProperty({ type: Array, clazz: InventoryCheckDetailLogs })
    logs?: InventoryCheckDetailLogs[];

    /**
     * @description 盘点范围
     */
    stockCheckScope?: StockCheckScope;

    checkOrderDetailWithBatchesToInventoryCheckDetail(): InventoryCheckDetail {
        const detail = JsonMapper.deserialize(InventoryCheckDetail, { ...this, list: [] });
        this.list?.forEach((listItem) => {
            const goodsInfo = listItem.goods;
            listItem.batchs?.forEach((batchItem) => {
                detail.list?.push(
                    JsonMapper.deserialize(CheckTaskGoodItem, {
                        ...batchItem,
                        goods: goodsInfo,
                    })
                );
            });
        });
        return detail;
    }
}

export class InventoryCheckDraft {
    id?: string;
    organId?: string;
    orderNo?: string;
    organ?: InventoryCheckDetailOrgan;
    kindCount?: number;
    createdUser?: GoodsCheckOrdersItemCreatedUser;
    @JsonProperty({ fromJson: fromJsonToDate })
    createdDate?: Date;
    @JsonProperty({ type: Array, clazz: InventoryCheckDetailComment })
    comment?: InventoryCheckDetailComment[];
    countChange?: number;
    costAmount?: number;
    saleAmount?: number;
    beforeCount?: number;
    afterCount?: number;
    beforeCostAmount?: number;
    afterCostAmount?: number;
    beforeSaleAmount?: number;
    afterSaleAmount?: number;
    beforeCostAmountExcludingTax?: number;
    afterCostAmountExcludingTax?: number;
    beforeSaleAmountExcludingTax?: number;
    afterSaleAmountExcludingTax?: number;
    totalCostPriceChange?: number;
    totalPriceChange?: number;
    createdUserId?: string;
    @JsonProperty({ type: Array, clazz: CheckTaskGoodItem })
    list?: CheckTaskGoodItem[];
    @JsonProperty({ type: Array, clazz: InventoryCheckDetailLogs })
    logs?: InventoryCheckDetailLogs[];

    @JsonProperty({ fromJson: fromJsonToDate })
    finishDate?: Date;
    @JsonProperty({ fromJson: fromJsonToDate })
    lastModifiedDate?: Date;
    @JsonProperty({ fromJson: fromJsonToDate })
    reviewDate?: Date;
    reviewUser?: GoodsCheckOrdersItemCreatedUser;
    status?: InventoryCheckDetailStatus;
    statusName?: string;
    @JsonProperty({ clazz: InventoryPharmacyInfo })
    pharmacy?: InventoryPharmacyInfo;
    __draftId?: string;
    //终端自定义字段，用于单人盘点中盘点范围的记录
    __searchParams?: {
        //二级分类需求
        typeId?: string[];
        customTypeId?: string[];
    };
    stockCheckScope?: StockCheckScope;

    toInventoryCheckListRows(): GoodsCheckOrdersItem {
        const checkListRow = JsonMapper.deserialize(GoodsCheckOrdersItem, {
            status: this.status ?? InventoryCheckDetailStatus.onlineDraft,
            lastModifiedDate: this.lastModifiedDate,
            statusName: this.statusName,
            createdDate: this.createdDate,
            createdUser: this.createdUser,
            organ: JsonMapper.deserialize(GoodsCheckOrdersItemOrgan, this.organ),
            __draftId: this.__draftId,
            __searchParams: this.__searchParams,
            pharmacy: this.pharmacy,
        });
        return checkListRow;
    }
}

export class InventoryCheckDraftDetail {
    beforeCostAmount?: number; //盘点前总成本(数量*成本价)
    afterCostAmount?: number; //盘点后总成本(数量*成本价)
    beforeSaleAmount?: number; //盘点前可以买的总价钱(数量*售价)
    afterSaleAmount?: number; //盘点后可以买的总价钱（数量*售价）
    beforeCostAmountExcludingTax?: number; //盘点前总成本(数量*成本价)(除开进销税)
    afterCostAmountExcludingTax?: number; //盘点后总成本(数量*成本价)(除开进销税)
    beforeSaleAmountExcludingTax?: number; //盘点前可以买的总价钱(数量*售价)(除开进销税)
    afterSaleAmountExcludingTax?: number; //盘点后可以买的总价钱(数量*售价)(除开进销税)
    beforeCount?: number; //盘点前总数量
    afterCount?: number;
    applyClinicId?: string;
    @JsonProperty({ type: Array, clazz: CheckTaskGoodItem })
    list?: CheckTaskGoodItem[];
    @JsonProperty({ type: Array, clazz: InventoryCheckDetailComment })
    comment?: InventoryCheckDetailComment[];
    @JsonProperty({ fromJson: fromJsonToDate })
    createdDate?: Date;
    createdUser?: InventoryCheckDetailCreatedUser;
    id?: string;
    kindCount?: number; //盘点单盘点药品的总数量
    @JsonProperty({ type: Array, clazz: InventoryCheckDetailLogs })
    logs?: InventoryCheckDetailLogs[];
    organId?: string;
    orderNo?: string;
    @JsonProperty({ type: InventoryPharmacyInfo })
    pharmacy?: InventoryPharmacyInfo;
    status?: InventoryCheckDetailStatus;
    statusName?: string;
    stockCheckScope?: StockCheckScope;
    totalCostPriceChange?: number;
    totalPriceChange?: number;
    organ?: InventoryCheckDetailOrgan;
    @JsonProperty({ fromJson: fromJsonToDate })
    lastModifiedDate?: Date;

    _localDraftDetail?: InventoryCheckDraft;
    __searchParams?: {
        //二级分类需求
        typeId?: string[];
        customTypeId?: string[];
    };

    get localDraftDetail(): InventoryCheckDraft {
        if (!this._localDraftDetail) {
            this.toTranLocalDraft();
        }
        //@ts-ignore
        return this._localDraftDetail;
    }

    set localDraftDetail(data: InventoryCheckDraft) {
        this._localDraftDetail = data;
    }

    toInventoryCheckListRows(): GoodsCheckOrdersItem {
        const result = this.localDraftDetail.toInventoryCheckListRows();
        result.id = this.id;
        return result;
    }

    /**
     * 云草稿转换为本地草稿
     */
    toTranLocalDraft(): void {
        this.localDraftDetail = JsonMapper.deserialize(InventoryCheckDraft, {
            status: InventoryCheckDetailStatus.onlineDraft,
            orderNo: this.orderNo,
            createdDate: this.createdDate,
            createdUser: this.createdUser,
            lastModifiedDate: this.lastModifiedDate,
            __draftId: this.id,
            __searchParams: this.__searchParams,
        });
    }
}
export class InventoryCheckDraftRsp {
    keyword?: string;
    limit?: number;
    offset?: number;
    total?: number;
    @JsonProperty({ type: Array, clazz: InventoryCheckDraftDetail })
    rows?: InventoryCheckDraftDetail[];
}

export class InventoryCheckReq {
    cMSpec?: string | string[];
    clearType?: string; //看代码的意思是把哪些类型的商品库存盘成0，后台已经在多人盘点的时候把这个逻辑关闭了
    comment?: string;
    customTypeIdList?: string[];
    isCheckScope?: number; //【多人盘点提交】【提交合并的时候的合并标记】【是否检查没在盘点范围内的药品】 0不检查范围 1 必须全部有
    @JsonProperty({ fromJson: fromJsonToDate })
    lastModifiedDate?: Date; //上次订单的修改时间，用于简单的判断订单是否改变
    @JsonProperty({ type: Array, clazz: PostStockCheckTaskReqItemsItem })
    list?: PostStockCheckTaskReqItemsItem[];
    orderClientUniqKey?: string; //可选参数 前端用于标识请求 主要用于解决盘点请求超过25s后，第二次继续点确定按钮 再次提交产生第二个盘点单的情况
    pharmacyNo?: number; //盘点的药房好默认为本地0号药房
    @JsonProperty({ type: StockCheckScope })
    stockCheckScope?: StockCheckScope;
    submitFlag?: number; //【多人盘点提交】【提交合并的时候的合并标记】【没在盘点范围内的药品如何处理】0 本次只是检查，不符合要求抛异常,1 没盘的盘成0,2 没盘的保持现有库存（也就是不盘
    typeIdList?: string[];
    type?: number | number[];
    subType?: number | number[]; //老协议兼容
    forceSubmit?: number;
}

export class InventoryCheckBatchItem {
    batchId?: string;
    batchNo?: string;
    dispGoodsCount?: string;
    dispLockingGoodsCount?: string;
    dispOutGoodsCount?: string;
    dispStockGoodsCount?: string;
    expiryDate?: string;
    lockingPackageCount?: number;
    lockingPieceCount?: number;
    outPackageCount?: number;
    outPieceCount?: number;
    packageCount?: number;
    pieceCount?: number;
    stockPackageCount?: number;
    stockPieceCount?: number;
}
