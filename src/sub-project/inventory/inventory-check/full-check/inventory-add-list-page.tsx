/**
 * 成都字节星球科技公司
 * <AUTHOR>
 * @date 2020-11-04
 *
 * @description
 */

import React from "react";
import { ABCNetworkPageContentStatus, BaseBlocNetworkPage } from "../../../base-ui/base-page";
import { ScrollView, Text, View } from "@hippy/react";
import colors from "../../../theme/colors";
import { Sizes, TextStyles } from "../../../theme";
import { InventorySubtaskDetailsPage } from "./inventory-subtask-details-page";
import { ABCNavigator } from "../../../base-ui/views/abc-navigator";
import { Spacer } from "../../../base-ui";
import { InventoryAddListPageBloc } from "./inventory-add-list-page-bloc";
import { ignore } from "../../../common-base-module/global";
import { ExecuteCountInventoryItemStatus, InventoryApiErrorCode, StockCheckTaskInfo } from "../../data/inventory-bean";
import { AbcListView } from "../../../base-ui/list/abc-list-view";
import { AbcView } from "../../../base-ui/views/abc-view";
import { ABCEmptyView } from "../../../base-ui/views/empty-view";
import { InventoryCheckAgent } from "../data/inventory-check-agent";
import { GoodsAgent } from "../../../data/goods/goods-agent";
import { GoodsType } from "../../../base-business/data/beans";
import { ABCApiError } from "../../../net";
import { showConfirmDialog } from "../../../base-ui/dialog/dialog-builder";

interface InventoryAddListPageProps {}

export class InventoryAddListPage extends BaseBlocNetworkPage<InventoryAddListPageProps, InventoryAddListPageBloc> {
    constructor(props: InventoryAddListPageProps) {
        super(props);
        this.bloc = new InventoryAddListPageBloc();
    }

    public componentDidMount(): void {
        super.componentDidMount();
        this.bloc.state
            .subscribe((state) => {
                let status = ABCNetworkPageContentStatus.show_data;
                if (state.loading) {
                    if (state.tasks?.length) {
                    } else status = ABCNetworkPageContentStatus.loading;
                } else if (state.loadError) {
                    status = ABCNetworkPageContentStatus.error;
                } else if (!state.tasks || state.tasks?.length == 0) {
                    status = ABCNetworkPageContentStatus.empty;
                }
                this.setContentStatus(status, state.loadError);
            })
            .addToDisposableBag(this);
    }

    getAppBarTitle(): string {
        return "盘点任务";
    }

    emptyContent(): JSX.Element {
        return <ABCEmptyView tips={"没有找到盘点任务"} />;
    }

    // 重新加载数据
    reloadData(): void {
        this.bloc.requestReloadData();
    }

    renderContent(): JSX.Element {
        const { tasks, loading } = this.bloc.currentState;
        return (
            <View style={{ flex: 1 }}>
                <AbcListView
                    loading={loading}
                    style={{ flex: 1 }}
                    dataSource={tasks ?? []}
                    numberOfRows={tasks?.length ?? 0}
                    getRowKey={this._getRowKey.bind(this)}
                    renderRow={this._renderRow.bind(this)}
                    scrollEventThrottle={300}
                    onRefresh={() => {
                        this.bloc.requestReloadData();
                    }}
                />
            </View>
        );
    }

    private _getRowKey(index: number): string {
        const { tasks } = this.bloc.currentState;
        return tasks![index].taskId!;
    }

    private _renderRow(
        data: StockCheckTaskInfo,
        unknown?: any, // FIXME: What's the argument meaning?
        index?: number
    ): JSX.Element {
        ignore(unknown);

        let text = "",
            textStyle = {};
        switch (data.status) {
            case ExecuteCountInventoryItemStatus.pendingSales: {
                text = "盘点中";
                textStyle = TextStyles.t16NG2;
                break;
            }
            case ExecuteCountInventoryItemStatus.taskCompleted: {
                text = "已完成";
                textStyle = TextStyles.t16MT4;
                break;
            }
            case ExecuteCountInventoryItemStatus.notStarted: {
                text = "未盘点";
                textStyle = TextStyles.t16MY2;
                break;
            }
        }

        const checkType = GoodsType.GoodsTypeAndSubType2TypeId({
            type: data.stockCheckScope?.type,
            subType: data.stockCheckScope?.subType,
            cMSpec: data.stockCheckScope?.cMSpec,
        });

        const typeId = data.stockCheckScope?.typeIdList ?? [];
        if (!!checkType && !data.stockCheckScope?.customTypeIdList?.length && !typeId.some((i) => i == checkType?.toString())) {
            typeId.push(checkType.toString());
        }
        const checkRangeDisplay =
            GoodsAgent.transGoodsClassifyName({
                typeId: typeId,
                customTypeId: data.stockCheckScope?.customTypeIdList,
            }) ?? "全部类型";

        return (
            <AbcView
                key={index}
                style={{ marginBottom: Sizes.dp4, backgroundColor: colors.white }}
                onClick={() => {
                    ABCNavigator.navigateToPage(
                        <InventorySubtaskDetailsPage
                            checkScopeDisplay={checkRangeDisplay}
                            checkTask={data}
                            isOnlineDraft={data.status != ExecuteCountInventoryItemStatus.notStarted}
                            saveDraft={(options) => {
                                return InventoryCheckAgent.postCheckCoworkJobDraftByTaskId(options)
                                    .then(() => this.reloadData())
                                    .catch((error) => {
                                        if (
                                            error instanceof ABCApiError &&
                                            error?.detail?.error?.code == InventoryApiErrorCode.goodsStockSummaryErrorCode
                                        ) {
                                            //处理12100异常code
                                            const { detail } = error?.detail?.error ?? {};
                                            showConfirmDialog(
                                                `提交失败：${detail?.errorTitle ?? ""}`,
                                                <ScrollView>
                                                    <View>
                                                        {detail.errorList
                                                            .map(
                                                                (it: { name: string; msgs: string[] }) => `${it.name}:${it.msgs.join("，")}`
                                                            )
                                                            .join("\n")}
                                                    </View>
                                                </ScrollView>
                                            );
                                        }
                                    });
                            }}
                        />
                    ).then();
                }}
            >
                <View
                    style={{
                        marginVertical: Sizes.dp12,
                        marginHorizontal: Sizes.dp16,
                    }}
                >
                    <View style={{ flex: 1, alignItems: "center", flexDirection: "row" }}>
                        <Text style={[TextStyles.t16MT1, { flexShrink: 1, lineHeight: Sizes.dp24 }]} numberOfLines={1}>
                            {data.taskName!}
                        </Text>
                        <Spacer />
                        <Text style={[textStyle, { lineHeight: Sizes.dp24 }]}>{text}</Text>
                    </View>
                    <Text style={[TextStyles.t14NT2, { flexShrink: 1, marginTop: Sizes.dp4, lineHeight: Sizes.dp20 }]} numberOfLines={1}>
                        {`总任务范围：${checkRangeDisplay}`}
                    </Text>
                    <Text style={[TextStyles.t14NT2, { flexShrink: 1, marginTop: Sizes.dp4, lineHeight: Sizes.dp20 }]} numberOfLines={1}>
                        {`${data.parentTaskName}`}
                    </Text>
                </View>
            </AbcView>
        );
    }
}
