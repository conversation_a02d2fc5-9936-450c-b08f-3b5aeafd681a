/**
 * 成都字节星球科技公司
 * <AUTHOR>
 * @date 2020-11-04
 *
 * @description
 */

import { Bloc, BlocEvent } from "../../../bloc";
import React from "react";
import { BaseLoadingState } from "../../../bloc/bloc-helper";
import { Subject } from "rxjs";
import { EventName } from "../../../bloc/bloc";
import { InventorySearchMedicinePage } from "./inventory-search-medicine-page";
import { InventoryTaskPopupDialog } from "./inventory-task-popup-dialog";
import {
    CheckTaskGoodItem,
    CoCheckTaskData,
    ExecuteCountInventoryItemStatus,
    InventoryApiErrorCode,
    InventorySubTaskDetailsPageType,
    PostStockCheckTaskReq,
    SearchParams,
    StockCheckTaskInfo,
    SummaryTaskGoodItem,
} from "../../data/inventory-bean";
import { InventoryAgent } from "../../data/inventory-agent";
import { checkTaskDraftManager } from "../../data/inventory-check-task-draft";
import { switchMap } from "rxjs/operators";
import { ABCError } from "../../../common-base-module/common-error";
import { GoodsInfo } from "../../../base-business/data/beans";
import { BarcodeScanner } from "../../../base-ui/camera/barcode-scanner";
import { GoodsAgent, SearchGoodInfoWithBatchItem } from "../../../data/goods/goods-agent";
import { Toast } from "../../../base-ui/dialog/toast";
import { LoadingDialog } from "../../../base-ui/dialog/loading-dialog";
import { errorSummary, errorToStr } from "../../../common-base-module/utils";
import { DialogIndex, showConfirmDialog, showQueryDialog } from "../../../base-ui/dialog/dialog-builder";
import { ABCApiError } from "../../../net";
import { JsonMapper } from "../../../common-base-module/json-mapper/json-mapper";
import { AnyType } from "../../../common-base-module/common-types";
import {
    InventorySubTaskNotCheckDialog,
    InventorySubTaskNotCheckDialogAction,
    InventorySubTaskSummaryWaringDialog,
} from "./inventory-subtask-not-check-dialog";
import _, { isNil } from "lodash";
import { AbcSet } from "../../../base-ui/utils/abc-set";
import { ABCNavigator } from "../../../base-ui/views/abc-navigator";
import { URLProtocols } from "../../../url-dispatcher";
import { InventoryCheckAgent } from "../data/inventory-check-agent";
import { userCenter } from "../../../user-center";
import { InventoryUtils } from "../../utils/inventory-utils";
import { ClinicAgent, EmployeesMeConfig } from "../../../base-business/data/clinic-agent";
import { ScrollView, View } from "@hippy/react";
import { InventoryCheckReq } from "../data/inventory-check-bean";
import { showBottomPanel } from "../../../base-ui/abc-app-library/panel";
import { InventoryCheckMultipleBatchDialog } from "../views/inventory-check-multiple-batch-dialog";
import { InventoryMedicineSearchPage } from "../../Inventory-medicine-search-page";

export class State extends BaseLoadingState {
    type: InventorySubTaskDetailsPageType = InventorySubTaskDetailsPageType.normal;

    detailData?: CoCheckTaskData;

    submitLoading?: boolean = false;

    hasMore = true;

    hasChange = false;

    scrollKeyIndex = 0;

    isLoadingAllData = false;

    stockCheckChainReview?: boolean;
    employeesMeConfig?: EmployeesMeConfig;
    //能否查看盘点药品价格
    get canViewPrice(): boolean {
        return !!this.employeesMeConfig?.employeeDataPermission?.inventory?.isCanSeeCheckGoodsPrice;
    }

    clone(): State {
        return Object.assign(new State(), this);
    }

    get canEdit(): boolean {
        return (
            this.detailData?.taskInfo?.status === ExecuteCountInventoryItemStatus.notStarted ||
            this.detailData?.taskInfo?.status == ExecuteCountInventoryItemStatus.pendingSales ||
            this.detailData?.taskInfo?.status == ExecuteCountInventoryItemStatus.taskCompleted
        );
    }
}

export class ScrollSameViewState extends State {
    static fromState(state: State): ScrollSameViewState {
        const newState = new ScrollSameViewState();
        Object.assign(newState, state);
        return newState;
    }
}

// 库存子任务详细信息页面块
export class InventorySubtaskDetailsPageBloc extends Bloc<_Event, State> {
    static Context = React.createContext<InventorySubtaskDetailsPageBloc | undefined>(undefined);
    disable = false;
    private _draftDetail?: CoCheckTaskData;
    private _isOnlineDraft?: boolean;
    private pharmacyNo?: number;

    static fromContext(context: InventorySubtaskDetailsPageBloc): InventorySubtaskDetailsPageBloc {
        return context;
    }

    constructor(checkTask: StockCheckTaskInfo, type: InventorySubTaskDetailsPageType, isOnlineDraft?: boolean, pharmacyNo?: number) {
        super();

        this._checkTask = checkTask;
        this.innerState.type = type;
        this.disable = type == InventorySubTaskDetailsPageType.onlyRead || type == InventorySubTaskDetailsPageType.summary;
        // 避免当前没有药房号，导致后续查询药品及批次信息有问题
        this.pharmacyNo = pharmacyNo ?? 0;

        //解决 线上草稿读取到了无法进行提交问题
        if (isOnlineDraft) {
            this.innerState.hasChange = true;
        }
        this._isOnlineDraft = isOnlineDraft;

        this.dispatch(new _EventInit()).then();
    }

    private _checkTask: StockCheckTaskInfo;
    private _loadDataTrigger: Subject<boolean> = new Subject<boolean>();
    private _fromDraft = false;
    private _limit = 30;
    private _refreshLimit = 500;
    private _someTaskList: AbcSet<CheckTaskGoodItem> = new AbcSet<CheckTaskGoodItem>();

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventAddGood, this._mapEventAddGoods);
        map.set(_EventDeleteGoods, this._mapEventDeleteGoods);
        map.set(_EventChangeMedicineTask, this._mapEventChangeMedicineTask);
        map.set(_EventSubmit, this._mapEventSubmit);
        map.set(_EventContinueSubmit, this._mapEventContinueSubmit);
        map.set(_EventLoadMore, this._mapEventLoadMore);
        map.set(_EventBackPage, this._mapEventBackPage);
        map.set(_EventSummary, this._mapEventSummary);
        map.set(_EventContinueSummary, this._mapEventContinueSummary);

        return map;
    }

    public update(state?: State): void {
        this.dispatch(new _EventUpdate(state)).then();
    }

    /**
     * 刷新本地数据的列表
     * 最好是分页刷新
     */
    private async refreshDraftGoodInfo() {
        const list = _.cloneDeep(this.innerState.detailData?.list);
        const listMap: Map<string, SearchGoodInfoWithBatchItem> = new Map();
        list?.forEach((item) => {
            const listMapItem = listMap.get(item.goodsId ?? item.goods?.id ?? "");
            if (listMapItem) {
                listMapItem.batchs?.push(item.batchId!);
                listMap.set(item.goodsId!, listMapItem);
            } else {
                const newMapItem = JsonMapper.deserialize(SearchGoodInfoWithBatchItem, {
                    goodsId: item.goodsId ?? item.goods!.id!,
                    batchs: [],
                });
                if (item.batchId) {
                    newMapItem.batchs?.push(item.batchId);
                }
                listMap.set(item.goodsId ?? item.goods!.id!, newMapItem);
            }
        });

        const params = [...listMap.values()];
        for (let i = 0; i < params.length; i = i + this._refreshLimit) {
            const result = await GoodsAgent.searchGoodInfoWithBatch({
                list: params.slice(i, i + this._refreshLimit),
                pharmacyNo: 0,
                pharmacyType: 0,
            });
            result?.list?.map((item: Partial<CheckTaskGoodItem> | undefined) => {
                const sameItem = this.innerState.detailData?.list?.find((listItem) => {
                    if (item?.batchId) {
                        return listItem.compareKey() == `${item?.goodsId}${item?.batchId}`;
                    } else {
                        return listItem.goods?.id == `${item?.goodsId}`;
                    }
                });
                if (sameItem) {
                    sameItem.beforePackageCount = item?.packageCount;
                    sameItem.beforePieceCount = item?.pieceCount;
                }
            });
        }
    }

    /**
     * 拉去任务全部药品信息
     */
    private async loadAllGoodsInfo() {
        this._someTaskList = new AbcSet<CheckTaskGoodItem>();
        return InventoryAgent.getStockCheckTaskJob({
            taskId: this._checkTask.taskId!,
            parentTaskId: this._checkTask.parentTaskId!,
        }).then((rsp) => {
            this.innerState.detailData?.list?.forEach((item) => this._someTaskList.add(item));
            rsp.list?.forEach((item) => {
                if (!this._someTaskList.has(item)) this.innerState.detailData?.list?.push(item);
            });
        });
    }

    private async _initPageConfig(): Promise<void> {
        let draftDetail = undefined;
        if (this.innerState.type == InventorySubTaskDetailsPageType.normal) {
            draftDetail = await checkTaskDraftManager.loadDraftByTaskId(this._checkTask.taskId!).catchIgnore();
            this._draftDetail = _.cloneDeep(draftDetail);
        } else if (this.innerState.type == InventorySubTaskDetailsPageType.summary) {
            //拉取诊所配置
            const chainConfig = await ClinicAgent.getInventoryChainConfig().catchIgnore();
            this.innerState.stockCheckChainReview =
                !!chainConfig?.chainReview?.stockCheckChainReview && !userCenter.clinic?.isChainAdminClinic;
        }
        if ((this._checkTask.sig ?? undefined) == draftDetail?.sig) {
            this.innerState.detailData = draftDetail;
        } else if (draftDetail) {
            const result = await showQueryDialog("", "任务单已被修改，是否同步信息(同步后将删除草稿)", "使用手机草稿", "同步信息");
            if (result == DialogIndex.positive) {
                this.innerState.detailData = draftDetail;
            } else if (result == DialogIndex.negative) {
                checkTaskDraftManager.removeDraft(this._checkTask.taskId!);
            }
        }
        this.innerState.employeesMeConfig = await ClinicAgent.getEmployeesMeConfig().catchIgnore();
    }
    private _initTrigger(): void {
        this._loadDataTrigger
            .pipe(
                switchMap(() => {
                    this.innerState.loading = true;
                    this.innerState.loadError = undefined;
                    this.update();

                    const params: { taskId: string; parentTaskId: string; offset?: number; limit?: number } = {
                        taskId: this._checkTask.taskId!,
                        parentTaskId: this._checkTask.parentTaskId!,
                        offset: 0,
                        limit: this._limit,
                    };

                    return InventoryAgent.getStockCheckTaskJob(params)
                        .catch((error) => new ABCError(error))
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                this.innerState.loading = false;
                if (rsp instanceof ABCError) {
                    if (rsp.detailError?.detail?.error?.code == 12236) {
                        const detail = rsp.detailError?.detail?.error?.detail?.map((item: any) => {
                            const _item = JsonMapper.deserialize(GoodsInfo, item);
                            return JsonMapper.deserialize(SummaryTaskGoodItem, {
                                ...item,
                                goods: _item,
                            });
                        });
                        InventorySubTaskSummaryWaringDialog.show({
                            title: rsp.detailError?.detail?.error.message,
                            detail: detail,
                            readonly: true,
                        }).finally(() => {
                            ABCNavigator.pop();
                        });
                    } else {
                        this.innerState.loadError = rsp;
                        this.update();
                    }

                    return;
                }
                this.innerState.detailData = rsp;
                if (!this.innerState.detailData?.list) this.innerState.detailData!.list = [];
                this.innerState.detailData!.taskInfo = this._checkTask;
                if (this._isOnlineDraft) {
                    this._draftDetail = _.cloneDeep(this.innerState.detailData);
                }
                this.update();

                if ((rsp?.list?.length ?? 0) < this._limit) {
                    this.innerState.hasMore = false;
                } else {
                    this.innerState.hasMore = false;
                    this.innerState.isLoadingAllData = true; // 标记正在加载全部数据
                    this.innerState.loading = true; // 确保connectLoadingStatus能显示loading状态
                    this.update();

                    this.loadAllGoodsInfo()
                        .then(() => {
                            // loadAllGoodsInfo完成
                        })
                        .catch((error) => {
                            const errorMsg = new ABCError(error);
                            if (errorMsg?.detailError?.detail?.error?.code == 12236) {
                                const detail = errorMsg.detailError?.detail?.error?.detail?.map((item: any) => {
                                    const _item = JsonMapper.deserialize(GoodsInfo, item);
                                    return JsonMapper.deserialize(SummaryTaskGoodItem, {
                                        ...item,
                                        goods: _item,
                                    });
                                });
                                InventorySubTaskSummaryWaringDialog.show({
                                    title: errorMsg.detailError?.detail?.error.message,
                                    detail: detail,
                                    readonly: true,
                                }).finally(() => {
                                    ABCNavigator.pop();
                                });
                            } else {
                                return showConfirmDialog("", "拉取药品信息错误").then(() => {
                                    this.innerState.loadError = "拉取药品信息错误";
                                    this.update();
                                });
                            }
                        })
                        .finally(() => {
                            this.innerState.isLoadingAllData = false; // 标记全部数据加载完成
                            this.innerState.loading = false; // 重置loading状态
                            // 如果是线上草稿模式，更新draftDetail
                            if (this._isOnlineDraft && this.innerState.detailData) {
                                this._draftDetail = _.cloneDeep(this.innerState.detailData);
                            }
                            this.update();
                        });
                }
            })
            .addToDisposableBag(this);
    }
    // 避免当前没有药房号，导致后续查询药品及批次信息有问题
    _resetPharmacyNo(): void {
        this.innerState.detailData?.list?.forEach((item) => {
            item.pharmacyNo =
                (!isNil(item.pharmacyNo) ? item.pharmacyNo : this.innerState.detailData?.taskInfo?.pharmacy?.no) ?? this.pharmacyNo;
        });
    }
    private async *_mapEventInit(/*event: _EventInit*/): AsyncGenerator<State> {
        await this._initPageConfig();
        this._initTrigger();
        if (this.innerState.detailData && !this.disable) {
            this._fromDraft = true;
            this.innerState.hasChange = true; //【ID1023383】【app库存】进入盘点任务，不修改任何内容返回，不应该有修改内容未提交的提示 注释
            this.refreshDraftGoodInfo()
                .toObservable()
                .subscribe(() => {
                    checkTaskDraftManager.saveDraft(this._checkTask.taskId!, this.innerState.detailData!).then();
                    this.update();
                })
                .addToDisposableBag(this);
        } else {
            this._loadDataTrigger.next();
        }
        this._resetPharmacyNo();
        yield this.innerState.clone();
    }

    private async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
        if (event.state) {
            yield event.state;
            return;
        }
        yield this.innerState.clone();
    }

    private async *_mapEventAddGoods(event: _EventAddGood): AsyncGenerator<State> {
        let goodsInfo: GoodsInfo | undefined;
        if (event.qrScan) {
            const params: SearchParams = {
                types: !_.isNil(this._checkTask.stockCheckScope?.type) ? [this._checkTask.stockCheckScope!.type] : undefined,
                subTypes: !_.isNil(this._checkTask.stockCheckScope?.subType) ? [this._checkTask.stockCheckScope!.subType] : undefined,
                cMSpec: !_.isNil(this._checkTask.stockCheckScope?.cMSpec) ? [this._checkTask.stockCheckScope!.cMSpec] : undefined,
                typeId: this._checkTask.stockCheckScope?.typeIdList,
                customTypeId: this._checkTask.stockCheckScope?.customTypeIdList,
                pharmacyNo: this.pharmacyNo,
            };
            goodsInfo = await InventoryUtils.BarScan2GoodsInfo({ ...params, withStock: "1", onlyStock: "1", disable: 0 }, true, {
                title: "扫描条形码/二维码",
            });
        } else {
            // 搜索药品/物资页面
            // goodsInfo = await InventorySearchMedicinePage.show(this._checkTask.stockCheckScope!, this.pharmacyNo); // 搜索药品/物资页面
            const params: SearchParams = {
                withStock: "1",
                onlyStock: "1",
                types: !_.isNil(this._checkTask.stockCheckScope?.type) ? [this._checkTask.stockCheckScope!.type] : undefined,
                subTypes: !_.isNil(this._checkTask.stockCheckScope?.subType) ? [this._checkTask.stockCheckScope!.subType] : undefined,
                cMSpec: !_.isNil(this._checkTask.stockCheckScope?.cMSpec) ? [this._checkTask.stockCheckScope!.cMSpec] : undefined,
                disable: 0,
                typeId: this._checkTask.stockCheckScope?.typeIdList,
                customTypeId: this._checkTask.stockCheckScope?.customTypeIdList,
                pharmacyNo: this.pharmacyNo,
            };
            goodsInfo = await ABCNavigator.navigateToPage<GoodsInfo | undefined>(
                <InventoryMedicineSearchPage searchParams={params} callback={(goodsInfo) => ABCNavigator.pop(goodsInfo)} />
            );
        }

        if (!goodsInfo) return;

        //判断goods是否在当前盘点范围内
        if (this.innerState.detailData?.taskInfo?.stockCheckScope) {
            const { type, subType, cMSpec } = this.innerState.detailData?.taskInfo?.stockCheckScope;
            if (
                (!_.isNil(type) && goodsInfo.type != type) ||
                (!_.isNil(subType) && goodsInfo.subType != subType) ||
                (!_.isNil(cMSpec) && cMSpec != "" && goodsInfo.cMSpec != cMSpec)
            ) {
                return showConfirmDialog("添加失败", "你扫码添加的药品不在本次盘点范围内");
            }
        }

        /**
         * @description 处于调拨中的药品不允许盘点
         */
        const lockList = await InventoryCheckAgent.lockMedicineCheck(goodsInfo.id!).catchIgnore();
        if (lockList?.rows.length) {
            await showConfirmDialog(`${goodsInfo.displayName}存在未确认的调拨单，需要调入方确认后可进行盘点`, "");
            return;
        }

        let index = 0;
        for (const item of this.innerState.detailData?.list ?? []) {
            //判断药品是否相同
            if (item.goods?.id == goodsInfo.id) {
                if (item.batchId == undefined) {
                    this.innerState.scrollKeyIndex = index;
                    yield ScrollSameViewState.fromState(this.innerState);
                    this.dispatch(new _EventChangeMedicineTask(item));
                    return;
                }
            }
            index++;
        }
        const isDrugstoreButler = userCenter.clinic?.isDrugstoreButler;

        const newItem = new CheckTaskGoodItem();
        newItem.goods = goodsInfo;
        newItem.beforePieceCount = !isDrugstoreButler ? goodsInfo.pieceCount : undefined;
        newItem.beforePackageCount = !isDrugstoreButler ? goodsInfo.packageCount : undefined;
        newItem.pieceNum = goodsInfo.pieceNum;
        newItem.goodsId = goodsInfo.id;

        let status = true;
        let editResult = undefined,
            drugBatchList;
        this.innerState.detailData = this.innerState.detailData ?? new CoCheckTaskData();
        // 药店--需要将搜索药品现有的所有批次传入进去，否则如果当前已经选了这个药品的其他批次，再次添加时会导致这个批次数据丢失
        if (isDrugstoreButler) {
            const existCurrentGoods = this.innerState.detailData?.list?.filter((item) => item?.goods?.id == goodsInfo?.id);
            let drugSelectBatchList: CheckTaskGoodItem[] = []; // 当前已选药品批次
            if (existCurrentGoods?.length) {
                drugSelectBatchList = existCurrentGoods.map((item) => {
                    return JsonMapper.deserialize(CheckTaskGoodItem, {
                        ...item,
                        goods: goodsInfo,
                        beforePieceCount: item.beforePackageCount,
                        beforePackageCount: item.beforePieceCount,
                        pieceNum: goodsInfo?.pieceNum,
                        goodsId: goodsInfo?.id,
                        _draftBeforePieceCount: item?._draftBeforePackageCount,
                        _draftBeforePackageCount: item?._draftBeforePackageCount,
                    });
                });
            } else {
                drugSelectBatchList = [newItem];
            }
            drugBatchList = await showBottomPanel<CheckTaskGoodItem[]>(
                <InventoryCheckMultipleBatchDialog taskGoodItemList={drugSelectBatchList!} />
            );
            if (!drugBatchList?.length) return;
        } else {
            do {
                editResult = await InventoryTaskPopupDialog.show(newItem); // 库存任务弹出页面
                if (!editResult) return;
                index = 0;
                status = false;
                for (const item of this.innerState.detailData?.list ?? []) {
                    //判断药品是否相同
                    if (item.goods?.id == goodsInfo.id) {
                        if (editResult.batchId == undefined || item.compareKey() == editResult.compareKey()) {
                            status = true;
                            this.innerState.scrollKeyIndex = index;
                            yield ScrollSameViewState.fromState(this.innerState);
                            if (editResult.batchId == undefined) await Toast.show("当前商品未选择批次");
                            if (item.compareKey() == editResult.compareKey()) await Toast.show("当前商品批次已存在");
                            break;
                        }
                    }
                    index++;
                }
            } while (status);
        }
        if (userCenter.clinic?.isDrugstoreButler) {
            // 查找当前药品是否存在，存在需要删除已有的批次，再追加重新选择的
            this.innerState.detailData.list =
                this.innerState.detailData?.list?.filter((item) => (item?.goodsId ?? item.goods?.id) != goodsInfo?.id) ?? [];
            this.innerState.detailData.list = this.innerState.detailData.list?.concat(drugBatchList ?? []);
        } else {
            this.innerState.detailData!.list!.push(editResult!);
        }
        this.innerState.hasChange = true;
        this.innerState.submitLoading = true;
        checkTaskDraftManager
            .saveDraft(this._checkTask.taskId!, this.innerState.detailData!)
            .toObservable()
            .subscribe(() => {
                this.innerState.submitLoading = false;
                this.update();
            })
            .addToDisposableBag(this);
        this.innerState.scrollKeyIndex = (this.innerState.detailData?.list?.length ?? 1) - 1;
        yield ScrollSameViewState.fromState(this.innerState);
    }

    // 删除-药品物资
    private async *_mapEventDeleteGoods(event: _EventDeleteGoods): AsyncGenerator<State> {
        const goodsInfo = event.goodsInfo;
        //删除提示框
        const result = await showQueryDialog(
            "是否删除？",
            `${goodsInfo.goods?.displayName}${goodsInfo.batchId ? ` 批次：${goodsInfo.batchId}` : ""}`
        );
        if (result != DialogIndex.positive) return;
        const index = this.innerState.detailData!.list!.findIndex((item) => item?.compareKey() === goodsInfo.compareKey());
        this.innerState.detailData!.list!.splice(index, 1);
        this.innerState.hasChange = true;
        this.innerState.submitLoading = true;
        checkTaskDraftManager
            .saveDraft(this._checkTask.taskId!, this.innerState.detailData!)
            .toObservable()
            .subscribe(() => {
                this.innerState.submitLoading = false;
                this.update();
            })
            .addToDisposableBag(this);
        this.update();
    }

    private async _searchWithQRScan(): Promise<GoodsInfo | undefined> {
        const result = await BarcodeScanner.scanWithDetailResult();
        if (result.action === "user_cancel" || result.error) {
            return undefined;
        }

        const loadingDialog = new LoadingDialog("正在搜索");
        loadingDialog.show(500);
        const list = await GoodsAgent.searchWithInStockByQRCode({
            key: result.barCode!,
            withStock: "1",
            onlyStock: "1",
            disable: 0,
        }).catch((error) => new ABCError(error));
        await loadingDialog.hide();
        if (list instanceof ABCError) {
            await Toast.show("搜索错误:" + errorSummary(list));
            return;
        }

        if (list.length == 0) {
            await Toast.show(`未找到该药品的入库记录，请先入库再进行操作`);
            return;
        }

        return list[0];
    }

    // 修改-子任务药品物资
    private async *_mapEventChangeMedicineTask(event: _EventChangeMedicineTask): AsyncGenerator<State> {
        if (userCenter.clinic?.isDrugstoreButler) {
            let drugBatchList: CheckTaskGoodItem[] = [];
            this.innerState.detailData = this.innerState.detailData ?? new CoCheckTaskData();
            // 查找出当前选中药品种类的所有批次
            const existCurrentGoods = this.innerState.detailData?.list?.filter(
                (item) => (item.goodsId ?? item.goods?.id) == (event.goodItem.goodsId ?? event.goodItem?.goods?.id)
            );
            let drugSelectBatchList: CheckTaskGoodItem[] = []; // 当前已选药品批次
            if (existCurrentGoods?.length) {
                drugSelectBatchList = existCurrentGoods.map((item) => {
                    return JsonMapper.deserialize(CheckTaskGoodItem, {
                        ...item,
                        beforePieceCount: item.beforePackageCount,
                        beforePackageCount: item.beforePieceCount,
                        pieceNum: event.goodItem.pieceNum,
                        goodsId: event.goodItem.id,
                    });
                });
            } else {
                drugSelectBatchList = [event.goodItem];
            }
            drugBatchList = await showBottomPanel<CheckTaskGoodItem[]>(
                <InventoryCheckMultipleBatchDialog
                    taskGoodItemList={drugSelectBatchList}
                    currentSelectBatchId={!!event.goodItem.batchId ? event.goodItem.batchId.toString() : undefined}
                />
            );
            if (!drugBatchList) return;
            // 查找当前药品是否存在，存在需要删除已有的批次，再追加重新选择的
            // 使用 filter 方法过滤掉要删除的元素
            this.innerState.detailData.list = this.innerState.detailData?.list?.filter(
                (item) => (item?.goodsId ?? item.goods?.id) != (event.goodItem.goodsId ?? event.goodItem?.goods?.id)
            );
            this.innerState.detailData.list = this.innerState.detailData.list?.concat(drugBatchList ?? []);
        } else {
            const index = this.innerState.detailData?.list?.findIndex((item) => item.compareKey() == event.goodItem.compareKey());
            const result = await InventoryTaskPopupDialog.show(event.goodItem);
            if (result) {
                if (index != undefined && index >= 0) {
                    result.packageCountChange = (result.packageCount ?? 0) - (result.beforePackageCount ?? 0);
                    result.pieceCountChange = (result.pieceCount ?? 0) - (result.beforePieceCount ?? 0);
                    (this.innerState.detailData?.list ?? [])[index] = result;
                }
            }
        }
        this.innerState.hasChange = true;
        this.innerState.submitLoading = true;
        this.update();
        checkTaskDraftManager
            .saveDraft(this._checkTask.taskId!, this.innerState.detailData!)
            .toObservable()
            .subscribe(() => {
                this.innerState.submitLoading = false;
                this.update();
            })
            .addToDisposableBag(this);
    }

    private async *_mapEventSubmit(/*event: _EventSubmit*/): AsyncGenerator<State> {
        //如果没有修改任何内容时，提交disable
        if (!this.innerState.hasChange) {
            Toast.show("无内容修改", { warning: true });
            return;
        }

        const result = await showQueryDialog("", "点击确认盘点任务将提交，等待创建人汇总完成盘点");
        if (result != DialogIndex.positive) return;

        this.innerState.hasChange = true;
        this.innerState.submitLoading = true;
        this.update();

        const dialog = new LoadingDialog();
        dialog.show(300);

        InventoryAgent.postStockCheckTask(CoCheckTaskData.CheckTaskGoodList2StockCheckTaskReq(this.innerState.detailData!))
            .toObservable()
            .subscribe(
                () => {
                    checkTaskDraftManager.removeDraft(this._checkTask.taskId!);
                    InventoryAgent.taskStatusPublisher.next();
                    dialog.hide().then(() => {
                        Toast.show("提交成功", { success: true }).then(() => ABCNavigator.pop("success"));
                    });
                    this.innerState.submitLoading = false;
                    this.update();
                },
                (error) => {
                    dialog.hide().then(() => {
                        if (error instanceof ABCApiError && error?.detail?.error?.code == "12238") {
                            const list: GoodsInfo[] = [];
                            error.detail.error.detail.forEach((item: AnyType) => {
                                list.push(JsonMapper.deserialize(GoodsInfo, { ...item, id: item.goodsId }));
                            });

                            this.dispatch(new _EventContinueSubmit(error?.detail?.error?.message, list));
                        } else if (error instanceof ABCApiError && error?.detail?.error?.code == "12232") {
                            showConfirmDialog("提交失败", error?.detail?.error?.message);
                        } else if (
                            error instanceof ABCApiError &&
                            error?.detail?.error?.code == InventoryApiErrorCode.goodsStockSummaryErrorCode
                        ) {
                            //处理12100异常code
                            const { detail } = error?.detail?.error ?? {};
                            showConfirmDialog(
                                `提交失败：${detail?.errorTitle ?? ""}`,
                                <ScrollView>
                                    <View>
                                        {detail.errorList
                                            .map((it: { name: string; msgs: string[] }) => `${it.name}:${it.msgs.join("，")}`)
                                            .join("\n")}
                                    </View>
                                </ScrollView>
                            );
                        } else {
                            showConfirmDialog("提交失败", error?.detail?.error?.message).then(() => {
                                InventoryAgent.subTaskErrorPublisher.next();
                            });
                        }
                        this.innerState.submitLoading = false;
                        this.update();
                    });
                }
            )
            .addToDisposableBag(this);
    }

    private async *_mapEventContinueSubmit(event: _EventContinueSubmit): AsyncGenerator<State> {
        const cloneDetail = _.cloneDeep(this.innerState.detailData);
        const notCheckList = event.goodsInfoList.map((item) => {
            return JsonMapper.deserialize(CheckTaskGoodItem, {
                ...item,
                goods: item,
                goodsId: item.id,
                beforePieceCount: item.pieceCount,
                beforePackageCount: item.packageCount,
            });
        });
        const result = await InventorySubTaskNotCheckDialog.show({
            title: event.title,
            detail: notCheckList,
        });
        if (!result) return;
        if (result.action == InventorySubTaskNotCheckDialogAction.keep) {
            notCheckList.map((item) => {
                item.pieceCount = item.beforePieceCount;
                item.packageCount = item.beforePackageCount;
            });
        } else if (result.action == InventorySubTaskNotCheckDialogAction.changZero) {
            notCheckList.map((item) => {
                item.pieceCount = 0;
                item.packageCount = 0;
            });
        }
        cloneDetail!.list = cloneDetail?.list?.concat(notCheckList);
        const dialog = new LoadingDialog();
        dialog.show(300);

        InventoryAgent.postStockCheckTask(CoCheckTaskData.CheckTaskGoodList2StockCheckTaskReq(cloneDetail!))
            .then(() => {
                checkTaskDraftManager.removeDraft(this._checkTask.taskId!);
                InventoryAgent.taskStatusPublisher.next();
                dialog.hide().then(() => {
                    Toast.show("提交成功", { success: true }).then(() => ABCNavigator.pop("success"));
                });
            })
            .catch((error) => {
                dialog.hide().then(() => {
                    if (error instanceof ABCApiError && error?.detail?.error?.code == InventoryApiErrorCode.goodsStockSummaryErrorCode) {
                        //处理12100异常code
                        const { detail } = error?.detail?.error ?? {};
                        showConfirmDialog(
                            `提交失败：${detail?.errorTitle ?? ""}`,
                            <ScrollView>
                                <View>
                                    {detail.errorList
                                        .map((it: { name: string; msgs: string[] }) => `${it.name}:${it.msgs.join("，")}`)
                                        .join("\n")}
                                </View>
                            </ScrollView>
                        );
                    } else {
                        Toast.show("提交失败", { warning: true }).then(() => {
                            InventoryAgent.subTaskErrorPublisher.next();
                        });
                    }
                });
            });
        this.update();
    }

    private async *_mapEventLoadMore(): AsyncGenerator<State> {
        if (this._fromDraft || !this.innerState.hasMore) return;
        this._loadDataTrigger.next();
    }

    private async *_mapEventBackPage(event: _EventBackPage): AsyncGenerator<State> {
        if (this.innerState.hasChange) {
            if (this.innerState.submitLoading) return;
            if (this._draftDetail && _.isEqual(this.innerState.detailData, this._draftDetail)) return ABCNavigator.pop();
            let result;
            if (!this._isOnlineDraft) {
                result = await showQueryDialog("是否保存", "是否需要保存成草稿");
            } else {
                result = await showQueryDialog("是否保存草稿", "草稿信息发生变动，是否保存？", "保存", "不保存");
            }
            if (result == DialogIndex.positive) {
                // 如果当前任务已经完成，此时不能保存成草稿了，只能选择提交
                if (this._checkTask.status == ExecuteCountInventoryItemStatus.taskCompleted) {
                    await showConfirmDialog("提示", "你的盘点任务已经提交完成", "我知道了");
                    return;
                }

                // 如果当前没有商品!this.innerState.detailData?.list?.length，不能保存成草稿
                if (!this.innerState.detailData?.list?.length) {
                    await showConfirmDialog("提示", "盘点任务没盘点任何药品", "我知道了");
                    return;
                }

                if (event.saveDraft) {
                    //使用线上草稿 - 需要删除本地草稿
                    try {
                        await event.saveDraft(CoCheckTaskData.CheckTaskGoodList2StockCheckTaskReq(this.innerState.detailData!)).then(() => {
                            checkTaskDraftManager.removeDraft(this.innerState.detailData!.taskInfo!.taskId!);
                        });
                    } catch (e) {
                        showConfirmDialog("保存失败", errorToStr(e));
                        return;
                    }
                }

                // 这个逻辑暂时不要了
                // if (this._checkTask.status == ExecuteCountInventoryItemStatus.taskCompleted) {
                //     //本人已完成的任务保存草稿下次进入提示使用本地草稿还是线上草稿
                //     this.innerState.detailData!.sig = "local";
                //     checkTaskDraftManager.saveDraft(this._checkTask.taskId!, this.innerState.detailData!);
                // }
                InventoryAgent.taskStatusPublisher.next();
                ABCNavigator.pop();
            } else {
                if (!this._isOnlineDraft) {
                    await checkTaskDraftManager.removeDraft(this._checkTask.taskId!);
                } else {
                    // 线上草稿取消保存时，需要恢复到原始的线上草稿数据
                    if (event.saveDraft && this._draftDetail) {
                        try {
                            // 使用原始的线上草稿数据，而不是当前编辑的数据
                            await event.saveDraft(CoCheckTaskData.CheckTaskGoodList2StockCheckTaskReq(this._draftDetail)).then(() => {
                                checkTaskDraftManager.removeDraft(this._checkTask.taskId!);
                            });
                        } catch (e) {
                            showConfirmDialog("保存失败", errorToStr(e));
                            return;
                        }
                    }
                }
                InventoryAgent.taskStatusPublisher.next();
                ABCNavigator.pop();
            }
        } else {
            InventoryAgent.taskStatusPublisher.next();
            ABCNavigator.pop();
        }
    }

    private async *_mapEventSummary(): AsyncGenerator<State> {
        if (this.innerState.stockCheckChainReview) {
            const result = await showQueryDialog("", "确定后将发给总部审核，审核通过后将立即更新库存");
            if (result != DialogIndex.positive) return;
        }
        const dialog = new LoadingDialog();
        dialog.show(300);

        InventoryAgent.postStockCheckTaskJobSubmit({
            parentTaskId: this.innerState.detailData!.taskInfo!.parentTaskId!,
            sig: this.innerState.detailData?.sig,
            submitSpecFlag: 1,
        })
            .then(() => {
                dialog.hide().then(() => {
                    InventoryAgent.taskStatusPublisher.next();
                    Toast.show("汇总成功", { success: true }).then(() => ABCNavigator.popUntil(URLProtocols.INVENTORY_CHECK_LIST));
                });
            })
            .catch((error) => {
                dialog.hide().then(() => {
                    if (error instanceof ABCApiError && error?.detail?.error?.code == "12238") {
                        const list: GoodsInfo[] = [];
                        error.detail.error.detail.forEach((item: AnyType) => {
                            list.push(
                                JsonMapper.deserialize(GoodsInfo, {
                                    ...item,
                                    id: item.goodsId,
                                    goodsBatchInfoList: item.batches,
                                    stockPackageCount: item.packageCount,
                                    stockPieceCount: item.pieceCount,
                                    manufacturer: item?.goods?.manufacturer,
                                })
                            );
                        });

                        this.dispatch(new _EventContinueSummary(error?.detail?.error?.message, list));
                    } else if (
                        error instanceof ABCApiError &&
                        error?.detail?.error?.code == InventoryApiErrorCode.goodsStockSummaryErrorCode
                    ) {
                        //处理12100异常code
                        const { detail } = error?.detail?.error ?? {};
                        showConfirmDialog(
                            `提交失败：${detail?.errorTitle ?? ""}`,
                            <ScrollView>
                                <View>
                                    {detail.errorList
                                        .map((it: { name: string; msgs: string[] }) => `${it.name}:${it.msgs.join("，")}`)
                                        .join("\n")}
                                </View>
                            </ScrollView>
                        );
                    } else {
                        showConfirmDialog("提交失败", error?.detail?.error?.message).then(() => {
                            InventoryAgent.subTaskErrorPublisher.next();
                        });
                    }
                });
            });
    }

    private async handleNotCheckGoods(notCheckList: CheckTaskGoodItem[]): Promise<void> {
        const innerState = this.innerState;
        const cloneData = _.cloneDeep(innerState.detailData!);
        cloneData.list = notCheckList;
        cloneData.comment = `${new Date().format("yyyy-MM-dd")} 全量盘点中未盘点的商品`;
        const switchReqData = CoCheckTaskData.CheckTaskGoodList2StockCheckTaskReq(cloneData);
        const req = JsonMapper.deserialize(InventoryCheckReq, {
            ...switchReqData,
            lastModifiedDate: new Date(),
            pharmacyNo: this.pharmacyNo,
            comment: cloneData.comment,
        });
        InventoryCheckAgent.createCheckOrderDraft(req)
            .then(() => {
                ABCNavigator.pop();
            })
            .catch((e) => {
                showConfirmDialog("保存失败", errorSummary(e?.message));
            });
    }

    /**
     * 选中药品与未盘点药品处理
     * @param type
     * @param notCheckList
     * @private
     */
    private handleSelectAndNotCheckGoods(type: number, notCheckList: CheckTaskGoodItem[]): PostStockCheckTaskReq {
        const cloneData = _.cloneDeep(this.innerState.detailData!);
        if (type == InventorySubTaskNotCheckDialogAction.changZero) {
            notCheckList = notCheckList?.map((item) => {
                item.packageCount = 0;
                item.pieceCount = 0;
                return item;
            });
        }
        cloneData.list = notCheckList;
        return CoCheckTaskData.CheckTaskGoodList2StockCheckTaskReq(cloneData!);
    }

    private async *_mapEventContinueSummary(event: _EventContinueSummary): AsyncGenerator<State> {
        const notCheckList: CheckTaskGoodItem[] = [];
        event.goodsInfoList?.map((item) => {
            // 药店管家商品必须指定批次，所以需要将批次信息打平放出来
            if (userCenter.clinic?.isDrugstoreButler && !!item?.goodsBatchInfoList?.length) {
                item.goodsBatchInfoList?.map((batchItem) => {
                    notCheckList.push(
                        JsonMapper.deserialize(CheckTaskGoodItem, {
                            ...item,
                            ...batchItem,
                            goods: item,
                            goodsId: item.id,
                            beforePieceCount: batchItem.pieceCount,
                            beforePackageCount: batchItem.packageCount,
                            pieceNum: item.pieceNum,
                        })
                    );
                });
            } else {
                notCheckList.push(
                    JsonMapper.deserialize(CheckTaskGoodItem, {
                        ...item,
                        goods: item,
                        goodsId: item.id,
                        beforePieceCount: item.pieceCount,
                        beforePackageCount: item.packageCount,
                    })
                );
            }
        });
        const result = await InventorySubTaskNotCheckDialog.show({
            title: event.title,
            detail: notCheckList,
            showKeep: true,
            canGenerateDraft: true,
        });
        if (_.isUndefined(result)) return;
        this.innerState.detailData = this.innerState.detailData ?? new CoCheckTaskData();
        let req = CoCheckTaskData.CheckTaskGoodList2StockCheckTaskReq(this.innerState.detailData!);
        if (result.action == InventorySubTaskNotCheckDialogAction.generatingDraft) {
            await this.handleNotCheckGoods(notCheckList);
        }
        if (result.action == InventorySubTaskNotCheckDialogAction.changZero || result.action == InventorySubTaskNotCheckDialogAction.keep) {
            req = this.handleSelectAndNotCheckGoods(result.action, notCheckList);
        }
        const dialog = new LoadingDialog();
        dialog.show(300);
        InventoryAgent.postStockCheckTaskJobSubmit({
            parentTaskId: this.innerState.detailData!.taskInfo!.parentTaskId!,
            sig: this.innerState.detailData?.sig,
            submitSpecFlag: 0,
            submitFlag: 0,
            list: req.list,
            isCheckScope: 0, //未盘点药品处理完成不再校验
        })
            .then(() => {
                dialog.hide().then(() => {
                    InventoryAgent.taskStatusPublisher.next();
                    Toast.show("汇总成功", { success: true }).then(() => ABCNavigator.popUntil(URLProtocols.INVENTORY_CHECK_LIST));
                });
            })
            .catch((error) => {
                dialog.hide().then(() => {
                    if (error instanceof ABCApiError && error?.detail?.error?.code == InventoryApiErrorCode.goodsStockSummaryErrorCode) {
                        //处理12100异常code
                        const { detail } = error?.detail?.error ?? {};
                        showConfirmDialog(
                            `提交失败：${detail?.errorTitle ?? ""}`,
                            <ScrollView>
                                <View>
                                    {detail.errorList
                                        .map((it: { name: string; msgs: string[] }) => `${it.name}:${it.msgs.join("，")}`)
                                        .join("\n")}
                                </View>
                            </ScrollView>
                        );
                    } else {
                        showConfirmDialog("汇总失败", error?.detail?.error?.message);
                    }
                });
            });
    }

    // 请求重新加载数据
    public requestReloadData(): void {
        this._loadDataTrigger?.next();
    }

    // 请求添加商品
    public requestAddGoods(qrscan: boolean): void {
        if (!this.innerState.canEdit) return;
        this.dispatch(new _EventAddGood(qrscan));
    }

    // 请求删除商品
    public requestDeleteGoods(goodsInfo: CheckTaskGoodItem): void {
        if (!this.innerState.canEdit) return;
        this.dispatch(new _EventDeleteGoods(goodsInfo));
    }
    // 请求变更药品任务
    public requestChangeMedicineTask(info: CheckTaskGoodItem): void {
        if (!this.innerState.canEdit) return;
        this.dispatch(new _EventChangeMedicineTask(info));
    }
    // 请求提交
    public requestSubmit(): void {
        if (!this.innerState.canEdit) return;
        if ((this.innerState.detailData?.list?.length ?? 0) == 0) {
            Toast.show("未选择任何药品", { warning: true });
            return;
        }
        this.dispatch(new _EventSubmit());
    }

    public requestLoadMore(): void {
        this.dispatch(new _EventLoadMore());
    }

    public requestBackPage(saveDraft?: (options: PostStockCheckTaskReq) => Promise<void>): void {
        this.dispatch(new _EventBackPage(saveDraft));
    }

    //
    public requestSummary(): void {
        this.dispatch(new _EventSummary());
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}

class _EventAddGood extends _Event {
    qrScan: boolean;
    constructor(qrScan: boolean) {
        super();
        this.qrScan = qrScan;
    }
}

class _EventDeleteGoods extends _Event {
    goodsInfo: CheckTaskGoodItem;
    constructor(goodsInfo: CheckTaskGoodItem) {
        super();
        this.goodsInfo = goodsInfo;
    }
}

class _EventChangeMedicineTask extends _Event {
    goodItem: CheckTaskGoodItem;
    constructor(data: CheckTaskGoodItem) {
        super();
        this.goodItem = data;
    }
}

class _EventSubmit extends _Event {}

class _EventContinueSubmit extends _Event {
    goodsInfoList: GoodsInfo[];
    title: string;

    constructor(title: string, goodsInfo: GoodsInfo[]) {
        super();
        this.goodsInfoList = goodsInfo;
        this.title = title;
    }
}

class _EventLoadMore extends _Event {}

class _EventBackPage extends _Event {
    saveDraft?(options: PostStockCheckTaskReq): Promise<void>;

    constructor(saveDraft?: (options: PostStockCheckTaskReq) => Promise<void>) {
        super();
        this.saveDraft = saveDraft;
    }
}

class _EventSummary extends _Event {}

class _EventContinueSummary extends _Event {
    goodsInfoList: GoodsInfo[];
    title: string;

    constructor(title: string, goodsInfo: GoodsInfo[]) {
        super();
        this.goodsInfoList = goodsInfo;
        this.title = title;
    }
}
