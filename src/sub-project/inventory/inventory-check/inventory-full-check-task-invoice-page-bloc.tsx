/**
 * create by <PERSON><PERSON>
 * desc:
 * create date 2021/2/3
 */

import { Bloc, BlocEvent } from "../../bloc";
import React from "react";
import { EventName } from "../../bloc/bloc";
import { BaseLoadingState } from "../../bloc/bloc-helper";
import { Subject } from "rxjs";
import { switchMap } from "rxjs/operators";
import { InventoryAgent } from "../data/inventory-agent";
import { ABCError } from "../../common-base-module/common-error";
import { CheckTaskDetails } from "./data/inventory-check-bean";
import {
    ExecuteCountInventoryItemStatus,
    InventoryApiErrorCode,
    InventoryClinicConfig,
    InventorySubTaskDetailsPageType,
    StockCheckTaskInfo,
} from "../data/inventory-bean";
import { DialogIndex, showConfirmDialog, showQueryDialog } from "../../base-ui/dialog/dialog-builder";
import { CreateCheckTaskWorkReq, InventoryCheckAgent } from "./data/inventory-check-agent";
import { Toast } from "../../base-ui/dialog/toast";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { InventorySubtaskDetailsPage } from "./full-check/inventory-subtask-details-page";
import { JsonMapper } from "../../common-base-module/json-mapper/json-mapper";
import { TimeUtils } from "../../common-base-module/utils";
import { UniqueKey } from "../../base-ui";
import _ from "lodash";
import { showOptionsBottomSheet } from "../../base-ui/dialog/bottom_sheet";
import { GoodsType } from "../../base-business/data/beans";
import { ClinicAgent } from "../../base-business/data/clinic-agent";
import { DoctorDetailInfo } from "../../base-business/data/clinic-data";
import { ABCApiError } from "../../net";
import { AddSubTaskDialog } from "./views/add-sub-task-dialog";
import URLProtocols from "../../url-dispatcher/url-protocols";
import { checkTaskDraftManager } from "../data/inventory-check-task-draft";
import { ModuleIds, userCenter } from "../../user-center/user-center";
import { ScrollView, Text, View } from "@hippy/react";
import { Sizes, TextStyles } from "../../theme";
import { InventoryCheckTypePanel } from "./views/inventory-check-type-panel";
import { GoodsTypeFilter } from "../views/goods-type-secondary-classification-panel";
import { runFuncBeforeCheckExpired } from "../../views/clinic-edition";
import { GoodsAgent, PharmacyListItem } from "../../data/goods/goods-agent";

const CHECK_PERMISSION = [
    ModuleIds.MODULE_ID_NONE,
    ModuleIds.MODULE_ID_INVENTORY,
    ModuleIds.MODULE_ID_INVENTORY_STOCK,
    ModuleIds.pharmacyInventory,
];

export class State extends BaseLoadingState {
    loading = true;
    detail?: CheckTaskDetails;

    taskId?: string;
    missionType?: number;
    employees: DoctorDetailInfo[] = [];
    showErrorHint = false;
    focusKey?: number;

    //goods配置相关信息--此处关注多药房
    pharmacyInfoConfig?: InventoryClinicConfig;
    /**
     * 筛选项的药房
     */
    currentPharmacy?: PharmacyListItem;
    //盘点药房(存储新建盘点单时选择的药房)
    entryPharmacy?: PharmacyListItem;
    pharmacyList?: PharmacyListItem[];

    clone(): State {
        return Object.assign(new State(), this);
    }

    get isCreate(): boolean {
        return !this.taskId;
    }

    get isMissionCreator(): boolean {
        return this.missionType === 1;
    }

    get disabledCollect(): boolean {
        if (!this.detail?.subTaskInfoItems?.length) return true;
        return (
            this.detail?.subTaskInfoItems.findIndex((item) => {
                return item.status !== ExecuteCountInventoryItemStatus.taskCompleted;
            }) > -1
        );
    }

    // 盘点范围的名称
    get checkRangeDisplay(): string {
        const stockCheckScope = this.detail?.taskInfo?.stockCheckScope;

        const checkType = GoodsType.GoodsTypeAndSubType2TypeId({
            type: stockCheckScope?.type,
            subType: stockCheckScope?.subType,
            cMSpec: stockCheckScope?.cMSpec,
        });

        const typeId = stockCheckScope?.typeIdList ?? [];
        if (!!checkType && !stockCheckScope?.customTypeIdList?.length && !typeId.some((i) => i == checkType?.toString())) {
            typeId.push(checkType.toString());
        }

        return (
            GoodsAgent.transGoodsClassifyName({
                typeId: typeId,
                customTypeId: stockCheckScope?.customTypeIdList,
            }) ?? "全部类型"
        );
    }
}

export class ScrollToFocusItemState extends State {
    static fromState(state: State): ScrollToFocusItemState {
        const newState = new ScrollToFocusItemState();
        Object.assign(newState, state);
        return newState;
    }
}

export class InventoryFullCheckTaskInvoicePageBloc extends Bloc<_Event, State> {
    static Context = React.createContext<InventoryFullCheckTaskInvoicePageBloc | undefined>(undefined);
    private _loadDataTrigger: Subject<number> = new Subject<number>();
    private _loadDoctorListData: Subject<number> = new Subject<number>();
    private _taskId?: string;
    private _taskType?: number;

    static fromContext(context: InventoryFullCheckTaskInvoicePageBloc): InventoryFullCheckTaskInvoicePageBloc {
        return context;
    }

    constructor(options: { id?: string; type?: number }) {
        super();
        this._taskId = options.id;
        this._taskType = options.type;
        this.innerState.taskId = options.id;
        this.innerState.missionType = options.type;

        this.dispatch(new _EventInit()).then();
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventAddSubTask, this._mapEventAddSubTask);
        map.set(_EventDeleteSubTask, this._mapEventDeleteSubTask);
        map.set(_EventCheckSubTask, this._mapEventCheckSubTask);
        map.set(_EventShowSubTaskDetail, this._mapEventShowSubTaskDetail);
        map.set(_EventStopCheckTask, this._mapEventStopCheckTask);
        map.set(_EventFinishCheck, this._mapEventFinishCheck);

        map.set(_EventChangeTaskName, this._mapEventChangeTaskName);
        map.set(_EventChangeTaskCheckScope, this._mapEventChangeTaskCheckScope);
        map.set(_EventChangeSubTaskName, this._mapEventChangeSubTaskName);
        map.set(_EventChangeSubTaskOwner, this._mapEventChangeSubTaskOwner);
        map.set(_EventSubmitCreateTask, this._mapEventSubmitCreateTask);
        map.set(_EventSelectMultiplePharmacy, this._mapEventSelectMultiplePharmacy);

        return map;
    }

    public update(state?: State): void {
        this.dispatch(new _EventUpdate(state)).then();
    }

    private async _initPageConfig(): Promise<void> {
        //多药房相关配置
        this.innerState.pharmacyInfoConfig = userCenter.inventoryClinicConfig;
        /**
         * 获取药房type
         */
        this.innerState.currentPharmacy = InventoryAgent.getCurrentPharmacy();
        this.innerState.entryPharmacy = _.cloneDeep(this.innerState.currentPharmacy);
        const pharmacyConfig = await GoodsAgent.getPharmacyList().catchIgnore();
        this.innerState.pharmacyList = pharmacyConfig?.rows;
    }
    private _initTrigger(): void {
        this._loadDataTrigger
            .pipe(
                switchMap(() => {
                    return InventoryAgent.getCheckTaskDetails(this._taskId!, this._taskType!).catch((e) => new ABCError(e));
                })
            )
            .subscribe(async (rsp) => {
                this.innerState.stopLoading();
                if (rsp instanceof ABCError) {
                } else {
                    this.innerState.detail = rsp;
                    for (const item of this.innerState.detail.subTaskInfoItems ?? []) {
                        if (item.taskId && item.status == ExecuteCountInventoryItemStatus.notStarted) {
                            const status = await checkTaskDraftManager.fileExists(item.taskId);
                            if (status) {
                                item.statusName = "盘点中";
                                item.status = ExecuteCountInventoryItemStatus.pendingSales;
                            }
                        }
                    }
                }
                this.update();
            })
            .addToDisposableBag(this);

        this._loadDoctorListData
            .pipe(
                switchMap(() => {
                    return ClinicAgent.getClinicEmployeesV3().catch((e) => new ABCError(e));
                })
            )
            .subscribe((rsp) => {
                if (rsp instanceof ABCError) {
                } else {
                    this.innerState.employees = rsp.filter((item) => {
                        if (item.clinicInfo) {
                            const moduleIds = item.clinicInfo.moduleIds?.split(",");
                            for (const item of moduleIds ?? []) {
                                if (CHECK_PERMISSION.findIndex((mId) => mId == Number(item)) > -1) {
                                    return item;
                                }
                            }
                        }
                    });
                }
            })
            .addToDisposableBag(this);

        InventoryAgent.taskStatusPublisher
            .subscribe(() => {
                if (this._taskId) {
                    this._loadDataTrigger.next();
                }
            })
            .addToDisposableBag(this);
    }
    private async *_mapEventInit(/*event: _EventInit*/): AsyncGenerator<State> {
        this.innerState.startLoading();
        await this._initPageConfig();
        this._initTrigger();
        if (!!this._taskId) {
            this._loadDataTrigger.next();
        } else {
            this._loadDoctorListData.next();
            this.innerState.stopLoading();
            this._init();
            this.update();
        }
    }

    private _init(): void {
        this.innerState.detail = JsonMapper.deserialize(CheckTaskDetails, {});
        const detail = this.innerState.detail;
        detail.taskInfo = JsonMapper.deserialize(StockCheckTaskInfo, {
            taskName: `${TimeUtils.formatDate(new Date())}全量盘点`,
            stockCheckScope: { type: undefined, subType: undefined, cMSpec: undefined },
        });
        detail.subTaskInfoItems = [];
        _.range(0, 2).forEach(() => {
            const newSubTask = new StockCheckTaskInfo();
            newSubTask.__taskId = UniqueKey();
            detail.subTaskInfoItems?.push(newSubTask);
        });
    }

    private async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
        if (event.state) {
            yield event.state;
            return;
        }
        yield this.innerState.clone();
    }

    private async *_mapEventAddSubTask(/*event: _EventAddSubTask*/): AsyncGenerator<State> {
        if (this.innerState.isCreate) {
            const newSubTask = new StockCheckTaskInfo();
            newSubTask.__taskId = UniqueKey();
            this.innerState.detail?.subTaskInfoItems?.push(newSubTask);
        } else {
            const subTask = await AddSubTaskDialog.show();
            if (subTask) {
                const req = {
                    taskId: this.innerState.detail?.taskInfo?.taskId,
                    subTaskItems: [subTask],
                };
                InventoryCheckAgent.createCheckTaskWork(req as CreateCheckTaskWorkReq)
                    .then(() => {
                        //提交成功重新拉取内容
                        InventoryAgent.taskStatusPublisher.next();
                        this._loadDataTrigger.next();
                    })
                    .catch(() => {
                        Toast.show("添加失败", { warning: true });
                    });
            }
        }
        this.update();
    }

    private async *_mapEventDeleteSubTask(event: _EventDeleteSubTask): AsyncGenerator<State> {
        if (this.innerState.isCreate) {
            if (this.innerState.detail?.subTaskInfoItems?.length == 1) return;
            const result = await showQueryDialog("确认后将删除子任务的盘点信息", "");
            if (result == DialogIndex.positive) {
                _.remove(this.innerState.detail?.subTaskInfoItems ?? [], (item) => item.__taskId == event.task.__taskId);
                this.update();
            }
        } else {
            if (!event.task.taskId) return;
            const result = await showQueryDialog("确认后将删除子任务的盘点信息", "");
            if (result == DialogIndex.positive) {
                InventoryCheckAgent.deleteCheckSubTask(event.task.taskId)
                    .then(() => {
                        this.innerState.startLoading();
                        this.update();
                        InventoryAgent.taskStatusPublisher.next();
                        this._loadDataTrigger.next();
                    })
                    .catch(() => Toast.show("删除失败", { warning: true }));
            }
        }
    }

    private async *_mapEventCheckSubTask(event: _EventCheckSubTask): AsyncGenerator<State> {
        ABCNavigator.navigateToPage<string>(
            <InventorySubtaskDetailsPage
                checkScopeDisplay={this.innerState.checkRangeDisplay}
                checkTask={event.task}
                isOnlineDraft={event.task.status != ExecuteCountInventoryItemStatus.notStarted}
                saveDraft={(options) => {
                    return InventoryCheckAgent.postCheckCoworkJobDraftByTaskId(options)
                        .then(() => this._loadDataTrigger.next())
                        .catch((error) => {
                            if (
                                error instanceof ABCApiError &&
                                error?.detail?.error?.code == InventoryApiErrorCode.goodsStockSummaryErrorCode
                            ) {
                                //处理12100异常code
                                const { detail } = error?.detail?.error ?? {};
                                showConfirmDialog(
                                    `提交失败：${detail?.errorTitle ?? ""}`,
                                    <ScrollView>
                                        <View>
                                            {detail.errorList
                                                .map((it: { name: string; msgs: string[] }) => `${it.name}:${it.msgs.join("，")}`)
                                                .join("\n")}
                                        </View>
                                    </ScrollView>
                                );
                            }
                        });
                }}
                pharmacyNo={event.task.pharmacy?.no}
            />
        ).then((status) => {
            if (status == "success") {
                InventoryAgent.taskStatusPublisher.next();
                ABCNavigator.popUntil(URLProtocols.INVENTORY_CHECK_LIST);
            }
        });
    }

    private async *_mapEventShowSubTaskDetail(event: _EventShowSubTaskDetail): AsyncGenerator<State> {
        ABCNavigator.navigateToPage(
            <InventorySubtaskDetailsPage
                checkTask={event.task}
                type={InventorySubTaskDetailsPageType.onlyRead}
                checkScopeDisplay={this.innerState.checkRangeDisplay}
            />
        );
    }

    private async *_mapEventStopCheckTask(/*event: _EventStopCheckTask*/): AsyncGenerator<State> {
        const result = await showQueryDialog("终止盘点将清空本次盘点数据", "");
        if (result == DialogIndex.positive) {
            InventoryCheckAgent.deleteCheckSubTask(this.innerState.detail!.taskInfo!.taskId!)
                .then(() => {
                    InventoryAgent.taskStatusPublisher.next();
                    Toast.show("终止成功", { success: true }).then(() => ABCNavigator.pop());
                })
                .catch(() => {
                    Toast.show("删除多人盘点任务出错", { warning: true });
                });
        }
        this.update();
    }

    private async *_mapEventFinishCheck(/*event: _EventFinishCheck*/): AsyncGenerator<State> {
        ABCNavigator.navigateToPage(
            <InventorySubtaskDetailsPage
                checkTask={this.innerState.detail!.taskInfo!}
                type={InventorySubTaskDetailsPageType.summary}
                checkScopeDisplay={this.innerState.checkRangeDisplay}
            />
        ).then();
    }

    private async *_mapEventChangeTaskName(event: _EventChangeTaskName): AsyncGenerator<State> {
        this.innerState.detail!.taskInfo!.taskName = event.name;
        this.update();
    }

    private async *_mapEventChangeTaskCheckScope(/*event: _EventChangeTaskCheckScope*/): AsyncGenerator<State> {
        const stockCheckScope = this.innerState.detail?.taskInfo?.stockCheckScope;
        const init = new GoodsTypeFilter();
        init.typeId = stockCheckScope?.typeIdList ?? [];
        init.customTypeId = stockCheckScope?.customTypeIdList ?? [];
        const oldTypeId = GoodsType.GoodsTypeAndSubType2TypeId({
            type: stockCheckScope?.type,
            cMSpec: stockCheckScope?.cMSpec,
            subType: stockCheckScope?.subType,
        });
        if (oldTypeId && !stockCheckScope?.typeIdList?.some((i) => i == oldTypeId.toString())) {
            init.typeId.push(oldTypeId.toString());
        }
        const result = await InventoryCheckTypePanel.show(init);
        if (!result) return;
        this.innerState.detail!.taskInfo!.stockCheckScope = {
            typeIdList: result.typeId,
            customTypeIdList: result.customTypeId,
        };
        this.update();
    }

    private async *_mapEventChangeSubTaskName(event: _EventChangeSubTaskName): AsyncGenerator<State> {
        const detail = this.innerState.detail;
        const subTasks = detail?.subTaskInfoItems;
        const _subTask = subTasks?.find((item) => item.__taskId == event.task.__taskId);
        if (_subTask) {
            _subTask.taskName = event.name;
        }
        this.update();
    }

    private async *_mapEventChangeSubTaskOwner(event: _EventChangeSubTaskOwner): AsyncGenerator<State> {
        const detail = this.innerState.detail;
        const subTasks = detail?.subTaskInfoItems;
        const _subTask = subTasks?.find((item) => item.__taskId == event.task.__taskId);
        if (_subTask) {
            const result = await showOptionsBottomSheet({
                title: "选择负责人",
                options: this.innerState.employees.map((item) => item.name ?? ""),
            });
            if (!result || !result.length) return;
            const employee = this.innerState.employees[result[0]];
            _subTask.ownerId = employee.id;
            _subTask.ownerName = employee.name;
        }
        this.update();
    }

    private async *_mapEventSubmitCreateTask(): AsyncGenerator<State> {
        const detail = this.innerState.detail;
        this.innerState.showErrorHint = true;
        const isOpenMultiplePharmacy = this.innerState.pharmacyInfoConfig?.isOpenMultiplePharmacy;
        if (isOpenMultiplePharmacy) {
            if (_.isUndefined(this.innerState.entryPharmacy?.no)) {
                Toast.show("请选择盘点药房", { warning: true });
                return;
            }
        }

        if (!detail?.taskInfo?.taskName) {
            //输入任务名
            Toast.show("输入任务名", { warning: true });
            return;
        } else if (!detail.taskInfo.stockCheckScope) {
            //选择盘点任务范围
            Toast.show("选择盘点任务范围", { warning: true });
            return;
        } else {
            if (!detail.subTaskInfoItems?.length) {
                //添加子任务
                Toast.show("添加子任务", { warning: true });
                return;
            }
            let index = 0;
            for (const subTask of detail.subTaskInfoItems ?? []) {
                if (!subTask.ownerId) {
                    Toast.show("选择负责人", { warning: true });
                    //选择负责人
                    this.innerState.focusKey = index;
                    return this.update(ScrollToFocusItemState.fromState(this.innerState));
                } else if (!subTask.taskName) {
                    //输入子任务名
                    Toast.show("输入子任务名", { warning: true });
                    this.innerState.focusKey = index;
                    return this.update(ScrollToFocusItemState.fromState(this.innerState));
                }
                index++;
            }
        }
        const req: CreateCheckTaskWorkReq = {
            subTaskItems: [],
            stockCheckScope: {},
            taskName: "",
            type: 1,
            pharmacyNo: this.innerState.entryPharmacy?.no,
        };

        req.taskName = detail.taskInfo.taskName;
        req.stockCheckScope = detail.taskInfo.stockCheckScope;
        detail.subTaskInfoItems.forEach((item) => {
            req.subTaskItems?.push({
                taskName: item.taskName,
                ownerId: item.ownerId,
            });
        });

        InventoryCheckAgent.createCheckTaskWork(req)
            .then(() => {
                InventoryAgent.taskStatusPublisher.next();
                showConfirmDialog(
                    "盘点创建成功",
                    <View>
                        <Text style={[TextStyles.t14NT2, { marginBottom: Sizes.dp6 }]}>任务负责人可通过以下方式进行子任务的盘点</Text>
                        <Text style={[TextStyles.t14NT2, { marginBottom: Sizes.dp4 }]}>1.登陆自己的系统账号在盘点列表中查看任务并执行</Text>
                        <Text style={[TextStyles.t14NT2]}>2.在APP首页中进入盘点任务查看并执行</Text>
                    </View>,
                    "我知道了"
                ).then(() => {
                    ABCNavigator.pop();
                });
            })
            .catch((error) => {
                if (error instanceof ABCApiError && error?.detail?.error?.code == "12240") {
                    showConfirmDialog(error?.detail?.error?.message ?? "", "");
                } else {
                    showConfirmDialog("提交失败", error?.detail?.error?.message);
                }
            });

        this.update();
    }

    private async *_mapEventSelectMultiplePharmacy(): AsyncGenerator<State> {
        const innerState = this.innerState,
            pharmacyList = innerState.pharmacyInfoConfig?.inventoryPharmacyList().filter((t) => t?.status == 1 && !!t?.enableCheck);
        const initIndex = pharmacyList?.findIndex((item) => item.no == innerState.entryPharmacy?.no) ?? -1;
        const result = await showOptionsBottomSheet({
            title: "选择库房",
            options: pharmacyList?.map((item) => item?.name ?? ""),
            initialSelectIndexes: new Set<number>([initIndex]),
        });
        if (_.isUndefined(result)) return;
        this.innerState.entryPharmacy = JsonMapper.deserialize(PharmacyListItem, pharmacyList?.[result[0]]);
        this.update();
    }

    /**
     * 添加子任务
     */
    @runFuncBeforeCheckExpired()
    public requestAddSubTask(): void {
        this.dispatch(new _EventAddSubTask());
    }

    /**
     * 删除子任务
     * @param task
     */
    @runFuncBeforeCheckExpired()
    public requestDeleteSubTask(task: StockCheckTaskInfo): void {
        this.dispatch(new _EventDeleteSubTask(task));
    }

    /**
     * 对子任务的操作 -- 执行或者修改
     * @param task
     */
    @runFuncBeforeCheckExpired()
    public requestCheckSubTask(task: StockCheckTaskInfo): void {
        this.dispatch(new _EventCheckSubTask(task));
    }

    /**
     * 查看任务详情
     * @param task
     */
    public requestShowSubTaskDetail(task: StockCheckTaskInfo): void {
        this.dispatch(new _EventShowSubTaskDetail(task));
    }

    /**
     * 终止盘点
     */
    @runFuncBeforeCheckExpired()
    public requestStopCheckTask(): void {
        this.dispatch(new _EventStopCheckTask());
    }

    /**
     * 查看汇总表并完成
     */
    @runFuncBeforeCheckExpired()
    public requestFinishCheck(): void {
        this.dispatch(new _EventFinishCheck());
    }

    /**
     * 修改任务名
     * @param name
     */
    @runFuncBeforeCheckExpired()
    public requestChangeTaskName(name: string): void {
        this.dispatch(new _EventChangeTaskName(name));
    }

    /**
     * 修改任务类型
     */
    @runFuncBeforeCheckExpired()
    public requestChangeTaskCheckScope(): void {
        this.dispatch(new _EventChangeTaskCheckScope());
    }

    /**
     * 修改子任务名字
     * @param name
     * @param task
     */
    @runFuncBeforeCheckExpired()
    public requestChangeSubTaskName(name: string, task: StockCheckTaskInfo): void {
        this.dispatch(new _EventChangeSubTaskName(name, task));
    }

    /**
     * 修改子任务创建人
     * @param task
     */
    @runFuncBeforeCheckExpired()
    public requestChangeSubTaskOwner(task: StockCheckTaskInfo): void {
        this.dispatch(new _EventChangeSubTaskOwner(task));
    }

    /**
     * 创建多人盘点任务
     */
    @runFuncBeforeCheckExpired()
    public requestSubmitCreateTask(): void {
        this.dispatch(new _EventSubmitCreateTask());
    }

    //盘点药房
    requestSelectMultiplePharmacy(): void {
        this.dispatch(new _EventSelectMultiplePharmacy());
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}

class _EventAddSubTask extends _Event {}

class _EventDeleteSubTask extends _Event {
    task: StockCheckTaskInfo;

    constructor(task: StockCheckTaskInfo) {
        super();
        this.task = task;
    }
}

class _EventCheckSubTask extends _Event {
    task: StockCheckTaskInfo;

    constructor(task: StockCheckTaskInfo) {
        super();
        this.task = task;
    }
}

class _EventShowSubTaskDetail extends _Event {
    task: StockCheckTaskInfo;

    constructor(task: StockCheckTaskInfo) {
        super();
        this.task = task;
    }
}

class _EventStopCheckTask extends _Event {}

class _EventFinishCheck extends _Event {}

class _EventChangeTaskCheckScope extends _Event {}

class _EventChangeTaskName extends _Event {
    name: string;

    constructor(name: string) {
        super();
        this.name = name;
    }
}

class _EventChangeSubTaskName extends _Event {
    name: string;
    task: StockCheckTaskInfo;

    constructor(name: string, task: StockCheckTaskInfo) {
        super();
        this.name = name;
        this.task = task;
    }
}

class _EventChangeSubTaskOwner extends _Event {
    task: StockCheckTaskInfo;

    constructor(task: StockCheckTaskInfo) {
        super();
        this.task = task;
    }
}

class _EventSubmitCreateTask extends _Event {}
class _EventSelectMultiplePharmacy extends _Event {}
