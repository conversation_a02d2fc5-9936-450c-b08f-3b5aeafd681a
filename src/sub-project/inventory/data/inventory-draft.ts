/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/7/31
 *
 * @description
 */
import { fromJsonToDate, JsonMapper, JsonProperty } from "../../common-base-module/json-mapper/json-mapper";
import { GoodsInfo, TraceableCodeList } from "../../base-business/data/beans";
import { Subject } from "rxjs";
import { LogUtils } from "../../common-base-module/log";
import _ from "lodash";
import { InventoryConst } from "../inventory-const";
import { InventoryInItem, InventoryInReq } from "../../data/goods/goods-agent";
import FileUtils from "../../common-base-module/file/file-utils";
import { userCenter } from "../../user-center";
import { InventoryExtendDataItem, ExtendData } from "./inventory-bean";
import { RowMatchInfo } from "../inventory-in/inventory-in-data/inventory-in-bean";
import { Colors } from "@app/theme";

export class InventoryMedicineItem {
    @JsonProperty({ type: GoodsInfo })
    goodsInfo?: GoodsInfo;
    qrcode?: string; //条形码

    dosageNum?: number; //剂量
    dosageUnit?: string; //剂量单位
    pieceNum?: number; //制剂数量
    pieceUnit?: string; //制剂单位
    useUnit?: string; //包装单位
    useCount?: number; //入库量

    useUnitCostPrice?: number; //进价

    @JsonProperty({ fromJson: fromJsonToDate })
    expiredTime?: Date; //效期

    @JsonProperty({ fromJson: fromJsonToDate })
    productionDate?: Date; //生产日期

    addInventory?: number; // 入库数量
    batchNumber?: string; //批号

    westernMedicine?: boolean; //药品类型，是否是西药

    dismounting?: boolean; // 是否拆零
    v2DisableStatus?: number;
    @JsonProperty({ type: InventoryExtendDataItem })
    extendData?: InventoryExtendDataItem; //南京地区--扩展字段
    @JsonProperty({ type: Array, clazz: TraceableCodeList })
    traceableCodeList?: TraceableCodeList[]; // 追溯码列表

    id?: string; // 拍照识别的商品，如果是待修正的，保存成线上草稿后，需要用这个字段来匹配list中替换的项
    rowMatchInfo?: RowMatchInfo; // 匹配失败时，保存匹配失败的行信息
    externalRelatedKey?: string; // 外部关联key
}

export class SupplierItem {
    supplierId?: string;
    name?: string;
    id?: string;
    status?: number; //SupplierItemStatus
    license?: string;
    contact?: string;
    mobile?: string;
    mark?: string;
    chainId?: string;
    createdBy?: string;

    @JsonProperty({ fromJson: fromJsonToDate })
    created?: Date;
}

export class InventoryDraft {
    @JsonProperty({ type: Array, clazz: InventoryMedicineItem })
    medicines: InventoryMedicineItem[] = [];

    @JsonProperty({ fromJson: fromJsonToDate })
    lastModifyTime = new Date(); //最近更改时间

    @JsonProperty({ type: SupplierItem })
    supplier?: SupplierItem; //供应商

    clinicId?: string;

    comment?: string; //备注
    outOrderNo?: string; //随货单号

    @JsonProperty({ ignore: true })
    hasChanged = false;

    @JsonProperty({ ignore: true })
    hasSubmit = false;

    @JsonProperty({ type: ExtendData })
    extendData?: ExtendData;

    toInventoryReq(): InventoryInReq {
        const req = new InventoryInReq();
        const items: InventoryInItem[] = [];
        this.medicines.forEach((item) => {
            const newItem = new InventoryInItem();
            newItem.goodsId = item.goodsInfo?.id;
            newItem.useUnit = item.useUnit;
            newItem.useCount = item.useCount;
            newItem.useUnitCostPrice = item.useUnitCostPrice;
            newItem.useTotalCostPrice = item.useUnitCostPrice! * item.useCount!;
            newItem.batchNo = item.batchNumber;
            newItem.expiryDate = item.expiredTime;
            newItem.productionDate = item.productionDate;

            items.push(newItem);
        });

        req.list = items;
        req.supplierId = this.supplier?.supplierId;
        req.clinicId = this.clinicId;
        req.comment = this.comment;
        req.outOrderNo = this.outOrderNo;

        req.extendData = this.extendData;
        return req;
    }
}

class InventoryDraftEvent {
    constructor(draft?: InventoryDraft) {
        this.draft = draft;
    }

    draft?: InventoryDraft;
}

export class InventoryDraftManager {
    static instance = new InventoryDraftManager();

    private constructor() {
        userCenter.sClinicChangeObserver.subscribe(async () => {
            this.reset();
            await this.loadDraft().catchIgnore();
            this.draftChangeObserver.next(new InventoryDraftEvent(this._draft));
        });
    }

    draftChangeObserver = new Subject<InventoryDraftEvent>();
    private _draft?: InventoryDraft;

    async loadDraft(): Promise<InventoryDraft | undefined> {
        if (this._draft != undefined) return this._draft;

        const fileName = await this.draftFile();
        try {
            const str = await FileUtils.readAsString(fileName);
            LogUtils.d(`loadDraft = str = ${str}, fileName =${fileName}`);
            if (!_.isEmpty(str)) this._draft = JsonMapper.deserialize(InventoryDraft, JSON.parse(str));
        } catch (e) {
            LogUtils.d(`loadDraft = str = ${e}, fileName =${fileName}`);
        }

        return this._draft;
    }

    draftFile(): Promise<string> {
        return InventoryConst.getDraftFile("inventory");
    }

    // async getMedicineItemByQRCode(qrCode: string): Promise<InventoryMedicineItem | undefined> {
    //     await this.loadDraft();
    //
    //     if (this._draft == undefined) return undefined;
    //
    //     for (const item of this._draft.medicines) {
    //         if (item.qrcode == qrCode) return item;
    //     }
    //
    //     return undefined;
    // }

    async removeDraft(): Promise<void> {
        try {
            const fileName = await this.draftFile();
            await FileUtils.deleteFile(fileName);
        } catch (e) {
            LogUtils.d("e =" + e);
        }
        this._draft = undefined;
        this.draftChangeObserver.next(new InventoryDraftEvent(this._draft));
    }

    public reset(): void {
        this._draft = undefined;
    }

    // async removeMedicine(medicine: InventoryMedicineItem): Promise<boolean> {
    //     if (_.isEmpty(this._draft?.medicines)) return true;
    //
    //     const index = this._draft!.medicines.findIndex((e) => medicine == e);
    //     if (index >= 0) {
    //         this._draft!.medicines.splice(index);
    //         this._draft!.lastModifyTime = new Date();
    //         this.draftChangeObserver.next(new InventoryDraftEvent(this._draft));
    //         await this.saveDraft(this._draft!);
    //     }
    //
    //     return true;
    // }

    // async addOrReplaceMedicine(item: InventoryMedicineItem): Promise<boolean> {
    //     this._draft = this._draft ?? (await this.createDraft());
    //     const index = this._draft!.medicines.findIndex((e) => e.qrcode == item.qrcode);
    //     if (index >= 0) {
    //         this._draft!.medicines.splice(index, 1, item);
    //     } else {
    //         this._draft!.medicines.push(item);
    //     }
    //
    //     this._draft!.lastModifyTime = new Date();
    //
    //     this.draftChangeObserver.next(new InventoryDraftEvent(this._draft));
    //     await this.saveDraft(this._draft);
    //
    //     return true;
    // }

    // async createDraft(): Promise<InventoryDraft> {
    //     const clinic = userCenter.clinic;
    //     this._draft = new InventoryDraft();
    //     this._draft.clinicId = clinic!.clinicId;
    //     LogUtils.d(`createDraft = ${this._draft.clinicId}`);
    //
    //     return this._draft;
    // }

    // async saveDraft(draft: InventoryDraft): Promise<boolean> {
    //     LogUtils.d(`saveDraft= ${draft}`);
    //     draft.hasChanged = false;
    //     const detailStr = JSON.stringify(draft);
    //     const fileName = await this.draftFile();
    //     await FileUtils.writeAsString(fileName, detailStr);
    //
    //     return true;
    // }
}

export const GET_SAVE_STATUS_INFO = (
    status: string
): {
    text: string;
    icon: string;
    iconColor?: string;
} => {
    let tipsInfo = {
        text: "修改内容会自动保存",
        icon: "",
        iconColor: "",
    };
    switch (status) {
        case "saving":
            tipsInfo = {
                text: "自动保存中…",
                icon: "s-cloud-line",
                iconColor: Colors.t3,
            };
            break;
        case "success":
            tipsInfo = {
                text: "已保存",
                icon: "s-cloudsaved-line",
                iconColor: Colors.g1,
            };
            break;
        case "error":
            tipsInfo = {
                text: "保存失败，点此重试",
                icon: "s-cloudsavedfailed-line",
                iconColor: Colors.r1,
            };
            break;
    }
    return tipsInfo;
};
