/**
 * create by <PERSON><PERSON>
 * desc:
 * create date 2021/1/28
 */
import React from "react";
import { View, Text } from "@hippy/react";
import { BaseBlocNetworkPage } from "../base-ui/base-page";
import { InventoryMedicineSearchPageBloc } from "./Inventory-medicine-search-page-bloc";
import { AbcListView } from "../base-ui/list/abc-list-view";
import { AppSearchBar } from "../base-ui/app-bar";
import { IconFontView, SizedBox, Spacer, UniqueKey } from "../base-ui";
import { ABCStyles, Colors, Sizes, TextStyles } from "../theme";
import { GoodsInfo } from "../base-business/data/beans";
import { AbcView } from "../base-ui/views/abc-view";
import { AbcTextInput } from "../base-ui/views/abc-text-input";
import { SearchParams } from "./data/inventory-bean";
import { <PERSON><PERSON>elper } from "../bloc/bloc-helper";
import { isNil } from "lodash";
import { AbcText } from "@app/abc-mobile-ui";
import { GoodsAgent } from "../data/goods/goods-agent";

interface InventoryMedicineSearchPageProps {
    searchParams?: SearchParams;
    placeholder?: string;
    displayStock?: boolean; // 是否展示库存
    callback?(goodsInfo?: GoodsInfo): void;
}

export class InventoryMedicineSearchPage extends BaseBlocNetworkPage<InventoryMedicineSearchPageProps, InventoryMedicineSearchPageBloc> {
    constructor(props: InventoryMedicineSearchPageProps) {
        super(props);
        this.bloc = new InventoryMedicineSearchPageBloc({
            callback: props.callback,
            searchParams: props.searchParams,
            displayStock: props.displayStock,
        });
    }

    componentDidMount(): void {
        BlocHelper.connectLoadingStatus(this.bloc, this, (state) => !state.list.length);
    }

    getAppBar(): JSX.Element {
        const { placeholder, searchParams } = this.props;
        let checkRangeDisplay = "";
        if (!isNil(searchParams?.typeId)) {
            checkRangeDisplay =
                GoodsAgent.transGoodsClassifyName({
                    typeId: searchParams?.typeId,
                    customTypeId: searchParams?.customTypeId,
                }) ?? "全部类型";
        }
        return (
            <View>
                <AppSearchBar
                    placeholder={placeholder ?? "输入药品名称搜索"}
                    rightPart={this.getRightAppBarIcons()}
                    onBackClick={this.onBackClick.bind(this)}
                    autoFocus={true}
                    onChangeText={(value) => this.bloc.requestChangeKeyword(value)}
                />
                {!!checkRangeDisplay && (
                    <View style={[Sizes.paddingLTRB(Sizes.dp16, Sizes.dp8), { backgroundColor: Colors.window_bg }]}>
                        <AbcText size={"mini"} theme={"T2"} style={{ flexShrink: 1 }}>
                            {`盘点范围：${checkRangeDisplay}`}
                        </AbcText>
                    </View>
                )}
            </View>
        );
    }

    getRightAppBarIcons(): JSX.Element[] {
        return [
            <View key={"scan"} onClick={() => this.bloc.requestScanQRCode()}>
                <IconFontView name={"scan"} color={Colors.mainColor} size={Sizes.dp16} />
            </View>,
        ];
    }

    renderContent(): JSX.Element | undefined {
        const state = this.bloc.currentState;
        const displayStock = this.props.displayStock;

        return (
            <View>
                <AbcListView
                    scrollEventThrottle={300}
                    numberOfRows={state.list.length}
                    dataSource={state.list}
                    getRowKey={(index) => state.list[index].id ?? UniqueKey()}
                    renderRow={(data) => <_GoodsInfoItem goodsInfo={data} callback={this.props.callback} displayStock={displayStock} />}
                />
            </View>
        );
    }
}

interface _GoodsInfoItemProps {
    goodsInfo?: GoodsInfo;
    displayStock?: boolean; // 是否显示库存信息
    callback?(goodsInfo?: GoodsInfo): void;
}

const _GoodsInfoItem: React.FC<_GoodsInfoItemProps> = (props) => {
    const { goodsInfo, displayStock } = props;
    return (
        <AbcView
            style={[ABCStyles.bottomLine, Sizes.paddingLTRB(Sizes.dp16, Sizes.dp10), { backgroundColor: Colors.white }]}
            onClick={() => {
                AbcTextInput.focusInput?.blur();
                props.callback?.(goodsInfo);
            }}
        >
            <View style={[ABCStyles.rowAlignCenter]}>
                <Text style={[TextStyles.t16MT1.copyWith({ lineHeight: Sizes.dp24 }), { flexShrink: 1 }]} numberOfLines={1}>
                    {goodsInfo?.displayName ?? ""}
                </Text>
                <Text style={[TextStyles.t14NT2.copyWith({ lineHeight: Sizes.dp20 }), { marginLeft: Sizes.dp4 }]}>
                    {goodsInfo?.displayTypeName ?? ""}
                </Text>
                <Spacer />
                {displayStock && (
                    <Text style={[TextStyles.t14NT2.copyWith({ lineHeight: Sizes.dp20 }), { marginLeft: Sizes.dp4 }]}>
                        {`${goodsInfo?.displayStockInfo(true) ?? "--"}`}
                    </Text>
                )}
            </View>
            <SizedBox height={Sizes.dp4} />
            <View style={[ABCStyles.rowAlignCenter]}>
                <Text
                    style={[
                        TextStyles.t14NT4.copyWith({ lineHeight: Sizes.dp20 }),
                        {
                            flexShrink: 1,
                            paddingRight: Sizes.dp4,
                        },
                    ]}
                >
                    {goodsInfo?.displayManufacturer ?? ""}
                </Text>
                <Text style={TextStyles.t14NT4.copyWith({ lineHeight: Sizes.dp20 })}>{goodsInfo?.packageSpec ?? ""}</Text>
            </View>
        </AbcView>
    );
};
