/**
 * create by <PERSON><PERSON>
 * desc:
 * create date 2021/1/29
 */
import React from "react";
import { ScrollView, Text, View } from "@hippy/react";
import { DividerLine, SafeAreaView, ToolBar, ToolBarButtonStyle1 } from "../../base-ui";
import { ListSettingItem } from "../../base-ui/views/list-setting-item";
import { GoodsInfo, GoodsInfoMaxCost, GoodsType, SheBaoPayModeEnum, SubClinicPricePriceMode } from "../../base-business/data/beans";
import { AbcView } from "../../base-ui/views/abc-view";
import { Colors, Sizes, TextStyles } from "../../theme";
import { userCenter } from "../../user-center";
import { CustomInput } from "../../base-ui/input/custom-input";
import { AbcCheckbox } from "../../base-ui/views/abc-checkbox";
import { PrecisionLimitFormatter } from "../../base-ui/utils/formatter";
import { GoodsAgent } from "../../data/goods/goods-agent";
import { BottomSheetHelper, showBottomSheet } from "../../base-ui/dialog/bottom_sheet";
import { BaseComponent } from "../../base-ui/base-component";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { Toast } from "../../base-ui/dialog/toast";
import { DialogIndex, showQueryDialog } from "../../base-ui/dialog/dialog-builder";
import { GoodsPriceUtils } from "./utils/goods-utils";
import { NumberKeyboardBuilder } from "../../base-ui/views/keyboards/number-keyboard";
import _, { isNil } from "lodash";
import { AbcFlexInputPrice } from "./views/abc-flex-input-price";
import { delayed } from "../../common-base-module/rxjs-ext/rxjs-ext";
import { NumberUtils } from "../../common-base-module/utils";
import { InventoryClinicConfig } from "../data/inventory-bean";
import { DeviceUtils } from "../../base-ui/utils/device-utils";

export interface InventoryChangePriceDialogProps {
    goodsInfo: GoodsInfo;
    disable?: boolean;
}

interface InventoryChangePriceDialogStates {
    goodsInfo: GoodsInfo;
}

export class InventoryChangePriceDialog extends BaseComponent<InventoryChangePriceDialogProps, InventoryChangePriceDialogStates> {
    private _flexInputPrice: AbcFlexInputPrice | null | undefined;

    static async show(options: InventoryChangePriceDialogProps): Promise<GoodsInfo> {
        const view = (
            <SafeAreaView
                showStatusBar={!DeviceUtils.isOhos()}
                style={{ flex: 1 }}
                statusBarColor={Colors.transparent}
                bottomSafeAreaColor={Colors.white}
            >
                <AbcView
                    style={{ height: DeviceUtils.isOhos() ? undefined : 180 }}
                    onClick={() => {
                        ABCNavigator.pop();
                    }}
                />
                <View
                    style={{
                        backgroundColor: Colors.white,
                        flex: 1,
                    }}
                >
                    {BottomSheetHelper.createTitleBar("修改价格")}
                    <InventoryChangePriceDialog {...options} />
                </View>
            </SafeAreaView>
        );
        return showBottomSheet(view);
    }

    private maxCostInfo?: GoodsInfoMaxCost;
    private showErrorHint = false;
    private _scale = 2;

    constructor(props: InventoryChangePriceDialogProps) {
        super(props);
        this.state = { goodsInfo: _.cloneDeep(this.props.goodsInfo) };
        if (props.goodsInfo.isChineseMedicine) this._scale = 4;

        if (this.clinicConfig && this.clinicConfig.subClinicPrice?.subSetPrice && !userCenter.clinic?.isChainAdminClinic) {
            const maxPercent = this.clinicConfig.subClinicPrice?.maxPricePercent ?? 0;
            const minPercent = this.clinicConfig.subClinicPrice?.minPricePercent ?? 0;
            this.chainMaxPackagePrice = (maxPercent / 100) * props.goodsInfo.chainPackagePrice!;
            this.chainMinPackagePrice = (minPercent / 100) * props.goodsInfo.chainPackagePrice!;
            this.chainMaxPiecePrice = (maxPercent / 100) * props.goodsInfo.chainPiecePrice!;
            this.chainMinPiecePrice = (minPercent / 100) * props.goodsInfo.chainPiecePrice!;
        }
    }

    componentDidMount(): void {
        super.componentDidMount();
        //获取当前商品的maxCost价格
        GoodsAgent.getGoodsInfoMaxCost(this.props.goodsInfo.id!).then((rsp) => (this.maxCostInfo = rsp));
        delayed(500).subscribe(() => {
            this._flexInputPrice?.textInput?.focus();
        });
    }

    get clinicConfig(): InventoryClinicConfig {
        return userCenter.inventoryClinicConfig ?? new InventoryClinicConfig();
    }

    get piecePrice(): number {
        return (this.state.goodsInfo.packagePrice ?? 0) / (this.state.goodsInfo.pieceNum ?? 1);
    }

    get showTax(): boolean {
        return !!(userCenter.clinic?.isChainAdminClinic || userCenter.clinic?.isNormalClinic);
    }

    // 是否允许自主定价
    get showSubSetPrice(): boolean {
        if (userCenter.inventoryClinicConfig && !userCenter.inventoryClinicConfig.subClinicPrice?.subSetPrice) return false;
        if (userCenter.inventoryClinicConfig && userCenter.inventoryClinicConfig.subClinicPrice?.subSetPriceAllClinics) return true;
        if (
            userCenter.clinic?.isChainAdminClinic &&
            userCenter.inventoryClinicConfig &&
            userCenter.inventoryClinicConfig.subClinicPrice?.subSetPrice
        )
            return true;
        return !!(
            userCenter.inventoryClinicConfig &&
            userCenter.inventoryClinicConfig.subClinicPrice?.subSetPriceClinics?.findIndex((item) => {
                return item.clinicId === userCenter.clinic?.clinicId;
            }) !== -1
        );
    }

    get maxPackagePrice(): number | string {
        const currentPackagePrice = this.state.goodsInfo.packagePrice;
        if (this.clinicConfig.subClinicPrice?.subSetPrice && currentPackagePrice) {
            return (Number(currentPackagePrice) * this.clinicConfig.subClinicPrice.maxPricePercent!) / 100;
        }
        return "";
    }

    get minPackagePrice(): number | string {
        const currentPackagePrice = this.state.goodsInfo.packagePrice;
        if (this.clinicConfig.subClinicPrice?.subSetPrice && currentPackagePrice) {
            return (Number(currentPackagePrice) * this.clinicConfig.subClinicPrice.minPricePercent!) / 100;
        }
        return "";
    }

    get maxPiecePrice(): number | string {
        const currentDismounting = this.state.goodsInfo.dismounting;
        const subType = this.state.goodsInfo.subType;
        const currentPiecePrice = this.piecePrice;
        if (this.clinicConfig.subClinicPrice?.subSetPrice && (currentDismounting || subType === 2) && currentPiecePrice) {
            return (Number(currentPiecePrice) * this.clinicConfig.subClinicPrice.maxPricePercent!) / 100;
        }
        return "";
    }

    get minPiecePrice(): number | string {
        const currentDismounting = this.state.goodsInfo.dismounting;
        const subType = this.state.goodsInfo.subType;
        const currentPiecePrice = this.piecePrice;
        if (this.clinicConfig.subClinicPrice?.subSetPrice && (currentDismounting || subType === 2) && currentPiecePrice) {
            return (Number(currentPiecePrice) * this.clinicConfig.subClinicPrice.minPricePercent!) / 100;
        }
        return "";
    }

    get packagePriceLabel(): string {
        return GoodsPriceUtils.priceRange(this.maxPackagePrice, this.minPackagePrice);
    }

    get piecePriceLabel(): string {
        return GoodsPriceUtils.priceRange(this.maxPiecePrice, this.minPiecePrice);
    }

    chainMaxPiecePrice: number | string = "";
    chainMinPiecePrice: number | string = "";
    chainMaxPackagePrice: number | string = "";
    chainMinPackagePrice: number | string = "";

    get chainPackagePriceRange(): string {
        return GoodsPriceUtils.priceRange(this.chainMaxPackagePrice, this.chainMinPackagePrice);
    }

    get chainPiecePriceRange(): string {
        return GoodsPriceUtils.priceRange(this.chainMaxPiecePrice, this.chainMinPiecePrice);
    }

    /**
     * 修改总部定价（中药）
     * @param text
     * @private
     */
    private _modifyChainPiecePrice(text: string): void {
        const goodsInfo = this.state.goodsInfo;
        goodsInfo.chainPiecePrice = text.trim().length ? Number(text) : undefined;
        this.setState({ goodsInfo: goodsInfo });
    }

    /**
     * 修改总部定价（西药）
     * @param text
     * @private
     */
    private _modifyChainPackagePrice(text: string): void {
        const goodsInfo = this.state.goodsInfo;
        goodsInfo.chainPackagePrice = text.trim().length ? Number(text) : undefined;
        this.setState({ goodsInfo: goodsInfo });
    }

    /**
     *
     * @param text
     * @private
     */
    private _modifyPackagePrice(text: string): void {
        const goodsInfo = this.state.goodsInfo;
        goodsInfo.packagePrice = text.trim().length ? Number(text) : undefined;
        //自动修改拆零价格
        if (_.isNumber(goodsInfo.pieceNum) && goodsInfo.pieceNum != 0 && _.isNumber(goodsInfo.packagePrice)) {
            goodsInfo.piecePrice = Number(NumberUtils.formatMaxFixed(goodsInfo.packagePrice / goodsInfo.pieceNum, this._scale));
        }
        this.setState({ goodsInfo: goodsInfo });
    }

    private _modifyDismountingStatus(status: boolean): void {
        const goodsInfo = this.state.goodsInfo;
        goodsInfo.dismounting = status ? 1 : 0;
        if (_.isNumber(goodsInfo.pieceNum) && goodsInfo.pieceNum != 0 && _.isNumber(goodsInfo.packagePrice)) {
            goodsInfo.piecePrice = Number(NumberUtils.formatMaxFixed(goodsInfo.packagePrice / goodsInfo.pieceNum, this._scale));
        }
        this.setState({ goodsInfo: goodsInfo });
    }

    private _modifyPiecePrice(text: string): void {
        const goodsInfo = this.state.goodsInfo;
        goodsInfo.piecePrice = text.trim().length ? Number(text) : undefined;
        this.setState({ goodsInfo: goodsInfo });
    }

    private _modifyInTaxRat(text: string): void {
        const goodsInfo = this.state.goodsInfo;
        goodsInfo.inTaxRat = text.trim().length ? Number(text) : undefined;
        this.setState({ goodsInfo: goodsInfo });
    }

    private _modifyOutTaxRat(text: string): void {
        const goodsInfo = this.state.goodsInfo;
        goodsInfo.outTaxRat = text.trim().length ? Number(text) : undefined;
        this.setState({ goodsInfo: goodsInfo });
    }

    private _modifyIsSellStatus(status: boolean): void {
        const goodsInfo = this.state.goodsInfo;
        goodsInfo.isSell = status ? 1 : 0;
        goodsInfo.resetPrice();
        this.setState({ goodsInfo: goodsInfo });
    }

    /**
     * 判断输入价格是否小于进价
     */
    validatePrice(): string | undefined {
        if (!this.maxCostInfo || !this.maxCostInfo.clinicName) return;
        return GoodsPriceUtils.validatePriceWithCost(this.state.goodsInfo, this.maxCostInfo).message;
    }

    /**
     * 提交商品信息
     * @private
     */
    private async _saveGoodsInfo(): Promise<void> {
        this.showErrorHint = false;
        const goodsInfo = this.state.goodsInfo;

        if (this.isSheBaoNationalCodeGoods()) {
            if (!goodsInfo.packagePrice || (goodsInfo.dismounting && !goodsInfo.piecePrice)) {
                this.showErrorHint = true;
                await Toast.show("医保要求售价必填且不能0", { warning: true });
                return;
            }
        }

        if (goodsInfo.type == GoodsType.medicine && (!goodsInfo.medicineCadn || !goodsInfo.medicineCadn.length)) {
            this.showErrorHint = true;
            Toast.show("通用名为空", { warning: true });
            return;
        } else {
            goodsInfo.medicineCadn = goodsInfo.medicineCadn ?? "";
        }
        if (goodsInfo.isChineseMedicine) {
        } else {
            if (!goodsInfo.pieceNum) {
                this.showErrorHint = true;
                Toast.show("制剂不能为空", { warning: true });
                return;
            }
            if (!goodsInfo.pieceUnit) {
                this.showErrorHint = true;
                Toast.show("制剂单位不能为空", { warning: true });
                return;
            }
            if (!goodsInfo.packageUnit) {
                this.showErrorHint = true;
                Toast.show("包装单位不能为空", { warning: true });
                return;
            }
        }
        //校验价格是否合规
        let validateInfo;
        if (goodsInfo.type == GoodsType.material && goodsInfo.isMedicalMaterial && !!goodsInfo.isSell) {
            //物资中的器械耗材才进行校验价格
            validateInfo = this._validatePackageUnitPrice();
            if ((!validateInfo || validateInfo.validate) && goodsInfo.dismounting) {
                validateInfo = this._validatePieceUnitPrice();
            }

            if (!validateInfo.validate) {
                this.showErrorHint = true;
                Toast.show(validateInfo.message!, { warning: true });
            }
        } else if (goodsInfo.type == GoodsType.medicine) {
            validateInfo = this._validatePackageUnitPrice();
            if ((!validateInfo || validateInfo.validate) && goodsInfo.dismounting) {
                validateInfo = this._validatePieceUnitPrice();
            }

            if (!validateInfo.validate) {
                this.showErrorHint = true;
                Toast.show(validateInfo.message!, { warning: true });
            }
        } else if (goodsInfo.isGoods) {
            validateInfo = this._validatePackageUnitPrice();
            if ((!validateInfo || validateInfo.validate) && goodsInfo.dismounting) {
                validateInfo = this._validatePieceUnitPrice();
            }

            if (!validateInfo.validate) {
                this.showErrorHint = true;
                Toast.show(validateInfo.message!, { warning: true });
            }
        }
        //校验价格是否符合
        if (this.showErrorHint) return this.forceUpdate();
        const validatePriceResult = this.validatePrice();
        if (validatePriceResult && validatePriceResult?.length) {
            const result = await showQueryDialog("确认修改？", validatePriceResult);
            if (result == DialogIndex.positive) {
                ABCNavigator.pop(this.state.goodsInfo);
            }
        } else {
            ABCNavigator.pop(this.state.goodsInfo);
        }
    }

    // 针对shebao属地为河北省的定点机构，且有医保对码的药品：售价校验不能为空，不能为0医保价格校验
    private isSheBaoNationalCodeGoods(): boolean {
        // 针对shebao属地为河北省的定点机构
        const heBeiDistraction = userCenter.inventoryClinicConfig?.chainReview?.currentRegionPriceNonZero;
        // 医保对码的药品(不过医保的话，也不需要校验shebaoPayMode)
        const isSheBaoGoods =
            !!this.state.goodsInfo?.shebao?.nationalCode &&
            (isNil(this.state.goodsInfo?.shebaoPayMode) || this.state.goodsInfo?.shebaoPayMode != SheBaoPayModeEnum.NO_USE);
        // 固定售价
        const fixedSellingPrice = this.state.goodsInfo?.priceType == SubClinicPricePriceMode.sellingPrice;
        return !!heBeiDistraction && isSheBaoGoods && fixedSellingPrice;
    }

    private _validatePieceUnitPrice(value?: number): { validate: boolean; message?: string } {
        return GoodsPriceUtils.validatePrice(
            value ?? this.state.goodsInfo.piecePrice,
            this.chainMaxPiecePrice,
            this.chainMinPiecePrice,
            "拆零售价",
            this.isSheBaoNationalCodeGoods()
        );
    }

    private _validatePackageUnitPrice(value?: number): { validate: boolean; message?: string } {
        return GoodsPriceUtils.validatePrice(
            value ?? this.state.goodsInfo.packagePrice,
            this.chainMaxPackagePrice,
            this.chainMinPackagePrice,
            "整装售价",
            this.isSheBaoNationalCodeGoods()
        );
    }

    private _renderChainPriceView(): JSX.Element {
        if (!(!userCenter.clinic?.isNormalClinic && !userCenter.clinic?.isChainAdminClinic)) return <View />;
        const goodsInfo = this.state.goodsInfo;
        return (
            <View style={{ backgroundColor: Colors.white }}>
                <ListSettingItem
                    title={"总部定价"}
                    bottomLine={true}
                    style={{ paddingHorizontal: Sizes.listHorizontalMargin }}
                    contentBuilder={() => {
                        const _editable = !this.props.disable;
                        if (goodsInfo.isChineseMedicine) {
                            return (
                                <View
                                    style={[
                                        {
                                            flexDirection: "row",
                                            justifyContent: "flex-start",
                                        },
                                    ]}
                                >
                                    <AbcFlexInputPrice
                                        ref={(ref) => {
                                            if (_editable && !this._flexInputPrice) {
                                                this._flexInputPrice = ref;
                                            }
                                        }}
                                        unit={goodsInfo.pieceUnit}
                                        editable={_editable}
                                        defaultValue={NumberUtils.formatMaxFixed(goodsInfo.chainPiecePrice ?? 0, this._scale)}
                                        formatter={PrecisionLimitFormatter(goodsInfo?.isChineseMedicine ? 4 : 2)}
                                        customKeyboardBuilder={new NumberKeyboardBuilder({})}
                                        style={{
                                            underlineColorAndroid: Colors.white,
                                            backgroundColor: Colors.white,
                                            height: Sizes.listItemHeight,
                                            ...(_editable ? TextStyles.t16NT1 : TextStyles.t16NT2),
                                        }}
                                        showPriceIcon={false}
                                        contentStyle={{ justifyContent: "flex-start" }}
                                        onChangeText={this._modifyChainPiecePrice.bind(this)}
                                    />
                                </View>
                            );
                        } else {
                            return (
                                <View
                                    style={[
                                        {
                                            flexDirection: "row",
                                            justifyContent: "flex-start",
                                        },
                                    ]}
                                >
                                    <AbcFlexInputPrice
                                        ref={(ref) => {
                                            if (_editable && !this._flexInputPrice) {
                                                this._flexInputPrice = ref;
                                            }
                                        }}
                                        unit={goodsInfo.packageUnit}
                                        editable={_editable}
                                        defaultValue={NumberUtils.formatMaxFixed(goodsInfo.chainPackagePrice ?? 0, this._scale)}
                                        formatter={PrecisionLimitFormatter(goodsInfo?.isChineseMedicine ? 4 : 2)}
                                        customKeyboardBuilder={new NumberKeyboardBuilder({})}
                                        style={{
                                            underlineColorAndroid: Colors.white,
                                            backgroundColor: Colors.white,
                                            height: Sizes.listItemHeight,
                                            ...(_editable ? TextStyles.t16NT1 : TextStyles.t16NT2),
                                        }}
                                        showPriceIcon={false}
                                        contentStyle={{ justifyContent: "flex-start" }}
                                        onChangeText={this._modifyChainPackagePrice.bind(this)}
                                    />
                                </View>
                            );
                        }
                    }}
                />
            </View>
        );
    }

    private _renderPriceDetail(): JSX.Element {
        const goodsInfo = this.state.goodsInfo;

        if (goodsInfo.type == GoodsType.material) {
            return this._renderMaterialPriceView();
        }

        if (goodsInfo.isGoods) {
            return this._renderGoodsPriceView();
        }

        if (+(goodsInfo.subType ?? 0) === 1 || +(goodsInfo.subType ?? 0) === 3)
            return (
                <View style={{ backgroundColor: Colors.white }}>
                    <View>
                        <ListSettingItem
                            title={userCenter.clinic?.isChainAdminClinic ? "总部定价" : "整装售价"}
                            style={{ paddingHorizontal: Sizes.listHorizontalMargin }}
                            showErrorBorder={this.showErrorHint && !this._validatePackageUnitPrice(goodsInfo.packagePrice).validate}
                            bottomLine={true}
                            endIcon={() => (
                                <Text style={[TextStyles.t14NT2, { lineHeight: Sizes.dp20, marginLeft: Sizes.dp6 }]}>
                                    {!userCenter.clinic?.isNormalClinic
                                        ? this.showSubSetPrice
                                            ? this.packagePriceLabel.length || this.chainPackagePriceRange.length
                                                ? `范围 ${
                                                      userCenter.clinic?.isChainAdminClinic
                                                          ? `${this.packagePriceLabel}`
                                                          : `${this.chainPackagePriceRange}`
                                                  }`
                                                : ""
                                            : "不支持门店自主定价"
                                        : ""}
                                </Text>
                            )}
                            contentBuilder={() => {
                                const _editable =
                                    !(userCenter.clinic?.isChainSubClinic && !this.showSubSetPrice) && !goodsInfo.v2DisableStatus;
                                return (
                                    <AbcFlexInputPrice
                                        ref={(ref) => {
                                            if (_editable && !this._flexInputPrice) {
                                                this._flexInputPrice = ref;
                                            }
                                        }}
                                        unit={goodsInfo.packageUnit}
                                        editable={_editable}
                                        placeholder={"点击输入"}
                                        defaultValue={NumberUtils.formatMaxFixed(goodsInfo.packagePrice ?? 0, this._scale)}
                                        formatter={PrecisionLimitFormatter(goodsInfo?.isChineseMedicine ? 4 : 2)}
                                        customKeyboardBuilder={new NumberKeyboardBuilder({})}
                                        style={{
                                            underlineColorAndroid: Colors.white,
                                            backgroundColor: Colors.white,
                                            height: Sizes.listItemHeight,
                                            ...(_editable ? TextStyles.t16NT1 : TextStyles.t16NT2),
                                        }}
                                        showPriceIcon={false}
                                        contentStyle={{ justifyContent: "flex-start" }}
                                        onChangeText={this._modifyPackagePrice.bind(this)}
                                    />
                                );
                            }}
                        />
                    </View>

                    {/*<DividerLine lineHeight={Sizes.dp8} />*/}

                    <View>
                        {!this.props.disable && !!this.state.goodsInfo.dismounting && (
                            <AbcCheckbox
                                text={"允许拆零销售"}
                                style={{ paddingHorizontal: Sizes.listHorizontalMargin, paddingVertical: Sizes.dp10 }}
                                enable={!this.props.disable}
                                check={!!this.state.goodsInfo.dismounting}
                                onChange={this._modifyDismountingStatus.bind(this)}
                            />
                        )}
                        <DividerLine lineHeight={Sizes.dpHalf} />
                        {!!this.state.goodsInfo.dismounting && (
                            <ListSettingItem
                                title={"拆零售价"}
                                style={{ paddingHorizontal: Sizes.listHorizontalMargin }}
                                bottomLine={true}
                                showErrorBorder={
                                    this.showErrorHint && !this._validatePieceUnitPrice(goodsInfo.piecePrice ?? this.piecePrice).validate
                                }
                                endIcon={() => (
                                    <Text style={[TextStyles.t14NT2, { lineHeight: Sizes.dp20, marginLeft: Sizes.dp6 }]}>
                                        {!userCenter.clinic?.isNormalClinic && this.showSubSetPrice && this.piecePrice
                                            ? this.piecePriceLabel.length || this.chainPiecePriceRange.length
                                                ? `范围 ${
                                                      userCenter.clinic?.isChainAdminClinic
                                                          ? `${this.piecePriceLabel}`
                                                          : `${this.chainPiecePriceRange}`
                                                  }`
                                                : ""
                                            : ""}
                                    </Text>
                                )}
                                contentBuilder={() => {
                                    const _editable =
                                        !(userCenter.clinic?.isChainSubClinic && !this.showSubSetPrice) && !goodsInfo.v2DisableStatus;
                                    return (
                                        <AbcFlexInputPrice
                                            ref={(ref) => {
                                                if (_editable && !this._flexInputPrice) {
                                                    this._flexInputPrice = ref;
                                                }
                                            }}
                                            editable={_editable}
                                            returnKeyType={"done"}
                                            multiline={false}
                                            syncTextOnBlur={true} //解决更改值后切换医生props不改变问题
                                            numberOfLines={1}
                                            placeholder={"点击输入"}
                                            defaultValue={NumberUtils.formatMaxFixed(goodsInfo.piecePrice ?? this.piecePrice, this._scale)}
                                            formatter={PrecisionLimitFormatter(goodsInfo?.isChineseMedicine ? 4 : 2)}
                                            customKeyboardBuilder={new NumberKeyboardBuilder({})}
                                            style={{
                                                underlineColorAndroid: Colors.white,
                                                backgroundColor: Colors.white,
                                                height: Sizes.listItemHeight,
                                                ...(_editable ? TextStyles.t16NT1 : TextStyles.t16NT2),
                                            }}
                                            unit={goodsInfo.pieceUnit}
                                            showPriceIcon={false}
                                            contentStyle={{ justifyContent: "flex-start" }}
                                            onChangeText={this._modifyPiecePrice.bind(this)}
                                        />
                                    );
                                }}
                            />
                        )}
                    </View>
                </View>
            );
        if (+(goodsInfo.subType ?? 0) === 2)
            return (
                <View style={{ backgroundColor: Colors.white }}>
                    <ListSettingItem
                        title={"整装售价"}
                        style={{ paddingHorizontal: Sizes.listHorizontalMargin }}
                        endIcon={() => (
                            <Text style={[TextStyles.t14NT2, { lineHeight: Sizes.dp20, marginLeft: Sizes.dp6 }]}>
                                {!userCenter.clinic?.isNormalClinic
                                    ? this.showSubSetPrice
                                        ? this.piecePriceLabel.length || this.chainPiecePriceRange.length
                                            ? `范围 ${
                                                  userCenter.clinic?.isChainAdminClinic
                                                      ? `${this.piecePriceLabel}`
                                                      : `${this.chainPiecePriceRange}`
                                              }`
                                            : ""
                                        : "不支持门店自主定价"
                                    : ""}
                            </Text>
                        )}
                        contentBuilder={() => {
                            const _editable = !(userCenter.clinic?.isChainSubClinic && !this.showSubSetPrice) && !goodsInfo.v2DisableStatus;
                            return (
                                <AbcFlexInputPrice
                                    ref={(ref) => {
                                        if (_editable && !this._flexInputPrice) {
                                            this._flexInputPrice = ref;
                                        }
                                    }}
                                    unit={goodsInfo.pieceUnit ?? "g"}
                                    editable={!(userCenter.clinic?.isChainSubClinic && !this.showSubSetPrice)}
                                    placeholder={"点击输入"}
                                    defaultValue={NumberUtils.formatMaxFixed(goodsInfo.piecePrice ?? 0, this._scale)}
                                    formatter={PrecisionLimitFormatter(goodsInfo?.isChineseMedicine ? 4 : 2)}
                                    customKeyboardBuilder={new NumberKeyboardBuilder({})}
                                    style={{
                                        underlineColorAndroid: Colors.white,
                                        backgroundColor: Colors.white,
                                        height: Sizes.listItemHeight,
                                        ...(_editable ? TextStyles.t16NT1 : TextStyles.t16NT2),
                                    }}
                                    showPriceIcon={false}
                                    contentStyle={{ justifyContent: "flex-start" }}
                                    onChangeText={this._modifyPiecePrice.bind(this)}
                                />
                            );
                        }}
                    />
                </View>
            );
        return <View />;
    }

    private _renderMaterialPriceView(): JSX.Element {
        const goodsInfo = this.state.goodsInfo;
        if (!goodsInfo.isMedicalMaterial) return <View />;
        return this._renderGoodsPriceView();
    }

    private _renderGoodsPriceView(): JSX.Element {
        const canEdit = !this.props.disable,
            goodsInfo = this.state.goodsInfo;
        return (
            <View style={{ marginTop: Sizes.dp8, backgroundColor: Colors.white }}>
                <AbcCheckbox
                    text={"允许对外销售"}
                    style={{ paddingHorizontal: Sizes.listHorizontalMargin, paddingVertical: Sizes.dp10 }}
                    enable={canEdit}
                    check={!!goodsInfo.isSell}
                    onChange={this._modifyIsSellStatus.bind(this)}
                />
                {!!goodsInfo.isSell && (
                    <ListSettingItem
                        title={userCenter.clinic?.isChainAdminClinic ? "总部定价" : "整装售价"}
                        endIcon={() => (
                            <Text style={[TextStyles.t14NT2, { lineHeight: Sizes.dp20, marginLeft: Sizes.dp6 }]}>
                                {!userCenter.clinic?.isNormalClinic
                                    ? this.showSubSetPrice
                                        ? this.packagePriceLabel.length || this.chainPackagePriceRange.length
                                            ? `范围 ${
                                                  userCenter.clinic?.isChainAdminClinic
                                                      ? `${this.packagePriceLabel}`
                                                      : `${this.chainPackagePriceRange}`
                                              }`
                                            : ""
                                        : "不支持门店自主定价"
                                    : ""}
                            </Text>
                        )}
                        style={{ paddingHorizontal: Sizes.listHorizontalMargin }}
                        showErrorBorder={this.showErrorHint && !this._validatePackageUnitPrice().validate}
                        bottomLine={true}
                        contentBuilder={() => {
                            const _editable = !(userCenter.clinic?.isChainSubClinic && !this.showSubSetPrice) && !goodsInfo.v2DisableStatus;
                            return (
                                <AbcFlexInputPrice
                                    ref={(ref) => {
                                        if (_editable && !this._flexInputPrice) {
                                            this._flexInputPrice = ref;
                                        }
                                    }}
                                    unit={goodsInfo.packageUnit}
                                    editable={_editable}
                                    placeholder={"点击输入"}
                                    defaultValue={NumberUtils.formatMaxFixed(goodsInfo.packagePrice ?? 0, this._scale)}
                                    formatter={PrecisionLimitFormatter(goodsInfo?.isChineseMedicine ? 4 : 2)}
                                    customKeyboardBuilder={new NumberKeyboardBuilder({})}
                                    style={{
                                        underlineColorAndroid: Colors.white,
                                        backgroundColor: Colors.white,
                                        height: Sizes.listItemHeight,
                                        ...(_editable ? TextStyles.t16NT1 : TextStyles.t16NT2),
                                    }}
                                    showPriceIcon={false}
                                    contentStyle={{ justifyContent: "flex-start" }}
                                    onChangeText={this._modifyPackagePrice.bind(this)}
                                />
                            );
                        }}
                    />
                )}
                {!!goodsInfo.isSell && !goodsInfo.isChineseMedicine && (
                    <AbcCheckbox
                        text={"允许拆零销售"}
                        style={{ paddingHorizontal: Sizes.listHorizontalMargin, paddingVertical: Sizes.dp10 }}
                        enable={canEdit}
                        check={!!goodsInfo.dismounting}
                        onChange={this._modifyDismountingStatus.bind(this)}
                    />
                )}
                {!!goodsInfo.isSell && !goodsInfo.isChineseMedicine && !!goodsInfo.dismounting && (
                    <ListSettingItem
                        title={"拆零售价"}
                        endIcon={() => (
                            <Text style={[TextStyles.t14NT2, { lineHeight: Sizes.dp20, marginLeft: Sizes.dp6 }]}>
                                {!userCenter.clinic?.isNormalClinic && this.showSubSetPrice && goodsInfo.piecePrice
                                    ? `范围 ${
                                          userCenter.clinic?.isChainAdminClinic ? `${this.piecePriceLabel}` : `${this.chainPiecePriceRange}`
                                      }`
                                    : ""}
                            </Text>
                        )}
                        style={{ paddingHorizontal: Sizes.listHorizontalMargin }}
                        showErrorBorder={this.showErrorHint && !this._validatePieceUnitPrice().validate}
                        bottomLine={true}
                        contentBuilder={() => {
                            const _editable = !(userCenter.clinic?.isChainSubClinic && !this.showSubSetPrice) && !goodsInfo.v2DisableStatus;
                            return (
                                <AbcFlexInputPrice
                                    ref={(ref) => {
                                        if (_editable && !this._flexInputPrice) {
                                            this._flexInputPrice = ref;
                                        }
                                    }}
                                    editable={_editable}
                                    placeholder={"点击输入"}
                                    defaultValue={NumberUtils.formatMaxFixed(goodsInfo.piecePrice ?? this.piecePrice, this._scale)}
                                    formatter={PrecisionLimitFormatter(goodsInfo?.isChineseMedicine ? 4 : 2)}
                                    customKeyboardBuilder={new NumberKeyboardBuilder({})}
                                    style={{
                                        underlineColorAndroid: Colors.white,
                                        backgroundColor: Colors.white,
                                        height: Sizes.listItemHeight,
                                        ...(_editable ? TextStyles.t16NT1 : TextStyles.t16NT2),
                                    }}
                                    unit={goodsInfo.pieceUnit}
                                    showPriceIcon={false}
                                    contentStyle={{ justifyContent: "flex-start" }}
                                    onChangeText={this._modifyPiecePrice.bind(this)}
                                />
                            );
                        }}
                    />
                )}
            </View>
        );
    }

    private _renderTaxView(): JSX.Element {
        const goodsInfo = this.state.goodsInfo;
        return (
            <View style={[{ backgroundColor: Colors.white }]}>
                <DividerLine lineHeight={Sizes.dp8} />
                <ListSettingItem
                    title={"进项税率"}
                    style={{ flex: 1, paddingHorizontal: Sizes.listHorizontalMargin }}
                    bottomLine={true}
                    contentBuilder={() => {
                        return (
                            <CustomInput
                                disable={this.props.disable}
                                style={{ flex: 1, textAlign: "right" }}
                                textStyle={TextStyles.t16NT1}
                                borderType={"none"}
                                value={goodsInfo.inTaxRat}
                                formatter={PrecisionLimitFormatter(0, 101)}
                                unit={"%"}
                                alwaysShowUtil={true}
                                onChange={this._modifyInTaxRat.bind(this)}
                            />
                        );
                    }}
                />
                {!!goodsInfo.isSell && (
                    <ListSettingItem
                        title={"销项税率"}
                        style={{ flex: 1, paddingHorizontal: Sizes.listHorizontalMargin }}
                        contentBuilder={() => {
                            return (
                                <CustomInput
                                    disable={this.props.disable}
                                    style={{ flex: 1, textAlign: "right" }}
                                    textStyle={TextStyles.t16NT1}
                                    borderType={"none"}
                                    value={goodsInfo.outTaxRat}
                                    formatter={PrecisionLimitFormatter(0, 101)}
                                    unit={"%"}
                                    alwaysShowUtil={true}
                                    onChange={this._modifyOutTaxRat.bind(this)}
                                />
                            );
                        }}
                    />
                )}
            </View>
        );
    }

    render(): JSX.Element {
        return (
            <View style={{ flex: 1, backgroundColor: Colors.window_bg }}>
                <ScrollView style={{ flex: 1 }}>
                    {this._renderChainPriceView()}
                    {this._renderPriceDetail()}
                    {this.showTax && this._renderTaxView()}
                </ScrollView>
                <ToolBar hideWhenKeyboardShow={true}>
                    <ToolBarButtonStyle1 text={"保存"} onClick={this._saveGoodsInfo.bind(this)} />
                </ToolBar>
            </View>
        );
    }
}
