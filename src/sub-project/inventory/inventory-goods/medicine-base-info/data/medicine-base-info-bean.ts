/**
 * create by <PERSON><PERSON>
 * desc:
 * create date 2021/2/7
 */
import { dateToYyyyMMddString, fromJsonToDate, JsonProperty } from "../../../../common-base-module/json-mapper/json-mapper";
import { BaseMedicineTypeEnum, TraceableCodeNoInfo } from "../../../../base-business/data/beans";
import { BusinessScopeList } from "../../../../base-business/data/clinic-data";
import _ from "lodash";

export enum CreateGoodsClientType {
    cadn = "medicine-cadn",
    tradename = "medicine-tradename",
    manufacturer = "medicine-manufacturer",
}

export interface MedicineCreateSearchParams {
    client?: string; // (生产厂家 medicine-manufacturer/material-manufacturer) (注册证号 material-registration-no)
    key_word?: string; // 搜索key
    cadn?: string; // 通用名
    tradeName?: string; // 商品名
    registered_name?: string; // 物资名称
    manufacturer?: string; // 生产厂家
    collapse?: number; //
}

export interface GoodsSearchDomainMedicineParams {
    keywords?: string;
    type?: number;
    offset?: number;
    limit?: number;
}

export class CreateGoodsHitItem {
    customTypeId?: number;
    medicineId?: number;

    fields?: {
        medicine_cadn?: Array<string>;
        medicine_manufacturer?: Array<string>;
        tradeName?: Array<string>;
        registered_name_keyword?: Array<string>; // 器械耗材
        registered_no_keyword?: Array<string>; // 注册证号
        manufacturer_keyword?: Array<string>; // 生产厂家
    };
    @JsonProperty({ name: "medicine_manufacturer" })
    medicineManufacturer?: string;
    @JsonProperty({ name: "medicine_cadn" })
    medicineCadn?: string;
    @JsonProperty({ name: "medicine_dosage_form" })
    medicineDosageForm?: string;
    @JsonProperty({ name: "medicine_sub_type" })
    medicineSubType?: number;
    // @JsonProperty({ name: "medicine_type" })
    typeId?: number;
    tradeName?: string; // 药品名
    @JsonProperty({ name: "medicine_bar_code" })
    barCode?: string; // 条形码
    @JsonProperty({ name: "medicine_approved_code" })
    medicineNmpn?: string; // 国药准字
    @JsonProperty({ name: "medicine_manufacturer" })
    manufacturerFull?: string; // 生产厂家全称
    @JsonProperty({ name: "medicine_dosage_num" })
    medicineDosageNum?: number; // 剂型数量
    @JsonProperty({ name: "medicine_dosage_unit" })
    medicineDosageUnit?: string; // 剂型单位
    @JsonProperty({ name: "medicine_dosage_form_num" })
    medicineDosageFormNum?: number; // 制剂数量
    @JsonProperty({ name: "medicine_dosage_form_unit" })
    medicineDosageFormUnit?: string; // 制剂单位
    @JsonProperty({ name: "medicine_package_unit" })
    medicinePackageUnit?: string; // 包装单位

    manufacturer_keyword?: string;
    manufacturer_fuzzy?: string;
    registered_no?: string;
    type?: number;
    registered_no_keyword?: string;
    sub_type?: number;
    id?: number;
    registered_name?: string;
    registered_no_fuzzy?: string;
    manufacturer?: string;
    registered_name_keyword?: string;
    registered_name_fuzzy?: string;
    _score?: number;
    storage?: string; //存储条件
    @JsonProperty({ type: Array, clazz: BusinessScopeList })
    businessScopeList?: BusinessScopeList[]; //所属经营范围
    @JsonProperty({ fromJson: fromJsonToDate })
    medicineNmpnStartExpiryDate?: Date; //批准问号有效期
    @JsonProperty({ fromJson: fromJsonToDate })
    medicineNmpnEndExpiryDate?: Date; //批准问号有效期
    get _medicineNmpnExpiryDate(): string {
        if (!this.medicineNmpnStartExpiryDate || !this.medicineNmpnEndExpiryDate) return "";
        return `${dateToYyyyMMddString(this.medicineNmpnStartExpiryDate)}-${dateToYyyyMMddString(this.medicineNmpnEndExpiryDate)}`;
    }
    get _businessScopeDisplayName(): string {
        const firstBusinessScope = _.first(this.businessScopeList);
        return (!!firstBusinessScope?.displayName ? firstBusinessScope?.displayName : firstBusinessScope?.name) ?? "";
    }

    get displayText(): string {
        return (
            this.fields?.medicine_cadn?.[0] ??
            this.fields?.medicine_manufacturer?.[0] ??
            this.fields?.tradeName?.[0] ??
            this.fields?.registered_name_keyword?.[0] ??
            this.fields?.registered_no_keyword?.[0] ??
            this.fields?.manufacturer_keyword?.[0] ??
            ""
        );
    }
}

export class GetCreateGoodsHitListRsp {
    total?: number;
    @JsonProperty({ type: Array, clazz: CreateGoodsHitItem })
    hits?: CreateGoodsHitItem[];
}

export class GoodsSearchDomainMedicineCreateListItem {
    id?: string;
    typeId?: number;
    type?: number;
    subType?: number;
    barCode?: string;
    medicineNmpn?: string;
    name?: string;
    manufacturer?: string;
    medicineCadn?: string;
    dispSpec?: string;
    packageUnit?: string;
    pieceNum?: number;
    pieceUnit?: string;
    otcType?: number;
    otcTypeName?: string;
    dangerIngredient?: number;
    maintainType?: number;
    storageType?: number;
    dddOfAntibiotics?: number;
    antibiotics?: number;
    unitOfAntibiotics?: string;
    shebaoNationalCode?: string;
    pharmacologicId?: string;
    pharmacologicFullName?: string;
    mha?: string;
    manufacturerFull?: string;
    shelfLife?: number; // 保质期
    baseMedicineType?: BaseMedicineTypeEnum; // 基药类型
    dosageFormType?: number; // 剂型类型
    @JsonProperty({ type: Array, clazz: BusinessScopeList })
    businessScopes?: BusinessScopeList[]; //所属经营范围
    materialSpec?: string;
    @JsonProperty({ type: TraceableCodeNoInfo })
    traceableCodeNoInfo?: TraceableCodeNoInfo; //追溯码
}
export class GetGoodsSearchDomainMedicineCreateRsp {
    total?: number;
    list?: GoodsSearchDomainMedicineCreateListItem[];
    query?: string[];
}
