/**
 * create by <PERSON><PERSON>
 * desc:
 * create date 2021/2/7
 */

import { Bloc, BlocEvent } from "../../../bloc";
import React from "react";
import { actionEvent, EventName } from "../../../bloc/bloc";
import { BaseLoadingState } from "../../../bloc/bloc-helper";
import {
    AntibioticEnum,
    BaseMedicineTypeEnum,
    GoodsInfo,
    GoodsInfoMaxCost,
    GoodsInfoShebao,
    GoodsMultiPriceView,
    GoodsSubType,
    GoodsType,
    GoodsTypeId,
    GspModifyStatusEnum,
    IngredientEnum,
    MaintainTypeEnum,
    MedicalMaterialDeviceType,
    OtcTypeEnum,
    SheBaoPayModeEnum,
    SubClinicPricePriceMode,
    westernMedicineDosageFormTypeIds,
} from "../../../base-business/data/beans";
import { Subject } from "rxjs";
import { switchMap } from "rxjs/operators";
import {
    GoodsAgent,
    GoodsFeeTypeListI<PERSON>,
    GoodsInterfaceClassificationItem,
    GoodsSecondaryClassificationItem,
} from "../../../data/goods/goods-agent";
import { ABCError } from "../../../common-base-module/common-error";
import { MedicineCreateSearchName } from "./medicine-create-search-name";
import { CreateGoodsClientType, MedicineCreateSearchParams } from "./data/medicine-base-info-bean";
import { BarcodeScanner } from "../../../base-ui/camera/barcode-scanner";
import { showOptionsBottomSheet } from "../../../base-ui/dialog/bottom_sheet";
import { ABCNavigator } from "../../../base-ui/views/abc-navigator";
import _, { isNil } from "lodash";
import { DialogIndex, showConfirmDialog, showQueryDialog } from "../../../base-ui/dialog/dialog-builder";
import { Toast } from "../../../base-ui/dialog/toast";
import { userCenter } from "../../../user-center";
import { GoodsPriceUtils } from "../utils/goods-utils";
import { MedicineBaseInfoAgent } from "./data/medicine-base-info-agent";
import { errorToStr } from "../../../common-base-module/utils";
import { ABCUtils } from "../../../base-ui/utils/utils";
import { InventoryAgent } from "../../data/inventory-agent";
import { JsonMapper } from "../../../common-base-module/json-mapper/json-mapper";
import { MedicineSpecChangeDetail, MedicineSpecChangeDialog } from "./view/medicine-spec-change-dialog";
import {
    ChainReview,
    GoodsInventoryInfo,
    GoodsPkgCostPriceMakeUpMinMaxItem,
    InOutTaxList,
    InventoryClinicConfig,
    InventoryGoodsAdjustInfo,
    PriceAdjustmentItemStatus,
} from "../../data/inventory-bean";
import {
    GetSheBaoCodeDetailParams,
    GetSheBaoCodeDetailRsp,
    GoodsSecondaryClassificationCustomTypesItem,
    InventoryGoodsAgent,
    MedicineCategoryItem,
    SheBaoCodeSearchGoodsListItem,
    GoodsTagTypesItemListItem,
} from "../data/inventory-goods-agent";
import { SheBaoSearchNationalCodeSearchPage } from "../inventory-shebao-national-code-search-page-";
import { SheBaoCodeTypeEnum } from "../data/inventory-goods-bean";
import { WesternMedicineConfigProvider } from "../../../outpatient/data/western-medicine-config";
import { WesternMedicine } from "../../../../assets/medicine_usage/western-medicine-config";
import { InventoryUtils } from "../../utils/inventory-utils";
import { PharmacologicSourceType } from "../../inventory-in/inventory-in-medicine-create-view-bloc";
import { SelectPharmacologicalActionFormDialog } from "../views/inventory-goods-pharmacological-action-dialog";
import { TextStyles } from "../../../theme";
import { pxToDp } from "../../../base-ui/utils/ui-utils";
import { showNarcoticsDangerousDrugsReleaseSheet } from "../views/inventory-narcotics-dangerous-drugs-release-dialog";
import { AbcDialog } from "../../../base-ui/abc-app-library";
import { InventoryStorageConditionDialog } from "../views/inventory-storage-condition-dialog";
import { InventoryBusinessScopeDialog } from "../views/inventory-business-scope-dialog";
import { RangePicker } from "../../../base-ui/picker/range-picker";
import { Range } from "../../../base-ui/utils/value-holder";
import { BusinessScopeList } from "../../../base-business/data/clinic-data";
import { InventoryInTagDialog } from "../../inventory-in/inventory-in-views/inventory-in-tag-dialog";
import { InventorySettingMemberPricePage } from "../views/inventory-setting-member-price/inventory-setting-member-price-page";
import { showBottomPanel } from "../../../base-ui/abc-app-library/panel";
import { InventoryGoodsSelectDosageDialog } from "../views/inventory-goods-select-dosage-dialog";

const ORIGIN_LIST = ["进口", "国产"];

export class State extends BaseLoadingState {
    loading = true;
    isUploading = false; // 避免重复点击

    detail: GoodsInventoryInfo = new GoodsInventoryInfo();
    _detail: GoodsInventoryInfo = new GoodsInventoryInfo();

    maxCostInfo?: GoodsInfoMaxCost;

    canEdit = true;

    showErrorHint = false;

    _primaryClassification: GoodsInterfaceClassificationItem[] = [];
    _secondaryClassification: GoodsSecondaryClassificationItem[] = [];

    inOutTaxList?: InOutTaxList[]; // 进销项税列表
    customTypesList: GoodsSecondaryClassificationCustomTypesItem[] = []; // 药品物资 添加药品 选择药品类型的二级分类列表
    feeTypesList: GoodsFeeTypeListItem[] = []; // 费用类型接口
    specifications?: string[]; // 规格
    useUnit?: string; //包装单位

    defaultMedicalInsuranceCodeStr?: string; // 医保对码默认显示文本
    sheBaoCodeSearchGoodsSelectItem?: SheBaoCodeSearchGoodsListItem; // 选中的医保对码项目
    sheBaoCodeSearchGoodsSubType?: number; // 医保对码药品类型（西药 中成药  中药饮片 中药颗粒）

    sheBaoCodeDetail?: GetSheBaoCodeDetailRsp;

    isHospital = userCenter.clinic?.isNormalHospital;

    medicineCategoryItems?: MedicineCategoryItem[]; // 用药助手分类

    priceMode?: SubClinicPricePriceMode; // 定价模式(1 固定售价 3 进价加成)
    // 启用加成配置
    chainReview?: ChainReview;

    //goods配置相关信息--此处关注多药房
    pharmacyInfoConfig?: InventoryClinicConfig;

    isAllWarehouses?: boolean; // 是全部库房（对应PC本地库房汇总）
    // 是否开启调价申请
    isOpenPriceAdjustmentApplication = false;
    adjustPriceInfo?: InventoryGoodsAdjustInfo;

    get canAdjustPrice(): boolean {
        if (!this.adjustPriceInfo || isNil(this.adjustPriceInfo?.status)) {
            return true;
        }
        const status = this.adjustPriceInfo?.status;
        const restrictedStatuses = [
            PriceAdjustmentItemStatus.DRAFT,
            PriceAdjustmentItemStatus.REVIEW,
            PriceAdjustmentItemStatus.GOODS_IN,
        ] as number[];
        return !restrictedStatuses.includes(status);
    }

    list?: GoodsTagTypesItemListItem[];
    // 是进价加成（定价模式）
    get isPurchaseMarkup(): boolean {
        return this.priceMode === SubClinicPricePriceMode.purchaseMarkup && !!this.chainReview?.isSupportPriceMakeUpMode;
    }

    get clinicConfig(): InventoryClinicConfig {
        return userCenter.inventoryClinicConfig ?? new InventoryClinicConfig();
    }

    get covidDictFlagStr(): string {
        if (this.sheBaoCodeSearchGoodsSelectItem?.covidDictFlag == true) {
            return "新冠";
        }
        return "";
    }

    //医保相关的判断
    get medicalFeeGradeStr(): string {
        switch (this.sheBaoCodeSearchGoodsSelectItem?.medicalFeeGrade) {
            case 1:
                return "甲";
            case 2:
                return "乙";
            case 3:
                return "丙";
            case 4:
                return "医保";
            default:
                return "";
        }
    }

    clone(): State {
        return Object.assign(new State(), this);
    }

    get isMedicine(): boolean {
        return this.detail.type == GoodsType.medicine;
    }

    get isGoods(): boolean {
        return this.detail.isGoods;
    }

    //物资
    get isMaterial(): boolean {
        return this.detail.type == GoodsType.material;
    }

    get hasChange(): boolean {
        return !_.isEqual(this.detail, this._detail);
    }

    get primaryClassification(): GoodsInterfaceClassificationItem[] {
        return this._primaryClassification;
    }

    get secondaryClassification(): GoodsSecondaryClassificationItem[] {
        return (
            this.primaryClassification.find(
                (item) =>
                    item.goodsType == this.detail.type &&
                    item.goodsSubType == this.detail.subType &&
                    (this.detail.cMSpec ?? "") == item.goodsCMSpec
            )?.customTypes ?? []
        );
    }

    ///价格相关

    get chainMaxPackagePrice(): number | string {
        if (this.pharmacyInfoConfig?.subClinicPrice?.subSetPrice && !userCenter.clinic?.isChainAdminClinic) {
            const maxPercent = this.pharmacyInfoConfig?.subClinicPrice?.maxPricePercent ?? 0;
            return (maxPercent / 100) * this.detail.chainPackagePrice!;
        }
        return "";
    }

    get chainMinPackagePrice(): number | string {
        if (this.pharmacyInfoConfig?.subClinicPrice?.subSetPrice && !userCenter.clinic?.isChainAdminClinic) {
            const minPercent = this.pharmacyInfoConfig?.subClinicPrice?.minPricePercent ?? 0;
            return (minPercent / 100) * this.detail.chainPackagePrice!;
        }
        return "";
    }

    get chainMaxPiecePrice(): number | string {
        if (this.pharmacyInfoConfig?.subClinicPrice?.subSetPrice && !userCenter.clinic?.isChainAdminClinic) {
            const maxPercent = this.pharmacyInfoConfig?.subClinicPrice?.maxPricePercent ?? 0;
            return (maxPercent / 100) * this.detail.chainPiecePrice!;
        }
        return "";
    }

    get chainMinPiecePrice(): number | string {
        if (this.pharmacyInfoConfig?.subClinicPrice?.subSetPrice && !userCenter.clinic?.isChainAdminClinic) {
            const minPercent = this.pharmacyInfoConfig?.subClinicPrice?.minPricePercent ?? 0;
            return (minPercent / 100) * this.detail.chainPiecePrice!;
        }
        return "";
    }

    get showTax(): boolean {
        return !!(userCenter.clinic?.isChainAdminClinic || userCenter.clinic?.isNormalClinic);
    }

    // 是否允许自主定价
    get showSubSetPrice(): boolean {
        if (!this.pharmacyInfoConfig?.subClinicPrice?.subSetPrice) return false;
        if (this.pharmacyInfoConfig?.subClinicPrice?.subSetPriceAllClinics) return true;
        if (userCenter.clinic?.isChainAdminClinic && this.pharmacyInfoConfig?.subClinicPrice?.subSetPrice) return true;
        return (
            (this.pharmacyInfoConfig?.subClinicPrice?.subSetPriceClinics?.findIndex((item) => {
                return item.clinicId === userCenter.clinic?.clinicId;
            }) ?? -1) !== -1
        );
    }

    get maxPackagePrice(): number | string {
        const currentPackagePrice = this.detail.packagePrice;
        if (this.pharmacyInfoConfig?.subClinicPrice?.subSetPrice && _.isNumber(currentPackagePrice)) {
            return (Number(currentPackagePrice) * (this.pharmacyInfoConfig?.subClinicPrice?.maxPricePercent || 0)) / 100;
        }
        return "";
    }

    get minPackagePrice(): number | string {
        const currentPackagePrice = this.detail.packagePrice;
        if (this.pharmacyInfoConfig?.subClinicPrice?.subSetPrice && _.isNumber(currentPackagePrice)) {
            return (Number(currentPackagePrice) * (this.pharmacyInfoConfig?.subClinicPrice?.minPricePercent || 0)) / 100;
        }
        return "";
    }

    get maxPiecePrice(): number | string {
        const currentDismounting = this.detail.dismounting;
        const subType = this.detail.subType;
        const currentPiecePrice = this.detail.piecePrice;
        if (
            this.pharmacyInfoConfig?.subClinicPrice?.subSetPrice &&
            (currentDismounting || subType === 2) &&
            _.isNumber(currentPiecePrice)
        ) {
            return (Number(currentPiecePrice) * (this.pharmacyInfoConfig?.subClinicPrice?.maxPricePercent || 0)) / 100;
        }
        return "";
    }

    get minPiecePrice(): number | string {
        const currentDismounting = this.detail.dismounting;
        const subType = this.detail.subType;
        const currentPiecePrice = this.detail.piecePrice;
        if (
            this.pharmacyInfoConfig?.subClinicPrice?.subSetPrice &&
            (currentDismounting || subType === 2) &&
            _.isNumber(currentPiecePrice)
        ) {
            return (Number(currentPiecePrice) * (this.pharmacyInfoConfig?.subClinicPrice?.minPricePercent || 0)) / 100;
        }
        return "";
    }

    get packagePriceLabel(): string {
        return GoodsPriceUtils.priceRange(this.maxPackagePrice, this.minPackagePrice);
    }

    get piecePriceLabel(): string {
        return GoodsPriceUtils.priceRange(this.maxPiecePrice, this.minPiecePrice);
    }

    get chainPackagePriceRange(): string {
        return GoodsPriceUtils.priceRange(this.chainMaxPackagePrice, this.chainMinPackagePrice);
    }

    get chainPiecePriceRange(): string {
        return GoodsPriceUtils.priceRange(this.chainMaxPiecePrice, this.chainMinPiecePrice);
    }

    // 针对shebao属地为河北省的定点机构，且有医保对码的药品：售价校验不能为空，不能为0医保价格校验
    private isSheBaoNationalCodeGoods(): boolean {
        // 针对shebao属地为河北省的定点机构
        const heBeiDistraction = userCenter.inventoryClinicConfig?.chainReview?.currentRegionPriceNonZero;
        // 医保对码的药品(不过医保的话，也不需要校验shebaoPayMode)
        const isSheBaoGoods =
            !!this.detail.shebao?.nationalCode &&
            (isNil(this.detail?.shebaoPayMode) || this.detail?.shebaoPayMode != SheBaoPayModeEnum.NO_USE);
        // 固定售价
        const fixedSellingPrice = this.detail?.priceType == SubClinicPricePriceMode.sellingPrice;
        return !!heBeiDistraction && isSheBaoGoods && fixedSellingPrice;
    }

    validatePackageUnitPrice(): { validate: boolean; message?: string } {
        return GoodsPriceUtils.validatePrice(
            this.detail.packagePrice,
            this.chainMaxPackagePrice,
            this.chainMinPackagePrice,
            "整装售价",
            this.isSheBaoNationalCodeGoods()
        );
    }

    validatePriceUnitPrice(): { validate: boolean; message?: string } {
        return GoodsPriceUtils.validatePrice(
            this.detail.piecePrice,
            this.chainMaxPiecePrice,
            this.chainMinPiecePrice,
            "拆零售价",
            this.isSheBaoNationalCodeGoods()
        );
    }

    // 是审批中状态
    get isGspWaitVerifyStatus(): boolean {
        return this.detail.gspModifyStatus === GspModifyStatusEnum.waitVerify;
    }

    //   药品对应的加成率范围
    medicineAdditionRateRange(typeId: number): GoodsPkgCostPriceMakeUpMinMaxItem | undefined {
        return this.pharmacyInfoConfig?.subClinicPrice?.priceModeMinMaxConfig?.find((t) => t.typeId == typeId);
    }
}

class _Event extends BlocEvent {}

class _EventModifyGoodsInfoFeeType extends _Event {}

export class InventoryMedicineBaseInfoPageBloc extends Bloc<_Event, State> {
    static Context = React.createContext<InventoryMedicineBaseInfoPageBloc | undefined>(undefined);
    private _goodsId: string;
    private _typeId: number;

    private _getSheBaoCodeDetailParams?: GetSheBaoCodeDetailParams;
    private _loadGoodInfoTrigger: Subject<number> = new Subject<number>();
    private _loadSheBaoCodeDetailTrigger: Subject<number> = new Subject();

    private _loadCustomTypesTrigger: Subject<number> = new Subject();
    private _updateGoodsInfoTrigger: Subject<GoodsInfo> = new Subject<GoodsInfo>();

    static fromContext(context: InventoryMedicineBaseInfoPageBloc): InventoryMedicineBaseInfoPageBloc {
        return context;
    }

    constructor(options: {
        id: string;
        typeId: number;
        disable?: boolean;
        getSheBaoCodeDetailParams?: GetSheBaoCodeDetailParams;
        medicineCategoryItems?: MedicineCategoryItem[];
    }) {
        super();
        this._goodsId = options.id;
        this._typeId = options.typeId;
        this._getSheBaoCodeDetailParams = options.getSheBaoCodeDetailParams;
        this.innerState.canEdit = !options.disable;
        this.innerState.medicineCategoryItems = options.medicineCategoryItems;
        this.dispatch(new _EventInit()).then();
    }

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventModifyCadn, this._mapEventModifyCadn); // 【基础】通用名

        map.set(_EventModifyTradename, this._mapEventModifyTradename); // 【基础】商品名
        map.set(_EventModifyManufacturerFull, this._mapEventModifyManufacturerFull); //【基础】厂家
        map.set(_EventModifyMedicineNmpn, this._mapEventModifyMedicineNmpn); //【基础】批准文号
        map.set(_EventScanQRCode, this._mapEventScanQRCode); //【基础】扫条码
        map.set(_EventModifyQRCode, this._mapEventModifyQRCode); //【基础】条码
        map.set(_EventModifyDosage, this._mapEventModifyDosage); //【基础】成分含量
        map.set(_EventModifyDosageUnit, this._mapEventModifyDosageUnit); //【基础】成分含量单位
        map.set(_EventModifyPieceNum, this._mapEventModifyPieceNum); // 【基础】小包装数数量
        map.set(_EventModifyPieceUnit, this._mapEventModifyPieceUnit); // 【基础】小包装数单位
        map.set(_EventModifyPackagePrice, this._mapEventModifyPackagePrice); // 【定价】售价（固定售价、进价均展示 进价加成是范围值且只读）
        map.set(_EventModifyPackageUnit, this._mapEventModifyPackageUnit); // 【基础】包装单位
        map.set(_EventModifySecondaryClassification, this._mapEventModifySecondaryClassification); //【基础】二级分类
        map.set(_EventModifyExtendSpec, this._mapEventModifyExtendSpec); // 【基础】中药(规格)
        map.set(_EventModifyEqCoefficient, this._mapEventModifyEqCoefficient); // 换算当量
        map.set(_EventModifyPosition, this._mapEventModifyPosition); // 【扩展】柜号
        map.set(_EventModifySmartDispense, this._mapEventModifySmartDispense); // 智能发药
        map.set(_EventModifyDismountingStatus, this._mapEventModifyDismountingStatus); //【定价】是否拆零
        map.set(_EventModifyPiecePrice, this._mapEventModifyPiecePrice); //【定价】拆零价
        map.set(_EventModifyOutTaxRat, this._mapEventModifyOutTaxRat); //【定价】销项税率
        map.set(_EventModifyInTaxRat, this._mapEventModifyInTaxRat); //【定价】进项税率
        map.set(_EventModifyName, this._mapEventModifyName); //【基础】物资名称
        map.set(_EventModifyOrigin, this._mapEventModifyOrigin); //【基础】进口、国产
        map.set(_EventModifyIsSellStatus, this._mapEventModifyIsSellStatus); // 对外销售
        map.set(_EventContinuePutMedicineInfo, this._mapEventContinuePutMedicineInfo);

        map.set(_EventSaveMedicineInfo, this._mapEventSaveMedicineInfo);

        map.set(_EventModifyMaterialSpec, this._mapEventModifyMaterialSpec); // 【基础】物资、商品(规格)
        map.set(_EventUpdateMedicineShortId, this._mapEventUpdateMedicineShortId); // 【基础】药品编码
        map.set(_EventUpdateMedicineSheBaoCode, this._mapEventUpdateMedicineSheBaoCode); // 【医保】医保对码
        map.set(_EventSelectSpecification, this._mapEventSelectSpecification); // 【基础】规格选择 (容量:成分含量*制剂/包装)
        map.set(_EventUpdateComponentNum, this._mapEventUpdateComponentNum); // 【基础】容量
        map.set(_EventSelectComponentUnit, this._mapEventSelectComponentUnit); // 【基础】容量单位
        map.set(_EventUpdateDosageNum, this._mapEventUpdateDosageNum); // 【基础】成分含量
        map.set(_EventUpdatePieceNum, this._mapEventUpdatePieceNum); // 【基础】制剂
        map.set(_EventSelectPieceUnit, this._mapEventSelectPieceUnit); // 【基础】制剂单位
        map.set(_EventSelectPackageUnit, this._mapEventSelectPackageUnit); // 【基础】包装
        map.set(_EventModifyDefaultInOutTax, this._mapEventModifyDefaultInOutTax); // 【定价】默认进/销项税率

        map.set(_EventSelectPharmacologic, this._mapEventSelectPharmacologic); // 【基础】药理分类（药理作用、科室分类、功效分类）
        map.set(_EventSelectPharmaceuticalForm, this._mapEventSelectPharmaceuticalForm); // 【基础】剂型
        map.set(_EventSelectPriceType, this._mapEventSelectPriceType); // 【定价】-定价模式（收配置控制，允许进价加成时可选）
        map.set(_EventSelectMarkupRate, this._mapEventSelectMarkupRate); // 【定价】加成率（进价加成时展示）
        map.set(_EventCheckPriceModeRange, this._mapEventCheckPriceModeRange);
        map.set(_EventSelectProfitCategorization, this._mapEventSelectProfitCategorization); // 【定价】利润分类
        map.set(_EventSelectMedicalInsurancePayment, this._mapEventSelectMedicalInsurancePayment); // 【医保】选择医保支付

        map.set(_EventSelectOTCForm, this._mapEventSelectOTCForm); // 【扩展】处方OTC
        map.set(_EventSelectNarcoticsDangerousDrugsRelease, this._mapEventSelectNarcoticsDangerousDrugsRelease); // 【扩展】精麻毒放
        map.set(_EventSelectAntibiotics, this._mapEventSelectAntibiotics); // 【扩展】药品-抗菌药物级别
        map.set(_EventUpdateDefinedDailyDose, this._mapEventUpdateDefinedDailyDose); // 【扩展】DDD值
        map.set(_EventSelectBaseMedicineType, this._mapEventSelectBaseMedicineType); // 【扩展】国家基药
        map.set(_EventSelectMedicalMaterialDeviceType, this._mapEventSelectMedicalMaterialDeviceType); // 【扩展】器械耗材分类
        map.set(_EventSelectMaintenanceClassification, this._mapEventSelectMaintenanceClassification); // 【扩展】养护分类
        map.set(_EventSelectStorageConditions, this._mapEventSelectStorageConditions); // 【扩展】存储条件
        map.set(_EventSelectExpirationDate, this._mapEventSelectExpirationDate); // 【扩展】保质期（月）
        map.set(_EventSelectTags, this._mapEventSelectTags); // 【扩展】标签
        map.set(_EventSelectBusinessScope, this._mapEventSelectBusinessScope); // 【扩展】所属经营范围
        map.set(_EventUpdateMark, this._mapEventUpdateMark); // 【扩展】备注
        map.set(_EventSelectNpmnExpiryDate, this._mapEventSelectNpmnExpiryDate); // 批准文号有效期
        map.set(_EventSelectMemberPrice, this._mapEventSelectMemberPrice); // 会员等级
        map.set(_EventUpdateMedicineGoodsTypeId, this._mapEventUpdateMedicineGoodsTypeId); // 药品类型
        map.set(_EventModifyPlaceOfOrigin, this._mapEventModifyPlaceOfOrigin); // 产地
        map.set(_EventUpdatePieceWeight, this._mapEventUpdatePieceWeight); // 单X重量

        return map;
    }

    public update(state?: State): void {
        this.dispatch(new _EventUpdate(state)).then();
    }

    private async _initPageConfig(): Promise<void> {
        const innerState = this.innerState;
        innerState.isAllWarehouses = InventoryAgent.getCurrentPharmacy()?.name == "全部库房";
        innerState.pharmacyInfoConfig = await userCenter.getInventoryChainConfig(false).catchIgnore();
        if (!!this.innerState.pharmacyInfoConfig) {
            innerState.inOutTaxList = innerState.pharmacyInfoConfig?.inOutTaxList;
            this.innerState.priceMode = innerState.pharmacyInfoConfig?.subClinicPrice?.priceMode;
            this.innerState.chainReview = innerState.pharmacyInfoConfig?.chainReview;
        }
        innerState.specifications = ["剂量*制剂/包装", "容量:成分含量*制剂/包装"];
        innerState.detail = JsonMapper.deserialize(GoodsInventoryInfo, {
            defaultInOutTax: innerState.detail.defaultInOutTax ? innerState.detail.defaultInOutTax : 1,
            typeId: this._typeId, // 读取已选择药品子类型缓存
        });
        const goodsClassificationConfig = await GoodsAgent.getGoodsClassification().catchIgnore();
        this.innerState._primaryClassification = goodsClassificationConfig ?? [];
        this.innerState.feeTypesList = ((await GoodsAgent.getGoodsFeeTypeList().catchIgnore()) ?? [])
            .filter((item) => InventoryUtils.checkScopeOptionIsInScope(1, Number(item.scopeId)))
            .filter((item) => !item.disable);
        // 初始化医保对码栏文本显示
        innerState.defaultMedicalInsuranceCodeStr =
            this.innerState.detail.shebao?.nationalCodeId && this.innerState.detail.shebao?.name
                ? this.innerState.detail.shebao?.name
                : this._getSheBaoCodeDetailParams?.codeId && this._getSheBaoCodeDetailParams?._sheBaoName
                ? this._getSheBaoCodeDetailParams?._sheBaoName
                : this._getSheBaoCodeDetailParams?.code ?? "不刷医保/暂无编码";

        //获取当前商品的maxCost价格
        const goodsInfoMaxCost = await GoodsAgent.getGoodsInfoMaxCost(this._goodsId).catchIgnore();
        if (!!goodsInfoMaxCost) innerState.maxCostInfo = goodsInfoMaxCost;
        this.innerState.list = await InventoryGoodsAgent.getGoodsTagTypes().catchIgnore(); // 获取前端定义的标签类型
        // 调价申请
        const approvalSettingList = await userCenter.getApprovalSettingList(false).catchIgnore();
        this.innerState.isOpenPriceAdjustmentApplication = !!approvalSettingList?.find((item) => item.isOpenAdjustmentPrice);
    }
    private _initPageTrigger(): void {
        const innerState = this.innerState;
        this._loadGoodInfoTrigger
            .pipe(
                switchMap(() => {
                    this.innerState.startLoading();
                    this.update();
                    const isWithSupplier = innerState.detail.isMedicalMaterial || innerState.detail.isGoodsHomemade;
                    return GoodsAgent.getGoodsInfo(this._goodsId, {
                        forPurchase: 1,
                        withSupplier: isWithSupplier ? 1 : undefined,
                        pharmacyNo: InventoryAgent.getCurrentPharmacy()?.no,
                    }).catch((e) => new ABCError(e));
                })
            )
            .subscribe(async (rsp) => {
                this.innerState.stopLoading();
                if (rsp instanceof ABCError) {
                    this.innerState.setLoadingError(rsp);
                } else {
                    if (!!rsp?.businessScopeList?.length) {
                        const currentSelectBusinessScope = rsp.businessScopeList?.[0];
                        if (!!currentSelectBusinessScope?.parentId) {
                            const businessScopeName: string[] = [];
                            this._findParentId(
                                currentSelectBusinessScope?.parentId,
                                userCenter.clinicDictionaryInfo?.businessScopeDict?.goodsBusinessScopeList,
                                businessScopeName
                            );
                            if (currentSelectBusinessScope && businessScopeName.length) {
                                currentSelectBusinessScope._displayName =
                                    businessScopeName.join("/") + "/" + currentSelectBusinessScope?.name;
                            }
                        }
                    }
                    const result = rsp;
                    // 避免老数据没有刷新值返回undefined
                    if (!result.eqCoefficient) {
                        result.eqCoefficient = 1;
                    }
                    // 山东济南--中药饮片（配方饮片、非配方饮片）单位为克、g时，默认单X重量为1
                    if (
                        userCenter.shebaoConfig?.isShandongJinan &&
                        (result?.isMedicineChinesePiece || result?.isMedicineChineseNonFormula) &&
                        (result?.pieceUnit == "g" || result?.pieceUnit == "克")
                    ) {
                        result.pieceUnitWeight = !!result.pieceUnitWeight ? result.pieceUnitWeight : 1;
                    }
                    this.innerState.detail = result;
                    this.innerState._detail = _.cloneDeep(result);

                    // 初始化已选标签状态（_disable 是否总部添加 _select 是否已勾选）
                    if (!!innerState.list && !!innerState.detail.goodsTagList) {
                        innerState.list.forEach((listItem) => {
                            if (!!listItem.children) {
                                listItem.children.map((children) => {
                                    innerState.detail.goodsTagList?.forEach((tagListItem) => {
                                        if (children.id === tagListItem.tagId) {
                                            children._select = true;
                                            children._disable = tagListItem.clinicId === undefined;
                                        }
                                    });
                                });
                            }
                        });
                    }

                    if (innerState.detail.typeId && innerState.detail.defaultInOutTax == 1) {
                        const inOutTax = innerState.inOutTaxList?.find((item) => item.typeId == innerState.detail.typeId);
                        innerState.detail.inTaxRat = inOutTax?.inTaxRat ?? 0;
                        innerState.detail.outTaxRat = inOutTax?.outTaxRat ?? 0;
                    }

                    //费用类型转换
                    const feeTypeDetail = this.innerState.feeTypesList.find((item) => item.feeTypeId == rsp.feeTypeId);
                    if (!!feeTypeDetail) {
                        innerState.detail.feeTypeName = feeTypeDetail.name;
                    }
                    // 药店需要获取调价药品详情
                    if (userCenter.clinic?.isDrugstoreButler && !!rsp?.waitingEffectUpdatePriceItemId) {
                        this.innerState.adjustPriceInfo = await GoodsAgent.fetchPriceItemInfo(
                            rsp.waitingEffectUpdatePriceItemId
                        ).catchIgnore();
                    }
                }
                this.update();
            })
            .addToDisposableBag(this);

        this._loadSheBaoCodeDetailTrigger
            .pipe(
                switchMap(() => {
                    return InventoryGoodsAgent.getSheBaoCodeDetail({
                        code: this._getSheBaoCodeDetailParams?.code,
                        goodsType: this._getSheBaoCodeDetailParams?.goodsType,
                        goodsSubType: this._getSheBaoCodeDetailParams?.goodsSubType,
                        shebaoCodeType: this._getSheBaoCodeDetailParams?.shebaoCodeType,
                        codeId: this._getSheBaoCodeDetailParams?.codeId,
                    })
                        .catch((error) => new ABCError(error))
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                if (!(rsp instanceof ABCError)) {
                    innerState.sheBaoCodeDetail = rsp;

                    innerState.sheBaoCodeSearchGoodsSelectItem = JsonMapper.deserialize(SheBaoCodeSearchGoodsListItem, {
                        medicineCadn: innerState.sheBaoCodeDetail?.medicineCadn,
                        medicalFeeGrade: innerState.sheBaoCodeDetail?.medicalFeeGrade,
                        covidDictFlag: innerState.sheBaoCodeDetail?.covidDictFlag,
                        spec: innerState.sheBaoCodeDetail?.spec,
                        manufacturer: innerState.sheBaoCodeDetail?.manufacturer,
                        shebaoCode: innerState.sheBaoCodeDetail?.shebaoCode,
                        standardCode: innerState.sheBaoCodeDetail?.standardCode,
                        approvalCode: innerState.sheBaoCodeDetail?.approvalCode,
                        priceLimit: innerState.sheBaoCodeDetail?.priceLimit,
                        restriction: innerState.sheBaoCodeDetail?.restriction,
                    });
                }

                this.update();
            })
            .addToDisposableBag(this);

        this._loadCustomTypesTrigger
            .pipe(
                switchMap(() => {
                    return InventoryGoodsAgent.getGoodsSecondaryClassificationCustomTypes(this._typeId!)
                        .catch((error) => new ABCError(error))
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                if (!(rsp instanceof ABCError)) {
                    this.innerState.customTypesList = rsp;
                }
                this.update();
            })
            .addToDisposableBag(this);

        this._updateGoodsInfoTrigger
            .pipe(
                switchMap((goodsInfo) => {
                    MedicineBaseInfoAgent.filterMedicinePostData(goodsInfo);
                    if (goodsInfo && goodsInfo.subType === 2) {
                        goodsInfo.materialSpec = goodsInfo.cMSpec;
                    }
                    return MedicineBaseInfoAgent.putGoodsBaseInfo(goodsInfo).catch((e) => new ABCError(e));
                })
            )
            .subscribe((rsp) => {
                if (rsp instanceof ABCError) {
                    if (rsp.detailError.detail.error.code == 12007) {
                        const detail = JsonMapper.deserialize(MedicineSpecChangeDetail, rsp.detailError.detail.error.detail);
                        this.dispatch(new _EventContinuePutMedicineInfo(detail));
                    } else {
                        showConfirmDialog("保存失败", errorToStr(rsp.detailError));
                    }
                } else {
                    //更新成功后重新拉取商品信息 （更新返回的信息不准确）
                    InventoryAgent.medicineRefreshPublisher.next();
                    ABCNavigator.pop();
                }
            })
            .addToDisposableBag(this);
    }

    /**
     * 根据parentId方向查找父级
     * @param parentId
     * @param businessScopeList
     * @param nameList
     * @private
     */
    private _findParentId(parentId?: string, businessScopeList?: BusinessScopeList[], nameList: string[] = []): string[] {
        const parent = businessScopeList?.find((item) => item.id == parentId);
        if (!!parent) {
            nameList.push(parent?.name ?? "");
            if (!!parent?.parentId) {
                this._findParentId(parent.parentId, businessScopeList, nameList);
            }
        }
        return nameList;
    }
    private async *_mapEventInit(/*event: _EventInit*/): AsyncGenerator<State> {
        await this._initPageConfig();
        this._initPageTrigger();
        this._loadGoodInfoTrigger.next();
        this._loadSheBaoCodeDetailTrigger.next();
        if (this._typeId) this._loadCustomTypesTrigger.next();
        this.update();
        // let _type = this._typeId;
        // switch (this._typeId) {
        //     case GoodsTypeId.medicineWest:
        //         _type = 1; // 西药
        //         break;
        //     case GoodsTypeId.medicineChinesePatent:
        //         _type = 2; // 中成药
        //         break;
        //     case GoodsTypeId.medicineChinesePiece:
        //     case GoodsTypeId.medicineChineseGranule:
        //         _type = 3; // 中药
        //         break;
        // }
        //
        // // 药理分类
        // InventoryGoodsAgent.getMedicineCategory()
        //     .toObservable()
        //     .subscribe((rsp) => {
        //         rsp.map((item) => {
        //             if (item.type == _type) {
        //                 innerState.medicineCategoryItems = item.items ?? []; // 拉取指定type类型的用药助手分类 结合_pharmacologicDisplay方法用于前端展示
        //             }
        //         });
        //         this.update();
        //     })
        //     .addToDisposableBag(this);
    }

    private async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
        this.innerState.isUploading = false;
        if (event.state) {
            yield event.state;
            return;
        }
        yield this.innerState.clone();
    }

    private async *_mapEventModifyCadn(/*event: _EventModifyCadn*/): AsyncGenerator<State> {
        const options: MedicineCreateSearchParams = {
            key_word: this.innerState.detail?.medicineCadn,
            client: CreateGoodsClientType.cadn,
        };
        const rsp = await MedicineCreateSearchName.show(options);
        if (rsp) {
            this.innerState.detail!.medicineCadn = rsp.displayText;
        }
        this.update();
    }

    private async *_mapEventModifyTradename(/*event: _EventModifyTradename*/): AsyncGenerator<State> {
        const options: MedicineCreateSearchParams = {
            key_word: this.innerState.detail?.name,
            client: CreateGoodsClientType.tradename,
            cadn: this.innerState.detail?.medicineCadn,
        };
        const rsp = await MedicineCreateSearchName.show(options);
        if (rsp) {
            this.innerState.detail!.name = rsp.displayText;
        }
        this.update();
    }

    private async *_mapEventSelectPharmaceuticalForm(/*ignore: _EventSelectPharmaceuticalForm*/): AsyncGenerator<State> {
        const innerState = this.innerState;
        //  南京、南宁-中药类型-剂型
        const isChineseMedicineDosage =
            (userCenter.shebaoConfig?.isRegionNanjing || userCenter.shebaoConfig?.isRegionNanning) && innerState.detail?.isChineseMedicine;

        const selectId: string = await showBottomPanel(
            <InventoryGoodsSelectDosageDialog
                selectId={innerState.detail.dosageFormType}
                isChineseMedicineDosage={isChineseMedicineDosage}
            />,
            {
                topMaskHeight: pxToDp(315),
            }
        );
        if (!selectId) return;
        innerState.detail.dosageFormType = selectId;

        this.update();
    }

    private async *_mapEventSelectPriceType(/*ignore: _EventSelectPriceType*/): AsyncGenerator<State> {
        const innerState = this.innerState;
        const priceMode = innerState.detail.priceType;
        const initIndex: Set<number> = new Set();
        const pricingModelList = [
            { priceType: SubClinicPricePriceMode.purchaseMarkup, priceTypeName: "进价加成" },
            { priceType: SubClinicPricePriceMode.sellingPrice, priceTypeName: "固定售价" },
        ];
        const initItem = pricingModelList.findIndex((item) => item.priceType == priceMode);
        if (!_.isUndefined(initItem) && initItem > -1) {
            initIndex.add(initItem);
        }
        const selects = await showOptionsBottomSheet({
            title: "定价模式",
            initialSelectIndexes: initIndex,
            options: pricingModelList.map((item) => item.priceTypeName),
            showCloseButton: true,
            showTopRadius: true,
            titlePosition: "center",
            titleStyle: TextStyles.t18MT1,
        });
        if (_.isEmpty(selects)) return;
        const selectedPricingModel = _.first(selects?.map((index) => pricingModelList[index]));

        innerState.detail.priceType = selectedPricingModel?.priceType;
        this.update();
    }

    private async *_mapEventSelectMarkupRate(event: _EventSelectMarkupRate): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.detail.priceMakeupPercent = event.markupRate;

        this.update();
    }

    async *_mapEventCheckPriceModeRange(event: _EventCheckPriceModeRange): AsyncGenerator<State> {
        if (!_.isNil(event?.typeId) && !_.isNil(event.value)) {
            const priceRange = this.innerState.medicineAdditionRateRange(event.typeId);
            if (event.value < (priceRange?.min ?? 0) || event.value > (priceRange?.max ?? 99999)) {
                this.innerState.showErrorHint = true;
            }
        }
        this.update();
    }

    private async *_mapEventSelectOTCForm(/*ignore: _EventSelectOTCForm*/): AsyncGenerator<State> {
        const innerState = this.innerState;

        const initIndex: Set<number> = new Set();
        const otcList = [
            { otcType: OtcTypeEnum.prescription, OtcTypeName: "处方药" },
            { otcType: OtcTypeEnum.CLASS_A_OTC, OtcTypeName: "甲类非处方药" },
            { otcType: OtcTypeEnum.CLASS_B_OTC, OtcTypeName: "乙类非处方药" },
        ];
        const initItem = otcList.findIndex((item) => item.otcType == innerState.detail?.otcType);
        if (!_.isUndefined(initItem) && initItem > -1) {
            initIndex.add(initItem);
        }
        const selects = await showOptionsBottomSheet({
            title: "处方/OTC",
            emptyText: "请选择处方OTC",
            initialSelectIndexes: initIndex,
            options: otcList.map((item) => item.OtcTypeName),
            showCloseButton: true,
            showTopRadius: true,
            titlePosition: "center",
            titleStyle: TextStyles.t18MT1,
        });

        if (_.isEmpty(selects)) return;
        const selectedOtcType = otcList[selects![0]].otcType;

        innerState.detail.otcType = selectedOtcType;
        this.update();
    }

    private async *_mapEventSelectNarcoticsDangerousDrugsRelease(/*ignore: _EventSelectNarcoticsDangerousDrugsRelease*/): AsyncGenerator<State> {
        const innerState = this.innerState;
        const initIndexs: Set<number> = new Set();

        const ingredientList = [
            { label: "精I", ingredient: IngredientEnum.JING_1 },
            { label: "精II", ingredient: IngredientEnum.JING_2 },
            { label: "麻", ingredient: IngredientEnum.MA },
            { label: "毒", ingredient: IngredientEnum.DU },
            { label: "放", ingredient: IngredientEnum.FANG },
            { label: "黄麻碱", ingredient: IngredientEnum.MA_HUANG_JIAN },
        ];

        for (const dangerIngredient of innerState.detail.getIngredientArray ?? []) {
            const initItem = ingredientList.findIndex((item) => item.ingredient == dangerIngredient);
            if (!_.isUndefined(initItem) && initItem > -1) {
                initIndexs.add(initItem);
            }
        }

        const selects = await showNarcoticsDangerousDrugsReleaseSheet({
            title: "选择精麻毒放",
            options: ingredientList?.map((item) => item.label),
            initialSelectIndexes: initIndexs,
            crossAxisCount: 4,
            height: pxToDp(375),
            multiSelect: true,
            showCloseButton: true,
            showConfirmBtn: true,
        });
        if (_.isEmpty(selects)) return;

        const _dangerIngredient: number[] = [];

        // 当选择“精一”后再选择“精二”时，会先删除已选中的“精二”，然后添加新选中的“精二”。同样选择“精一”同理。
        for (const index of selects!) {
            const ingredient = ingredientList[index].ingredient;
            if (ingredient === IngredientEnum.JING_1) {
                // Remove "精二" if it exists
                const jing2Index = _dangerIngredient.indexOf(IngredientEnum.JING_2);
                if (jing2Index > -1) {
                    _dangerIngredient.splice(jing2Index, 1);
                }
            } else if (ingredient === IngredientEnum.JING_2) {
                // Remove "精一" if it exists
                const jing1Index = _dangerIngredient.indexOf(IngredientEnum.JING_1);
                if (jing1Index > -1) {
                    _dangerIngredient.splice(jing1Index, 1);
                }
            }

            _dangerIngredient.push(ingredient);
        }

        innerState.detail.dangerIngredient = innerState.detail.setIngredientArray(_dangerIngredient);

        this.update();
    }

    private async *_mapEventSelectAntibiotics(/*ignore: _EventSelectAntibiotics*/): AsyncGenerator<State> {
        const innerState = this.innerState;

        const initIndex: Set<number> = new Set();
        const antibioticsList = [
            { antibiotic: AntibioticEnum.no, title: "非限制使用级" },
            { antibiotic: AntibioticEnum.yes, title: "限制使用级" },
            { antibiotic: AntibioticEnum.special, title: "特殊使用级" },
        ];
        const initItem = antibioticsList.findIndex((item) => item.antibiotic == innerState.detail.antibiotic);
        if (!_.isUndefined(initItem) && initItem > -1) {
            initIndex.add(initItem);
        }
        const selects = await showOptionsBottomSheet({
            title: "抗菌药物级别",
            emptyText: "选择抗菌级别",
            initialSelectIndexes: initIndex,
            options: antibioticsList?.map((item) => item.title!),
            showCloseButton: true,
            showTopRadius: true,
            titlePosition: "center",
            titleStyle: TextStyles.t18MT1,
            clearOnClick: () => {
                if (innerState.detail.antibiotic !== undefined) {
                    innerState.detail.antibiotic = undefined;
                    ABCNavigator.pop();
                }
                this.update();
            },
            canOnClickClear: !!innerState.detail.antibiotic,
        });
        if (_.isEmpty(selects)) return;
        const selectedAntibiotics = antibioticsList[selects![0]].antibiotic;
        innerState.detail.antibiotic = selectedAntibiotics;

        this.update();
    }

    private async *_mapEventUpdateDefinedDailyDose(event: _EventUpdateDefinedDailyDose): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.detail.dddOfAntibiotic = event.dddOfAntibiotic;
        innerState.detail.unitOfAntibiotic = event.unitOfAntibiotic;

        this.update();
    }

    private async *_mapEventSelectBaseMedicineType(/*ignore: _EventSelectBaseMedicineType*/): AsyncGenerator<State> {
        const innerState = this.innerState;

        const initIndex: Set<number> = new Set();
        const baseMedicineTypeList = [
            { baseMedicineType: BaseMedicineTypeEnum.national, title: "国家基药" },
            { baseMedicineType: BaseMedicineTypeEnum.landmarkBased, title: "地标基药" },
        ];
        const initItem = baseMedicineTypeList.findIndex((item) => item.baseMedicineType == innerState.detail.baseMedicineType);
        if (!_.isUndefined(initItem) && initItem > -1) {
            initIndex.add(initItem);
        }
        const selects = await showOptionsBottomSheet({
            title: "基药",
            emptyText: "选择基药",
            initialSelectIndexes: initIndex,
            options: baseMedicineTypeList?.map((item) => item.title!),
            showCloseButton: true,
            showTopRadius: true,
            titlePosition: "center",
            titleStyle: TextStyles.t18MT1,
            clearOnClick: () => {
                if (innerState.detail.baseMedicineType !== undefined) {
                    innerState.detail.baseMedicineType = undefined;
                }
                ABCNavigator.pop();
                this.update();
            },
            canOnClickClear: !!innerState.detail.baseMedicineType,
        });
        if (_.isEmpty(selects)) return;
        const selectedBaseMedicineType = baseMedicineTypeList[selects![0]].baseMedicineType;
        innerState.detail.baseMedicineType = selectedBaseMedicineType;

        this.update();
    }

    private async *_mapEventSelectMedicalMaterialDeviceType(/*ignore: _EventSelectMedicalMaterialDeviceType*/): AsyncGenerator<State> {
        const innerState = this.innerState;

        const initIndex: Set<number> = new Set();
        const deviceTypeList = [
            { deviceType: MedicalMaterialDeviceType.LEVEL_A, title: "一类器械耗材" },
            { deviceType: MedicalMaterialDeviceType.LEVEL_B, title: "二类器械耗材" },
            { deviceType: MedicalMaterialDeviceType.LEVEL_C, title: "三类器械耗材" },
        ];
        const initItem = deviceTypeList.findIndex((item) => item.deviceType == innerState.detail.deviceType);
        if (!_.isUndefined(initItem) && initItem > -1) {
            initIndex.add(initItem);
        }
        const selects = await showOptionsBottomSheet({
            title: "器械耗材分类",
            emptyText: "选择器械耗材分类",
            initialSelectIndexes: initIndex,
            options: deviceTypeList?.map((item) => item.title!),
            showCloseButton: true,
            showTopRadius: true,
            titlePosition: "center",
            titleStyle: TextStyles.t18MT1,
            clearOnClick: () => {
                if (innerState.detail.deviceType !== undefined) {
                    innerState.detail.deviceType = undefined;
                    ABCNavigator.pop();
                }
                this.update();
            },
            canOnClickClear: !!innerState.detail.deviceType,
        });
        if (_.isEmpty(selects)) return;
        const selectedDeviceType = deviceTypeList[selects![0]].deviceType;
        innerState.detail.deviceType = selectedDeviceType;

        this.update();
    }

    private async *_mapEventSelectMaintenanceClassification(/*ignore: _EventSelectMaintenanceClassification*/): AsyncGenerator<State> {
        const innerState = this.innerState;

        const initIndex: Set<number> = new Set();
        const maintenanceClassificationList = [
            { maintainType: MaintainTypeEnum.NO, maintainTypeName: "无需养护" },
            { maintainType: MaintainTypeEnum.NORMAL, maintainTypeName: "普通养护" },
            { maintainType: MaintainTypeEnum.VIP, maintainTypeName: "重点养护" },
        ];
        const initItem = maintenanceClassificationList.findIndex((item) => item.maintainType == innerState.detail.maintainType);
        if (!_.isUndefined(initItem) && initItem > -1) {
            initIndex.add(initItem);
        }
        const selects = await showOptionsBottomSheet({
            title: "养护分类",
            showCloseButton: true,
            initialSelectIndexes: initIndex,
            options: maintenanceClassificationList.map((item) => item.maintainTypeName),
            showTopRadius: true,
            titlePosition: "center",
            titleStyle: TextStyles.t18MT1,
            clearOnClick: () => {
                if (innerState.detail.maintainType !== undefined) {
                    innerState.detail.maintainType = undefined;
                }
                ABCNavigator.pop();
                this.update();
            },
            canOnClickClear: !!innerState.detail.maintainType,
        });
        if (_.isEmpty(selects)) return;
        const selectedMaintainType = _.first(selects?.map((index) => maintenanceClassificationList[index].maintainType));
        innerState.detail.maintainType = selectedMaintainType;

        this.update();
    }

    private async *_mapEventSelectStorageConditions(/*ignore: _EventSelectStorageConditions*/): AsyncGenerator<State> {
        const innerState = this.innerState;
        const result = await InventoryStorageConditionDialog.show({ storage: innerState.detail.storage });
        if (!result) return;
        innerState.detail.storage = result;
        this.update();
    }

    private async *_mapEventSelectExpirationDate(event: _EventSelectExpirationDate): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.detail.shelfLife = event.monthNum;

        this.update();
    }

    private async *_mapEventSelectTags(/*ignore: _EventSelectTags*/): AsyncGenerator<State> {
        const innerState = this.innerState;
        const selectTags = await InventoryInTagDialog.show({
            selectTagList: innerState.detail.goodsTagList,
            list: innerState.list,
        });
        if (!selectTags) return;
        innerState.detail.goodsTagList = selectTags.selectTagList;
        innerState.detail.goodsTagIdList = (innerState.detail.goodsTagList ?? [])?.map((item) => item.tagId ?? "");
        this.update();
    }

    private async *_mapEventModifyManufacturerFull(event: _EventModifyManufacturerFull): AsyncGenerator<State> {
        const options: MedicineCreateSearchParams = {
            key_word: this.innerState.detail?.manufacturerFull,
            client: CreateGoodsClientType.manufacturer,
            cadn: this.innerState.detail?.medicineCadn,
            tradeName: this.innerState.detail?.name,
        };
        const rsp = await MedicineCreateSearchName.show(options);
        if (rsp) {
            const str = rsp.displayText;
            if (event.type === "manufacturerFull") {
                this.innerState.detail!.manufacturerFull = str;
                this.innerState.detail!.mha = str;
            } else {
                this.innerState.detail!.mha = str;
            }
        }
        this.update();
    }

    private async *_mapEventModifyMedicineNmpn(event: _EventModifyMedicineNmpn): AsyncGenerator<State> {
        this.innerState.detail!.medicineNmpn = event.num;
        this.update();
    }

    private async *_mapEventScanQRCode(): AsyncGenerator<State> {
        const barcode = await BarcodeScanner.scan();
        if (barcode && barcode.length) {
            this.innerState.detail!.barCode = barcode;
        }
        this.update();
    }

    private async *_mapEventModifyQRCode(event: _EventModifyQRCode): AsyncGenerator<State> {
        this.innerState.detail!.barCode = event.QrCode;
        this.update();
    }

    private async *_mapEventModifyDosage(event: _EventModifyDosage): AsyncGenerator<State> {
        this.innerState.detail!.medicineDosageNum = event.num;
        this.update();
    }

    private async *_mapEventModifyDosageUnit(event: _EventModifyDosageUnit): AsyncGenerator<State> {
        this.innerState.detail!.medicineDosageUnit = event.unit;
        this.update();
    }

    private async *_mapEventModifyPieceNum(event: _EventModifyPieceNum): AsyncGenerator<State> {
        this.innerState.detail!.pieceNum = event.num;
        this.innerState.detail.piecePrice = Number(ABCUtils.formatPrice(this.innerState.detail.packagePrice! / (event.num ?? 1)));
        this.update();
    }

    private async *_mapEventModifyPieceUnit(event: _EventModifyPieceUnit): AsyncGenerator<State> {
        this.innerState.detail!.pieceUnit = event.unit;
        // 山东济南-中药饮片（配方饮片、非配方饮片），如果pieceUnit为g、克，pieceUnitWeight默认为1
        if (
            userCenter.shebaoConfig?.isShandongJinan &&
            (this.innerState.detail?.isMedicineChinesePiece || this.innerState.detail?.isMedicineChineseNonFormula) &&
            ["g", "克"].includes(this.innerState.detail?.pieceUnit)
        ) {
            this.innerState.detail.pieceUnitWeight = !!this.innerState.detail?.pieceUnitWeight
                ? this.innerState.detail?.pieceUnitWeight
                : 1;
        }
        this.update();
    }

    private async *_mapEventModifyPackagePrice(event: _EventModifyPackagePrice): AsyncGenerator<State> {
        this.innerState.detail.packagePrice = event.num;
        //自动修改拆零价格
        if (_.isNumber(this.innerState.detail.pieceNum) && this.innerState.detail.pieceNum != 0 && _.isNumber(event.num)) {
            this.dispatch(new _EventModifyPiecePrice(Number(ABCUtils.formatPrice(event.num / this.innerState.detail.pieceNum))));
        }
        this.update();
    }

    private async *_mapEventModifyPackageUnit(event: _EventModifyPackageUnit): AsyncGenerator<State> {
        this.innerState.detail.packageUnit = event.unit;
        this.update();
    }

    // 单X重量必填规则：开通医保 & 该饮片有医保对码 & 结算方式非不过医保
    private isNeedVerifyPieceUnitWeight(): boolean {
        const goodsInfo = this.innerState.detail;
        // 开通医保
        const openSheBao = userCenter.shebaoConfig?.isOpenSocial;
        // 地区限制：山东济南
        const shandongJinan = userCenter.shebaoConfig?.isShandongJinan;
        // 中药饮片（配方饮片、非配方饮片）
        const isChinesePiece = goodsInfo.isMedicineChinesePiece || goodsInfo.isMedicineChineseNonFormula;
        // 医保对码的药品(如果shebaoPayMode不存在就不校验，如果存在则需要校验是否不过医保)
        const isSheBaoGoods =
            !!goodsInfo?.shebao?.nationalCode && (isNil(goodsInfo?.shebaoPayMode) || goodsInfo?.shebaoPayMode != SheBaoPayModeEnum.NO_USE);
        return !!(openSheBao && shandongJinan && isChinesePiece && isSheBaoGoods);
    }

    private async *_mapEventSaveMedicineInfo(/*event: _EventSaveMedicineInfo*/): AsyncGenerator<State> {
        const state = this.innerState,
            goodsInfo = this.innerState.detail;
        if (state.isUploading) return;
        // 进价加成--加成率不能为空
        if (goodsInfo?.priceType == SubClinicPricePriceMode.purchaseMarkup && !_.isNil(goodsInfo.typeId)) {
            const priceMakeupPercent = goodsInfo.priceMakeupPercent,
                priceRange = state.medicineAdditionRateRange(goodsInfo.typeId);
            if (_.isNil(priceMakeupPercent)) {
                this.innerState.showErrorHint = true;
                this.update();
                return Toast.show("加成率不能为空", { warning: true });
            } else if (priceMakeupPercent < (priceRange?.min ?? 0) || priceMakeupPercent > (priceRange?.max ?? 99999)) {
                this.innerState.showErrorHint = true;
                this.update();
                return Toast.show("加成率不在总部限价范围内", { warning: true });
            }
        }
        //校验数据是否完整
        if (state.isMedicine && (!goodsInfo.medicineCadn || !goodsInfo.medicineCadn.length)) {
            this.innerState.showErrorHint = true;
            this.update();
            return Toast.show("通用名为空", { warning: true });
        } else {
            goodsInfo.medicineCadn = goodsInfo.medicineCadn ?? "";
        }
        if (goodsInfo.isChineseMedicine) {
        } else {
            if (!goodsInfo.pieceNum) {
                this.innerState.showErrorHint = true;
                this.update();
                return Toast.show("制剂不能为空", { warning: true });
            }
            if (!goodsInfo.pieceUnit) {
                this.innerState.showErrorHint = true;
                this.update();
                return Toast.show("制剂单位不能为空", { warning: true });
            }
            if (!goodsInfo.packageUnit) {
                this.innerState.showErrorHint = true;
                this.update();
                return Toast.show("包装单位不能为空", { warning: true });
            }
        }

        if (this.isNeedVerifyPieceUnitWeight() && isNil(goodsInfo?.pieceUnitWeight)) {
            this.update();
            return Toast.show(`单${goodsInfo?.pieceUnit}重量不能为空`, { warning: true });
        }

        let validateInfo;
        if (state.isMaterial && goodsInfo.isMedicalMaterial && !!goodsInfo.isSell) {
            //物资中的器械耗材才进行校验价格
            validateInfo = this.innerState.validatePackageUnitPrice();
            if ((!validateInfo || validateInfo.validate) && goodsInfo.dismounting) {
                validateInfo = this.innerState.validatePriceUnitPrice();
            }

            if (!validateInfo.validate) {
                this.innerState.showErrorHint = true;
                Toast.show(validateInfo.message!, { warning: true });
                return this.update();
            }
        }

        if (state.isMedicine) {
            // 中药没有整装售价
            validateInfo = !goodsInfo.isChineseMedicine ? this.innerState.validatePackageUnitPrice() : undefined;
            if ((!validateInfo || validateInfo.validate) && goodsInfo.dismounting) {
                validateInfo = this.innerState.validatePriceUnitPrice();
            }

            if (!!validateInfo && !validateInfo.validate) {
                this.innerState.showErrorHint = true;
                Toast.show(validateInfo.message!, { warning: true });
                return this.update();
            }
        }

        if (state.isGoods) {
            validateInfo = this.innerState.validatePackageUnitPrice();
            if ((!validateInfo || validateInfo.validate) && goodsInfo.dismounting) {
                validateInfo = this.innerState.validatePriceUnitPrice();
            }

            if (!validateInfo.validate) {
                this.innerState.showErrorHint = true;
                Toast.show(validateInfo.message!, { warning: true });
                return this.update();
            }
        }

        //校验价格是否符合
        const validatePriceResult = GoodsPriceUtils.validatePriceWithCost(goodsInfo, this.innerState.maxCostInfo);
        if (!validatePriceResult.validate) {
            const result = await showQueryDialog("确认修改？", validatePriceResult.message!);
            if (result == DialogIndex.positive) {
                this._updateGoodsInfoTrigger.next(this.innerState.detail);
            }
        } else {
            state.isUploading = true;
            this._updateGoodsInfoTrigger.next(this.innerState.detail);
        }
    }

    private async *_mapEventModifySecondaryClassification(/*event: _EventModifySecondaryClassification*/): AsyncGenerator<State> {
        const state = this.innerState;
        const initIndex: Set<number> = new Set();
        const initItem = state.customTypesList.findIndex((item) => item.id == state.detail.customTypeId);
        if (!_.isUndefined(initItem) && initItem > -1) {
            initIndex.add(initItem);
        }
        const result = await showOptionsBottomSheet({
            title: "请选择二级分类",
            emptyText: "未设置二级分类",
            initialSelectIndexes: initIndex,
            options: state.customTypesList?.map((item) => item.name!),
            showCloseButton: true,
            showTopRadius: true,
            titlePosition: "center",
            titleStyle: TextStyles.t18MT1,
            clearOnClick: () => {
                if (state.detail.customTypeId !== undefined) {
                    state.detail.customTypeId = undefined;
                    state.detail.customTypeName = undefined;
                }
                ABCNavigator.pop();
                this.update();
            },
            canOnClickClear: !!state.detail.customTypeId,
        });
        if (result && result.length) {
            state.detail.customTypeId = Number(state.customTypesList[result[0]].id);
            state.detail.customTypeName = state.customTypesList[result[0]].name;
            this.update();
        }
    }

    // 用药助手(1 西药-药理作用分类  2 中成药-科室分类  3 中药-功效分类
    private async *_mapEventSelectPharmacologic(event: _EventSelectPharmacologic): AsyncGenerator<State> {
        const goodsInfo = this.innerState.detail;
        const selects = await SelectPharmacologicalActionFormDialog.show({ type: event.type, pharmacologicId: goodsInfo.pharmacologicId });
        if (_.isEmpty(selects)) return;
        goodsInfo.pharmacologicId = selects.subTypeId;
        goodsInfo.pharmacologicsName = `${selects.sourceTypeName}/${selects.subTypeName}`;

        this.update();
    }

    private async *_mapEventModifyEqCoefficient(event: _EventModifyEqCoefficient): AsyncGenerator<State> {
        this.innerState.detail.eqCoefficient = event.eqCoefficient;
        this.update();
    }

    private async *_mapEventModifyExtendSpec(event: _EventModifyExtendSpec): AsyncGenerator<State> {
        this.innerState.detail.extendSpec = event.extendSpec;
        this.update();
    }

    private async *_mapEventModifyPosition(event: _EventModifyPosition): AsyncGenerator<State> {
        this.innerState.detail.position = event.position;
        this.update();
    }

    private async *_mapEventModifySmartDispense(event: _EventModifySmartDispense): AsyncGenerator<State> {
        this.innerState.detail.smartDispense = event.status ? 1 : 0;
        this.update();
    }

    private async *_mapEventModifyDismountingStatus(event: _EventModifyDismountingStatus): AsyncGenerator<State> {
        this.innerState.detail.dismounting = event.status ? 1 : 0;
        const goodsInfo = this.innerState.detail;
        if (_.isNumber(goodsInfo.piecePrice)) {
            goodsInfo.piecePrice = this.innerState._detail.piecePrice;
        }
        this.update();
    }

    private async *_mapEventModifyIsSellStatus(event: _EventModifyIsSellStatus): AsyncGenerator<State> {
        const goodsInfo = this.innerState.detail;
        this.innerState.detail.isSell = event.status ? 1 : 0;
        if (goodsInfo.isSell == 0) goodsInfo.dismounting = this.innerState.detail.isSell; // 当不允许对外销售时需将是否拆零也设为不允许
        goodsInfo.resetPrice();
        this.update();
    }

    private async *_mapEventModifyPiecePrice(event: _EventModifyPiecePrice): AsyncGenerator<State> {
        this.innerState.detail.piecePrice = event.num;
        this.update();
    }

    public requestModifyGoodsInfoFeeType(): void {
        this.dispatch(new _EventModifyGoodsInfoFeeType());
    }

    private async *_mapEventModifyOutTaxRat(event: _EventModifyOutTaxRat): AsyncGenerator<State> {
        this.innerState.detail.outTaxRat = event.num;
        this.update();
    }

    private async *_mapEventModifyInTaxRat(event: _EventModifyInTaxRat): AsyncGenerator<State> {
        this.innerState.detail.inTaxRat = event.num;
        this.update();
    }

    // 暂时注释掉这个方法，这个profitCategoryType的类型变化了，这个方法也没有调用，所以没有影响
    private async *_mapEventSelectProfitCategorization(/*event: _EventSelectProfitCategorization*/): AsyncGenerator<State> {
        // const innerState = this.innerState;
        //
        // const initIndex: Set<number> = new Set();
        // // 毛利类型列表
        // const storageConditionsList = [
        //     { profitCategoryType: ProfitCategoryTypeEnum.high, title: "异常高毛利", content: "120%以上（含）" },
        //     { profitCategoryType: ProfitCategoryTypeEnum.leve_A, title: "A类", content: "90%以上（含）~ 120% " },
        //     { profitCategoryType: ProfitCategoryTypeEnum.leve_B, title: "B类", content: "60%以上（含）~ 90%" },
        //     { profitCategoryType: ProfitCategoryTypeEnum.leve_C, title: "C类", content: "30%以上（含）~ 60%" },
        //     { profitCategoryType: ProfitCategoryTypeEnum.leve_D, title: "D类", content: "30%以下" },
        //     { profitCategoryType: ProfitCategoryTypeEnum.leve_E, title: "E类", content: "0%以下" },
        // ];
        //
        // const initItem = storageConditionsList.findIndex((item) => item.profitCategoryType == innerState.detail.profitCategoryType);
        // if (!_.isUndefined(initItem) && initItem > -1) {
        //     initIndex.add(initItem);
        // }
        // const selects = await showOptionsBottomSheet({
        //     title: "利润分类",
        //     titlePosition: "center",
        //     showTopRadius: true,
        //     showCloseButton: true,
        //     titleStyle: TextStyles.t18MT1,
        //     options: storageConditionsList.map((item) => item.title),
        //     optionsWidgets: storageConditionsList.map((item, index) => (
        //         <AbcView
        //             key={index}
        //             style={[
        //                 ABCStyles.rowAlignCenter,
        //                 {
        //                     flex: 1,
        //                     paddingVertical: Sizes.dp16,
        //                     paddingHorizontal: Sizes.dp24,
        //                     backgroundColor: Colors.S2,
        //                 },
        //             ]}
        //         >
        //             <Text style={[TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp22 }), { textAlign: "left" }]} numberOfLines={1}>
        //                 {item.title ?? ""}
        //             </Text>
        //             <Text
        //                 style={[
        //                     TextStyles.t16NT3.copyWith({ lineHeight: Sizes.dp22, color: Colors.t3 }),
        //                     { textAlign: "left", marginLeft: Sizes.dp9 },
        //                 ]}
        //                 numberOfLines={1}
        //             >
        //                 {item.content ?? ""}
        //             </Text>
        //         </AbcView>
        //     )),
        //     initialSelectIndexes: initIndex,
        // });
        // if (_.isEmpty(selects)) return;
        // const selectedProfitCategoryType = _.first(selects?.map((index) => storageConditionsList[index].profitCategoryType));
        // innerState.detail.profitCategoryType = selectedProfitCategoryType;

        this.update();
    }

    private async *_mapEventModifyName(event: _EventModifyName): AsyncGenerator<State> {
        this.innerState.detail.name = event.name;
        this.update();
    }
    /**
     * 根据商品类型修改默认费用类别
     */
    _goodsTypeToDefaultType(): void {
        const { feeTypesList, detail } = this.innerState;
        let defaultFeeTypeName = "";
        // 获取默认费用类型
        if (detail.isWesternMedicine) {
            defaultFeeTypeName = "西药费";
        } else if (detail.isMedicineChinesePiece) {
            defaultFeeTypeName = "中药费";
        } else if (detail.isMedicineChineseGranule) {
            defaultFeeTypeName = "中药费";
        } else if (detail.isChineseWesternMedicine) {
            defaultFeeTypeName = "中成药费";
        } else if (detail.isMedicalMaterial || detail.isMaterial || detail.isGoods) {
            defaultFeeTypeName = "卫生材料费";
        }
        const defaultFeeType = feeTypesList.find((item) => item.name === defaultFeeTypeName);
        if (!!defaultFeeType) {
            detail.feeTypeId = defaultFeeType.feeTypeId;
            detail.feeTypeName = defaultFeeType.name;
        }
        this.update();
    }
    private _replaceChineseHerbalSlice(item: any, parentName?: string): void {
        // 替换当前节点
        ["name", "displayName", "_displayName"].forEach((key) => {
            if (item[key]?.includes("中药饮片")) {
                item[key] = item[key].replace(/中药饮片/g, "配方饮片");
            }
        });

        // 拼接 _displayName（如果有父级）
        if (parentName) {
            item._displayName = `${parentName}/${item.name}`;
        } else {
            item._displayName = item.name;
        }

        // 递归处理所有子节点
        if (Array.isArray(item.children)) {
            item.children.forEach((child: any) => {
                this._replaceChineseHerbalSlice(child, item.name);
            });
        }
    }
    /**
     * 拼接父级所属经营范围的名称
     */
    private _combineBusinessScopeName(businessScopeList?: BusinessScopeList[]): void {
        if (userCenter.clinic?.isDrugstoreButler) {
            businessScopeList?.forEach((item) => {
                this._replaceChineseHerbalSlice(item);
            });
        } else {
            businessScopeList?.forEach((item) => {
                item._displayName = item.name;
                item.children?.forEach((child: BusinessScopeList) => {
                    child._displayName = `${item.name}/${child.name}`;
                });
            });
        }
    }
    private initBusinessScopeList(): void {
        const innerState = this.innerState;
        const goodsInfo = innerState.detail;
        // 药店-如果选择了配方饮片，那么所属经营范围默认选中【配方饮片/含配方】，如果选择了非配方饮片，默认选中【配方饮片/不含配方】
        if (userCenter.clinic?.isDrugstoreButler) {
            const businessScopeList = userCenter.clinicDictionaryInfo?.businessScopeDict?.goodsBusinessScopeList;
            this._combineBusinessScopeName(businessScopeList);
            if (goodsInfo.typeId == GoodsTypeId.medicineChinesePiece) {
                const parent = businessScopeList?.find((t) => t.id == "4");
                goodsInfo.businessScopeList = parent?.children?.filter((k) => k.id === "4000");
                // 配方饮片默认选择处方OTC为【处方药】
                goodsInfo.otcType = OtcTypeEnum.prescription;
            } else if (goodsInfo.typeId == GoodsTypeId.medicineChineseNonFormula) {
                const parent = businessScopeList?.find((t) => t.id == "4");
                goodsInfo.businessScopeList = parent?.children?.filter((k) => k.id === "4001");
                goodsInfo.otcType = undefined;
            } else {
                goodsInfo.businessScopeList = undefined;
                goodsInfo.otcType = undefined;
            }
        }
    }
    private async *_mapEventUpdateMedicineGoodsTypeId(): AsyncGenerator<State> {
        const innerState = this.innerState;
        const goodsInfo = innerState.detail;
        const medicineTypeList = [
            {
                name: "配方饮片",
                subType: GoodsSubType.medicineChinese,
                typeId: GoodsTypeId.medicineChinesePiece,
            },
            {
                name: "非配方饮片",
                subType: GoodsSubType.medicineChinese,
                typeId: GoodsTypeId.medicineChineseNonFormula,
            },
        ];
        const initIndex = medicineTypeList?.findIndex((t) => t.name == goodsInfo?.displayTypeName) ?? -1;
        const selects = await showOptionsBottomSheet({
            title: "请选择药品类型",
            options: medicineTypeList?.map((item) => item.name),
            showCloseButton: true,
            showTopRadius: true,
            titlePosition: "center",
            titleStyle: TextStyles.t18MT1,
            initialSelectIndexes: new Set<number>([initIndex]),
        });
        if (_.isEmpty(selects)) return;
        if (!!selects?.length) {
            goodsInfo.subType = medicineTypeList[selects[0]]?.subType;
            goodsInfo.typeId = medicineTypeList[selects[0]]?.typeId;
            this._typeId = goodsInfo.typeId;
        }
        if (
            !(
                goodsInfo.typeId == GoodsTypeId.medicineChinesePiece ||
                goodsInfo.typeId == GoodsTypeId.medicineChineseGranule ||
                goodsInfo.typeId == GoodsTypeId.medicineChineseNonFormula
            )
        ) {
            goodsInfo.pieceUnit = undefined; // 选择非中药类型清除计价单位
        } else if (
            (goodsInfo.typeId == GoodsTypeId.medicineChinesePiece || goodsInfo.typeId == GoodsTypeId.medicineChineseGranule) &&
            !goodsInfo.pieceUnit
        ) {
            goodsInfo.pieceUnit = "g"; // 中药类型计价单位默认单位为g
        }

        // 山东济南--中药饮片（配方饮片、非配方饮片）单位为克、g时，默认单X重量为1
        if (
            userCenter.shebaoConfig?.isShandongJinan &&
            (goodsInfo?.isMedicineChinesePiece || goodsInfo?.isMedicineChineseNonFormula) &&
            ["g", "克"].includes(goodsInfo?.pieceUnit ?? "")
        ) {
            goodsInfo.pieceUnitWeight = !!goodsInfo.pieceUnitWeight ? goodsInfo.pieceUnitWeight : 1;
        } else {
            goodsInfo.pieceUnitWeight = undefined;
        }

        this._goodsTypeToDefaultType();
        this.initBusinessScopeList();

        if (goodsInfo.typeId) this._loadCustomTypesTrigger.next();

        if (goodsInfo.defaultInOutTax == 1) {
            const inOutTax = innerState.inOutTaxList?.find((item) => item.typeId == goodsInfo.typeId);
            goodsInfo.inTaxRat = inOutTax?.inTaxRat ?? 0;
            goodsInfo.outTaxRat = inOutTax?.outTaxRat ?? 0;
        }
        this.update();
    }

    private async *_mapEventModifyMaterialSpec(event: _EventModifyMaterialSpec): AsyncGenerator<State> {
        this.innerState.detail.materialSpec = event.materialSpec;
        this.update();
    }

    private async *_mapEventContinuePutMedicineInfo(event: _EventContinuePutMedicineInfo): AsyncGenerator<State> {
        const result = await MedicineSpecChangeDialog.show(event.detail);
        if (result == DialogIndex.positive) {
            this._updateGoodsInfoTrigger.next(_.assign(this.innerState.detail, { force: true }));
        }
    }

    private async *_mapEventModifyOrigin(): AsyncGenerator<State> {
        const state = this.innerState;
        const initIndex: Set<number> = new Set();
        const initItem = ORIGIN_LIST.findIndex((item) => item == state.detail.origin);
        if (!_.isUndefined(initItem) && initItem > -1) {
            initIndex.add(initItem);
        }
        const result = await showOptionsBottomSheet({
            title: "选择来源",
            options: ORIGIN_LIST,
            initialSelectIndexes: initIndex,
        });

        if (result && result.length) {
            state.detail.origin = ORIGIN_LIST[result[0]];
            this.update();
        }
    }

    private async *_mapEventModifyPlaceOfOrigin(event: _EventModifyPlaceOfOrigin): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.detail.origin = event.placeOrigin;
        this.update();
    }

    private async *_mapEventUpdatePieceWeight(event: _EventUpdatePieceWeight): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.detail.pieceUnitWeight = event.value;
        this.update();
    }

    // 选择所属经营范围
    requestSelectBusinessScope(): void {
        this.dispatch(new _EventSelectBusinessScope());
    }
    private async *_mapEventSelectBusinessScope(): AsyncGenerator<State> {
        const selects = await InventoryBusinessScopeDialog.show({ businessScopeItem: this.innerState.detail.businessScopeList });
        if (!selects?.length) return;
        this.innerState.detail.businessScopeList = selects;
        this.update();
    }
    private async *_mapEventUpdateMark(event: _EventUpdateMark): AsyncGenerator<State> {
        this.innerState.detail.remark = event.mark;
        this.update();
    }
    private async *_mapEventSelectNpmnExpiryDate(): AsyncGenerator<State> {
        const innerState = this.innerState;
        const initRange = new Range<Date>(
                innerState.detail?.medicineNmpnStartExpiryDate ?? new Date(),
                innerState.detail?.medicineNmpnEndExpiryDate
            ),
            maxRange = new Range<Date>(undefined, undefined);
        const timeRange = await RangePicker.show(initRange, maxRange);
        if (!timeRange) return;
        innerState.detail.medicineNmpnStartExpiryDate = timeRange.start;
        innerState.detail.medicineNmpnEndExpiryDate = timeRange.end;
        this.update();
    }

    public requestModifyCadn(): void {
        this.dispatch(new _EventModifyCadn());
    }

    // 商品名
    public requestModifyTradename(): void {
        this.dispatch(new _EventModifyTradename());
    }

    // 剂型
    public requestSelectPharmaceuticalForm(): void {
        this.dispatch(new _EventSelectPharmaceuticalForm());
    }

    // 定价模式
    public requestSelectPriceType(): void {
        this.dispatch(new _EventSelectPriceType());
    }

    // 加成率
    public requestSelectMarkupRate(markupRate: number): void {
        this.dispatch(new _EventSelectMarkupRate(markupRate));
    }
    requestCheckPriceModeRange(typeId?: number, value?: number): void {
        this.dispatch(new _EventCheckPriceModeRange(typeId, value));
    }

    // 处方OTC
    public requestSelectOTCForm(): void {
        this.dispatch(new _EventSelectOTCForm());
    }

    // 精麻毒放
    public requestSelectNarcoticsDangerousDrugsRelease(): void {
        this.dispatch(new _EventSelectNarcoticsDangerousDrugsRelease());
    }

    // 抗菌药物
    public requestSelectAntibiotics(): void {
        this.dispatch(new _EventSelectAntibiotics());
    }

    // DDD值
    public requestUpdateDefinedDailyDose(options: { dddOfAntibiotic?: number; unitOfAntibiotic?: string }): void {
        const { dddOfAntibiotic, unitOfAntibiotic } = options;
        this.dispatch(new _EventUpdateDefinedDailyDose(dddOfAntibiotic, unitOfAntibiotic));
    }

    // 国家基药
    public requestSelectBaseMedicineType(): void {
        this.dispatch(new _EventSelectBaseMedicineType());
    }

    // 器械耗材分类
    public requestSelectMedicalMaterialDeviceType(): void {
        this.dispatch(new _EventSelectMedicalMaterialDeviceType());
    }

    // 养护分类
    public requestSelectMaintenanceClassification(): void {
        this.dispatch(new _EventSelectMaintenanceClassification());
    }

    // 存储条件
    public requestSelectStorageConditions(): void {
        this.dispatch(new _EventSelectStorageConditions());
    }

    // 保质期（月）
    public requestSelectExpirationDate(monthNum: number): void {
        this.dispatch(new _EventSelectExpirationDate(monthNum));
    }

    // 标签
    public requestSelectTag(): void {
        this.dispatch(new _EventSelectTags());
    }

    // 厂家
    public requestModifyManufacturerFull(type: "manufacturerFull" | "mha"): void {
        this.dispatch(new _EventModifyManufacturerFull(type));
    }

    // 批准文号
    public requestModifyMedicineNmpn(value: string): void {
        this.dispatch(new _EventModifyMedicineNmpn(value));
    }

    // 搜索条码
    public requestScanQRCode(): void {
        this.dispatch(new _EventScanQRCode());
    }

    // 条码
    public requestModifyQRCode(value: string): void {
        this.dispatch(new _EventModifyQRCode(value));
    }

    // 剂量
    public requestModifyDosage(text: string): void {
        this.dispatch(new _EventModifyDosage(text.length ? Number(text) : undefined));
    }

    public requestModifyDosageUnit(unit: string): void {
        this.dispatch(new _EventModifyDosageUnit(unit));
    }

    public requestModifyPieceNum(text: string): void {
        this.dispatch(new _EventModifyPieceNum(text.length ? Number(text) : undefined));
    }

    public requestModifyPackagePrice(num: string): void {
        this.dispatch(new _EventModifyPackagePrice(num.length ? Number(num) : undefined));
    }

    public requestModifyPieceUnit(unit: string): void {
        this.dispatch(new _EventModifyPieceUnit(unit));
    }

    public requestModifyPackageUnit(unit: string): void {
        this.dispatch(new _EventModifyPackageUnit(unit));
    }

    public requestSaveMedicineInfo(): void {
        this.dispatch(new _EventSaveMedicineInfo());
    }

    // 二级分类
    public requestModifySecondaryClassification(): void {
        this.dispatch(new _EventModifySecondaryClassification());
    }

    // 药理作用(1 西药-药理作用分类  2 中成药-科室分类  3 中药-功效分类
    public requestSelectPharmacologic(type: PharmacologicSourceType): void {
        this.dispatch(new _EventSelectPharmacologic(type));
    }

    public requestModifyExtendSpec(text?: string): void {
        this.dispatch(new _EventModifyExtendSpec(text));
    }

    public requestModifyEqCoefficient(value?: number): void {
        this.dispatch(new _EventModifyEqCoefficient(value));
    }

    // 柜号
    public requestModifyPosition(text?: string): void {
        this.dispatch(new _EventModifyPosition(text));
    }

    public requestModifySmartDispense(status?: boolean): void {
        this.dispatch(new _EventModifySmartDispense(status));
    }

    public requestModifyDismountingStatus(status?: boolean): void {
        this.dispatch(new _EventModifyDismountingStatus(status));
    }

    public requestModifyPiecePrice(num: string): void {
        this.dispatch(new _EventModifyPiecePrice(num.length ? Number(num) : undefined));
    }
    requestUpdateMark(text?: string): void {
        this.dispatch(new _EventUpdateMark(text));
    }
    requestSelectNpmnExpiryDate(): void {
        this.dispatch(new _EventSelectNpmnExpiryDate());
    }

    // 产地
    requestModifyPlaceOfOrigin(placeOrigin: string): void {
        this.dispatch(new _EventModifyPlaceOfOrigin(placeOrigin));
    }

    // 单X重量
    requestUpdatePieceWeight(value: number): void {
        this.dispatch(new _EventUpdatePieceWeight(value));
    }

    @actionEvent(_EventModifyGoodsInfoFeeType)
    private async *_mapEventModifyGoodsInfoFeeType(): AsyncGenerator<State> {
        const state = this.innerState;
        const initIndex: Set<number> = new Set();
        const initItem = state.feeTypesList.findIndex((item) => item.feeTypeId == state.detail.feeTypeId);
        if (!_.isUndefined(initItem) && initItem > -1) {
            initIndex.add(initItem);
        }
        const result = await showOptionsBottomSheet({
            title: "请选择费用类型",
            emptyText: "未设置费用类型",
            initialSelectIndexes: initIndex,
            options: state.feeTypesList?.map((item) => item.name!),
        });
        if (result && result.length) {
            state.detail.feeTypeId = state.feeTypesList[result[0]].feeTypeId;
            state.detail.feeTypeName = state.feeTypesList[result[0]].name;
            this.update();
        }
    }

    public requestModifyInTaxRat(num: string): void {
        this.dispatch(new _EventModifyInTaxRat(num.length ? Number(num) : undefined));
    }

    public requestModifyOutTaxRat(num: string): void {
        this.dispatch(new _EventModifyOutTaxRat(num.length ? Number(num) : undefined));
    }

    // 利润分类
    public requestSelectProfitCategorization(): void {
        this.dispatch(new _EventSelectProfitCategorization());
    }

    // 物资名称
    public requestModifyName(text: string): void {
        this.dispatch(new _EventModifyName(text));
    }
    requestUpdateMedicineGoodsTypeId(): void {
        this.dispatch(new _EventUpdateMedicineGoodsTypeId());
    }

    // 进口/国产
    public requestModifyOrigin(): void {
        this.dispatch(new _EventModifyOrigin());
    }

    public requestModifyMaterialSpec(materialSpec: string): void {
        this.dispatch(new _EventModifyMaterialSpec(materialSpec));
    }

    public requestModifyIsSellStatus(status?: boolean): void {
        this.dispatch(new _EventModifyIsSellStatus(status));
    }

    private _calculatePiecePrice(): void {
        const innerState = this.innerState;
        if (innerState.detail.packagePrice != null && (innerState.detail.pieceNum ?? 0.0) > 0) {
            innerState.detail.piecePrice = innerState.detail.packagePrice / innerState.detail.pieceNum!;
            innerState.detail.piecePrice = Math.round(innerState.detail.piecePrice * 100) / 100;
        }
    }

    private async *_mapEventUpdateMedicineShortId(event: _EventUpdateMedicineShortId): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.detail.shortId = event.shortId;
        this.update();
    }

    private async *_mapEventSelectComponentUnit(/*ignore: _EventSelectComponentUnit*/): AsyncGenerator<State> {
        const innerState = this.innerState;
        const westernConfig = WesternMedicineConfigProvider.getConfig();
        const units = westernConfig?.dosageUnit?.map((item) => item.name!) ?? [];
        const selects = await AbcDialog.showOptionsBottomSheet({
            title: "选择单位",
            options: units!,
            initialSelectIndexes: new Set([units!.indexOf(innerState.detail.componentContentUnit!)]),
            height: pxToDp(400),
            crossAxisCount: 4,
        });
        if (_.isEmpty(selects)) return;

        innerState.detail.componentContentUnit = units[_.first(selects)!];
        this.update();
    }

    private async *_mapEventSelectPackageUnit(/*ignore: _EventSelectPackageUnit*/): AsyncGenerator<State> {
        const innerState = this.innerState;
        const westernConfig = WesternMedicineConfigProvider.getConfig();
        const units = westernConfig.materialAndCommodityUnit!.map((item) => item.name!);
        const selects = await AbcDialog.showOptionsBottomSheet({
            title: "选择单位",
            options: units,
            initialSelectIndexes: new Set([units.indexOf(innerState.detail.packageUnit!)]),
            height: pxToDp(400),
            crossAxisCount: 4,
        });
        if (_.isEmpty(selects)) return;

        innerState.detail.packageUnit = units[_.first(selects)!];

        innerState.useUnit = innerState.detail?.unitPreferPackage;

        yield innerState.clone();
    }

    private async *_mapEventSelectSpecification(): AsyncGenerator<State> {
        const innerState = this.innerState;
        const selects = await showOptionsBottomSheet({
            title: "请选择规格",
            options: innerState.specifications,
            optionDescs: ["示例：50mg * 10片 / 盒", "示例： 1ml : 0.5mg * 8支 / 盒"],
        });

        if (_.isEmpty(selects)) return;
        innerState.detail.specType = _.first(selects)!;
        yield innerState.clone();
    }

    private async *_mapEventUpdateMedicineSheBaoCode(/*event: _EventUpdateMedicineSheBaoCode*/): AsyncGenerator<State> {
        const innerState = this.innerState;
        const goodsInfo = innerState.detail;

        if (goodsInfo.isWesternMedicine) {
            innerState.sheBaoCodeSearchGoodsSubType = 1; // 西药 1
        } else if (goodsInfo.isChineseWesternMedicine) {
            innerState.sheBaoCodeSearchGoodsSubType = 3; // 中成药 3
        } else if (goodsInfo.isMedicineChinesePiece || goodsInfo.isMedicineChineseGranule) {
            innerState.sheBaoCodeSearchGoodsSubType = 2; // 中药饮片/中药颗粒 2
        }

        const result: SheBaoCodeSearchGoodsListItem | string = await ABCNavigator.navigateToPage<SheBaoCodeSearchGoodsListItem>(
            <SheBaoSearchNationalCodeSearchPage
                goodsType={GoodsType.medicine}
                goodsSubType={innerState.sheBaoCodeSearchGoodsSubType}
                shebaoCodeType={SheBaoCodeTypeEnum.nationalCode}
                name={goodsInfo.medicineCadn}
                manufacturer={goodsInfo.manufacturerFull}
                shebaoCode={goodsInfo.shebao?.nationalCode}
                medicalFeeGradeStr={innerState?.medicalFeeGradeStr}
                covidDictFlagStr={innerState?.covidDictFlagStr}
                nationalCodeId={goodsInfo.shebao?.nationalCodeId}
            />
        );
        if (_.isEmpty(result)) return;

        innerState.sheBaoCodeSearchGoodsSelectItem = result;

        if (result._popMedicalInsuranceCodeTypeStr == "null") {
            goodsInfo.shebao = JsonMapper.deserialize(GoodsInfoShebao, {
                goodsId: goodsInfo.id,
                goodsType: GoodsTypeId.medicine,
                isDummy: 0,
                medicineNum: goodsInfo.shortId,
                name: innerState.sheBaoCodeSearchGoodsSelectItem?.medicineCadn ?? goodsInfo.displayName,
                medicalFeeGrade: goodsInfo.medicalFeeGrade,
                nationalCode: innerState.sheBaoCodeSearchGoodsSelectItem?.shebaoCode,
                nationalCodeId: undefined,
            });
        } else {
            goodsInfo.shebao = JsonMapper.deserialize(GoodsInfoShebao, {
                goodsId: goodsInfo.id,
                goodsType: GoodsTypeId.medicine,
                isDummy: 0,
                medicineNum: goodsInfo.shortId,
                name: innerState.sheBaoCodeSearchGoodsSelectItem?.medicineCadn ?? goodsInfo.medicineCadn,
                medicalFeeGrade: goodsInfo.medicalFeeGrade,
                nationalCode: innerState.sheBaoCodeSearchGoodsSelectItem.shebaoCode,
                nationalCodeId: innerState.sheBaoCodeSearchGoodsSelectItem?.shebaoCodeId,
            });
        }
        if (result._popMedicalInsuranceCodeTypeStr == "null") {
            innerState.defaultMedicalInsuranceCodeStr = "不刷医保/暂无编码";
        } else if (result._popMedicalInsuranceCodeTypeStr == "save") {
            innerState.defaultMedicalInsuranceCodeStr = result.shebaoCode;
            innerState.sheBaoCodeSearchGoodsSelectItem.shebaoCode = result.shebaoCode;
        } else if (result._popMedicalInsuranceCodeTypeStr == "card") {
            innerState.defaultMedicalInsuranceCodeStr = result.medicineCadn;
        } else {
            if (!!innerState.sheBaoCodeSearchGoodsSelectItem?.shebaoCode?.length) {
                innerState.defaultMedicalInsuranceCodeStr = innerState.sheBaoCodeSearchGoodsSelectItem?.shebaoCode;
            } else {
                innerState.defaultMedicalInsuranceCodeStr = "选择医保对码";
            }
        }

        //  总部且地区为杭州:选择对码后，若有剂型dosageFrom值，且档案【剂型】无值时，自动填入档案【剂型】中
        if (
            (userCenter.clinic?.isChainAdminClinic || userCenter.clinic?.isNormalClinic) &&
            userCenter.shebaoConfig?.isRegionHangzhou &&
            !!result?.dosageForm &&
            result.goodsType == GoodsType.medicine &&
            (result.goodsSubType == GoodsSubType.medicineChinesePatent || result.goodsSubType == GoodsSubType.medicineWestern) &&
            !goodsInfo?.dosageFormType
        ) {
            const dosageFormTypeList = userCenter.clinicDictionaryInfo?.businessScopeDict?.westDrugDosageFormTypeList ?? [];
            goodsInfo.dosageFormType = dosageFormTypeList?.find((t) => t.name == result?.dosageForm)?.typeId;
        }

        yield innerState.clone();
    }

    private async *_mapEventSelectMedicalInsurancePayment(/*event: _EventSelectMedicalInsurancePayment*/): AsyncGenerator<State> {
        const innerState = this.innerState;
        let initialOption: Set<number> = new Set();
        const _options = [
            { payMode: SheBaoPayModeEnum.OVERALL, text: "优先统筹支付" },
            { payMode: SheBaoPayModeEnum.SELF, text: "优先个账支付" },
            { payMode: SheBaoPayModeEnum.NO_USE, text: "不使用医保支付" },
        ];
        _options.forEach((payInfo) => {
            if (payInfo.payMode == innerState.detail.shebaoPayMode) {
                initialOption = new Set([payInfo.payMode]);
            }
        });

        const selects = await showOptionsBottomSheet({
            title: "选择医保支付",
            options: _options.map((item) => item.text),
            initialSelectIndexes: initialOption,
            showCloseButton: true,
            showTopRadius: true,
            titlePosition: "center",
            titleStyle: TextStyles.t18MT1,
        });
        if (!selects) return;
        innerState.detail.shebaoPayMode = _.first(selects);
        yield innerState.clone();
    }

    private async *_mapEventUpdatePieceNum(event: _EventUpdatePieceNum): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.detail.pieceNum = event.num;
        this._calculatePiecePrice();
        yield innerState.clone();
    }

    private async *_mapEventSelectPieceUnit(/*ignore: _EventSelectPieceUnit*/): AsyncGenerator<State> {
        const innerState = this.innerState;
        const units = WesternMedicine.medicinePricingUnit?.map((item) => item.name!) ?? [];
        const selects = await AbcDialog.showOptionsBottomSheet({
            title: "选择计价单位",
            options: units,
            initialSelectIndexes: new Set([units.indexOf(innerState.detail.pieceUnit!)]),
            height: pxToDp(400),
            crossAxisCount: 4,
        });
        if (_.isEmpty(selects)) return;

        innerState.detail.pieceUnit = units[_.first(selects)!];

        innerState.useUnit = innerState.detail?.unitPreferPackage;

        yield innerState.clone();
    }

    private async *_mapEventUpdateDosageNum(event: _EventUpdateDosageNum): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.detail.medicineDosageNum = event.num;
        this.update();
    }

    private async *_mapEventUpdateComponentNum(event: _EventUpdateComponentNum): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.detail.componentContentNum = event.num;
        this.update();
    }

    public requestSelectPackageUnit(): void {
        this.dispatch(new _EventSelectPackageUnit());
    }

    public requestSelectSpecification(): void {
        this.dispatch(new _EventSelectSpecification());
    }
    public requestUpdateDosageNum(value: number): void {
        this.dispatch(new _EventUpdateDosageNum(value));
    }

    // 容量
    public requestUpdateComponentNum(value: number): void {
        this.dispatch(new _EventUpdateComponentNum(value));
    }

    public requestSelectComponentUnit(): void {
        this.dispatch(new _EventSelectComponentUnit());
    }
    public requestUpdatePieceNum(value: number): void {
        this.dispatch(new _EventUpdatePieceNum(value));
    }

    public requestSelectPieceUnit(): void {
        this.dispatch(new _EventSelectPieceUnit());
    }
    public requestUpdatePiecePrice(price: number): void {
        this.dispatch(new _EventModifyPiecePrice(price));
    }

    // 售价
    public requestUpdatePackagePrice(price: number): void {
        this.dispatch(new _EventModifyPackagePrice(price));
    }

    public requestModifyDefaultInOutTax(defaultInOutTax: number): void {
        this.dispatch(new _EventModifyDefaultInOutTax(defaultInOutTax));
    }

    private async *_mapEventModifyDefaultInOutTax(event: _EventModifyDefaultInOutTax): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.detail.defaultInOutTax = event.num;
        if (innerState.detail.typeId && innerState.detail.defaultInOutTax == 1) {
            const inOutTax = innerState.inOutTaxList?.find((item) => item.typeId == innerState.detail.typeId);
            innerState.detail.inTaxRat = inOutTax?.inTaxRat ?? 0;
            innerState.detail.outTaxRat = inOutTax?.outTaxRat ?? 0;
        }
        this.update();
    }
    private async *_mapEventSelectMemberPrice(): AsyncGenerator<State> {
        const result: GoodsMultiPriceView[] = await ABCNavigator.navigateToPage(
            <InventorySettingMemberPricePage
                goodsInfo={this.innerState.detail}
                memberPriceList={this.innerState.detail.multiPriceList}
                editType="edit"
                canSetPrice={!userCenter.clinic?.isNormalClinic ? this.innerState.showSubSetPrice : false}
                canAdjustPrice={this.innerState.canAdjustPrice}
            />
        );
        if (!result) return;
        this.innerState.detail.multiPriceList = result;
        this.update();
    }

    // 商品编码
    public requestUpdateMedicineShortId(shortId: string): void {
        this.dispatch(new _EventUpdateMedicineShortId(shortId));
    }

    // 选择医保对码
    public requestUpdateMedicineSheBaoCode(): void {
        this.dispatch(new _EventUpdateMedicineSheBaoCode());
    }

    // 选择医保支付
    public requestSelectMedicalInsurancePayment(): void {
        this.dispatch(new _EventSelectMedicalInsurancePayment());
    }
    // 设置会员价
    requestSelectMemberPrice(): void {
        this.dispatch(new _EventSelectMemberPrice());
    }
}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}

class _EventModifyCadn extends _Event {}

class _EventModifyTradename extends _Event {}

class _EventSelectPharmaceuticalForm extends _Event {}

class _EventSelectPriceType extends _Event {}

class _EventSelectMarkupRate extends _Event {
    markupRate?: number;
    constructor(markupRate?: number) {
        super();
        this.markupRate = markupRate;
    }
}
class _EventCheckPriceModeRange extends _Event {
    typeId?: number;
    value?: number;
    constructor(typeId?: number, value?: number) {
        super();
        this.typeId = typeId;
        this.value = value;
    }
}
class _EventSelectOTCForm extends _Event {}

class _EventSelectNarcoticsDangerousDrugsRelease extends _Event {}

class _EventSelectAntibiotics extends _Event {}

class _EventUpdateDefinedDailyDose extends _Event {
    dddOfAntibiotic?: number;
    unitOfAntibiotic?: string;

    constructor(dddOfAntibiotic?: number, unitOfAntibiotic?: string) {
        super();
        this.dddOfAntibiotic = dddOfAntibiotic;
        this.unitOfAntibiotic = unitOfAntibiotic;
    }
}

class _EventSelectMedicalMaterialDeviceType extends _Event {}

class _EventSelectBaseMedicineType extends _Event {}

class _EventSelectMaintenanceClassification extends _Event {}

class _EventSelectStorageConditions extends _Event {}

class _EventSelectExpirationDate extends _Event {
    monthNum?: number;

    constructor(monthNum?: number) {
        super();
        this.monthNum = monthNum;
    }
}

class _EventSelectTags extends _Event {}

class _EventModifyManufacturerFull extends _Event {
    type?: string;

    constructor(type?: string) {
        super();
        this.type = type;
    }
}

class _EventModifyMedicineNmpn extends _Event {
    num: string;

    constructor(num: string) {
        super();
        this.num = num;
    }
}

class _EventUpdateMedicineShortId extends _Event {
    shortId?: string;
    constructor(shortId?: string) {
        super();
        this.shortId = shortId;
    }
}

class _EventUpdateMedicineSheBaoCode extends _Event {}

class _EventSelectMedicalInsurancePayment extends _Event {}

class _EventScanQRCode extends _Event {}

class _EventModifyQRCode extends _Event {
    QrCode: string;

    constructor(QrCode: string) {
        super();
        this.QrCode = QrCode;
    }
}

class _EventSelectSpecification extends _Event {}

class _EventSelectPackageUnit extends _Event {}

class _EventModifyDosage extends _Event {
    num?: number;

    constructor(num?: number) {
        super();
        this.num = num;
    }
}

class _EventUpdateDosageNum extends _EventModifyDosage {}

class _EventSelectComponentUnit extends _Event {}

class _EventUpdateComponentNum extends _EventModifyDosage {}

class _EventUpdatePieceNum extends _EventModifyDosage {}

class _EventSelectPieceUnit extends _Event {}

class _EventModifyDosageUnit extends _Event {
    unit: string;

    constructor(unit: string) {
        super();
        this.unit = unit;
    }
}

class _EventModifyPieceNum extends _Event {
    num?: number;

    constructor(num?: number) {
        super();
        this.num = num;
    }
}

class _EventModifyPackagePrice extends _Event {
    num?: number;

    constructor(num?: number) {
        super();
        this.num = num;
    }
}

class _EventModifyDefaultInOutTax extends _EventModifyPackagePrice {}
class _EventModifyInTaxRat extends _EventModifyPackagePrice {}

class _EventModifyOutTaxRat extends _EventModifyPackagePrice {}

class _EventSelectProfitCategorization extends _Event {}

class _EventModifyPiecePrice extends _Event {
    num?: number;

    constructor(num?: number) {
        super();
        this.num = num;
    }
}

class _EventModifyPieceUnit extends _Event {
    unit: string;

    constructor(unit: string) {
        super();
        this.unit = unit;
    }
}

class _EventModifyPackageUnit extends _Event {
    unit: string;

    constructor(unit: string) {
        super();
        this.unit = unit;
    }
}

class _EventModifyExtendSpec extends _Event {
    extendSpec?: string;

    constructor(unit?: string) {
        super();
        this.extendSpec = unit;
    }
}

class _EventModifyEqCoefficient extends _Event {
    eqCoefficient?: number;

    constructor(val?: number) {
        super();
        this.eqCoefficient = val;
    }
}

class _EventSaveMedicineInfo extends _Event {}

class _EventModifySecondaryClassification extends _Event {}

class _EventSelectPharmacologic extends _Event {
    type?: PharmacologicSourceType;
    constructor(type?: PharmacologicSourceType) {
        super();
        this.type = type;
    }
}

class _EventModifyPosition extends _Event {
    position?: string;

    constructor(position?: string) {
        super();
        this.position = position;
    }
}

class _EventModifySmartDispense extends _Event {
    status?: boolean;

    constructor(status?: boolean) {
        super();
        this.status = status;
    }
}

class _EventModifyDismountingStatus extends _Event {
    status?: boolean;

    constructor(status?: boolean) {
        super();
        this.status = status;
    }
}

class _EventModifyName extends _Event {
    name: string;

    constructor(text: string) {
        super();
        this.name = text;
    }
}

class _EventUpdateMedicineGoodsTypeId extends _Event {}

class _EventModifyOrigin extends _Event {}

class _EventModifyMaterialSpec extends _Event {
    materialSpec: string;
    constructor(materialSpec: string) {
        super();
        this.materialSpec = materialSpec;
    }
}

class _EventModifyIsSellStatus extends _EventModifyDismountingStatus {}

class _EventContinuePutMedicineInfo extends _Event {
    detail: MedicineSpecChangeDetail;

    constructor(detail: MedicineSpecChangeDetail) {
        super();
        this.detail = detail;
    }
}
class _EventSelectBusinessScope extends _Event {}
class _EventUpdateMark extends _Event {
    mark?: string;
    constructor(mark?: string) {
        super();
        this.mark = mark;
    }
}
class _EventSelectNpmnExpiryDate extends _Event {}
class _EventSelectMemberPrice extends _Event {}

class _EventModifyPlaceOfOrigin extends _Event {
    placeOrigin: string;
    constructor(placeOrigin: string) {
        super();
        this.placeOrigin = placeOrigin;
    }
}
class _EventUpdatePieceWeight extends _Event {
    value: number;
    constructor(value: number) {
        super();
        this.value = value;
    }
}
