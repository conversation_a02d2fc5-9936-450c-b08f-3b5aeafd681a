/**
 * create by <PERSON><PERSON>
 * desc:
 * create date 2021/2/7
 */
import React from "react";
import { ScrollView, Text, View } from "@hippy/react";
import { BaseBlocNetworkPage } from "../../../base-ui/base-page";
import { BlocBuilder } from "../../../bloc";
import { InventoryMedicineBaseInfoPageBloc } from "./inventory-medicine-base-info-page-bloc";
import {
    ListSettingEditItem,
    ListSettingItem,
    ListSettingItemStyle,
    ListSettingRadiosItem,
} from "../../../base-ui/views/list-setting-item";
import { IconFontView, SizedBox, Spacer, ToolBar, ToolBarButtonStyle1 } from "../../../base-ui";
import { ABCStyles, ABCStyleSheet, Colors, Sizes, TextStyles } from "../../../theme";
import {
    LengthLimitingTextInputFormatter,
    <PERSON>O<PERSON><PERSON><PERSON><PERSON>atter,
    PrecisionLimitFormatter,
    <PERSON>For<PERSON>,
} from "../../../base-ui/utils/formatter";
import { CustomInput } from "../../../base-ui/input/custom-input";
import { WesternMedicine } from "../../../../assets/medicine_usage/western-medicine-config";
import { userCenter } from "../../../user-center";
import { showOptionsBottomSheet } from "../../../base-ui/dialog/bottom_sheet";
import _ from "lodash";
import { BlocHelper } from "../../../bloc/bloc-helper";
import { ignore } from "../../../common-base-module/global";
import { NumberKeyboardBuilder } from "../../../base-ui/views/keyboards/number-keyboard";
import { AbcFlexInputPrice } from "../views/abc-flex-input-price";
import { GroupDivider } from "../../../base-ui/divider-line";
import { StringUtils } from "../../../base-ui/utils/string-utils";
import { AbcSwitch } from "../../../base-ui/switch/abc-switch";
import { AbcView } from "../../../base-ui/views/abc-view";
import { DeviceUtils } from "../../../base-ui/utils/device-utils";
import { SheBaoCodeCardView } from "../inventory-shebao-national-code-search-page-";
import { AbcTextInput, KeyboardType } from "../../../base-ui/views/abc-text-input";
import { InventoryConst } from "../../inventory-const";
import { _RadiusButton } from "../inventory-goods-medicine-create-view";
import { GetSheBaoCodeDetailParams, InventoryGoodsAgent, MedicineCategoryItem } from "../data/inventory-goods-agent";
import { DialogIndex, showConfirmDialog, showQueryDialog } from "../../../base-ui/dialog/dialog-builder";
import { GoodsPriceUtils } from "../utils/goods-utils";
import { AssetImageView } from "../../../base-ui/views/asset-image-view";
import { SubClinicPricePriceMode } from "../../../base-business/data/beans";
import { GoodsPkgCostPriceMakeUpMinMaxItem } from "../../data/inventory-bean";
import { InventoryAgent } from "../../data/inventory-agent";
import { PharmacyType } from "../../../charge/data/charge-bean-air-pharmacy";
import { ListSettingEditItemInventoryInTagView } from "../../inventory-in/inventory-in-views/inventory-in-tag-dialog";
import { GoodsDistributionItem } from "../data/inventory-goods-bean";
import abcI18Next from "../../../language/config";
import { AbcText } from "@app/abc-mobile-ui";
import { InventoryPieceWeightView } from "../views/inventory-piece-weight-view";

enum ListSettingItemType {
    listSettingItem = "ListSettingItem",
    listSettingEditItem = "ListSettingEditItem",
    listSettingRadiosItem = "ListSettingRadiosItem",
}

const styles = ABCStyleSheet.create({
    groupContainer: {
        paddingHorizontal: Sizes.listHorizontalMargin,
        backgroundColor: Colors.white,
    },
});

interface InventoryMedicineBaseInfoPageProps {
    id: string;
    typeId: number;
    disable?: boolean;

    getSheBaoCodeDetailParams?: GetSheBaoCodeDetailParams;
    medicineCategoryItems?: MedicineCategoryItem[]; // 用药助手分类
    isShowPosition?: boolean;
}

export class InventoryMedicineBaseInfoPage extends BaseBlocNetworkPage<
    InventoryMedicineBaseInfoPageProps,
    InventoryMedicineBaseInfoPageBloc
> {
    private _inputHeight = Sizes.dp22;
    private _priceModeInput?: AbcTextInput | null;

    constructor(props: InventoryMedicineBaseInfoPageProps) {
        super(props);
        this.bloc = new InventoryMedicineBaseInfoPageBloc({
            id: props.id,
            typeId: props.typeId,
            disable: props.disable,
            getSheBaoCodeDetailParams: props.getSheBaoCodeDetailParams,
            medicineCategoryItems: props.medicineCategoryItems,
        });
    }

    getAppBar(): JSX.Element | undefined {
        return <BlocBuilder bloc={this.bloc} condition={() => true} build={() => super.getAppBar()} />;
    }

    getAppBarTitle(): string {
        const state = this.bloc.currentState;
        if (state.isGoods) return "商品信息";

        if (state.isMaterial) return "物资信息";

        if (state.isMedicine) return "药品信息";
        return "";
    }

    componentDidMount(): void {
        BlocHelper.connectLoadingStatus(this.bloc, this);
    }

    async handleSave(): Promise<void> {
        const state = this.bloc.currentState,
            goodsInfo = state.detail,
            originGoodsInfo = state._detail;

        let flag = DialogIndex.positive;
        const openEqCoefficientDialog = async (rows: GoodsDistributionItem[]) => {
            if (rows.length) {
                return await showConfirmDialog(
                    "当量调整后库存余量调整确认",
                    <View>
                        <Text style={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}>
                            {`换算当量由颗粒:饮片 1:${originGoodsInfo.eqCoefficient}调整为 1:${goodsInfo.eqCoefficient}，以下库房现有存量颗粒，如需调整数量请及时进行盘点`}
                        </Text>
                        <View style={{ flex: 1, paddingVertical: Sizes.dp16 }}>
                            <ScrollView contentContainerStyle={{ paddingVertical: Sizes.dp16 }}>
                                {rows.map((item) => {
                                    const { pharmacy } = item;
                                    return (
                                        <View style={[ABCStyles.bottomLine, { paddingVertical: Sizes.dp6 }]}>
                                            <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
                                                <View style={{ width: Sizes.dp219 }}>
                                                    <Text style={[TextStyles.t16NT0, { height: Sizes.dp24 }]}>{pharmacy?.name || ""}</Text>
                                                    <Text
                                                        style={[TextStyles.t13NT1.copyWith({ color: "#929AA6" }), { height: Sizes.dp20 }]}
                                                    >
                                                        {userCenter.clinic?.isChainAdminClinic ? item.clinicName : ""}
                                                    </Text>
                                                </View>
                                                <View style={{ marginLeft: Sizes.dp8, width: Sizes.dp80 }}>
                                                    <Text style={[TextStyles.t16NT0, { height: Sizes.dp24 }]}>
                                                        {item.dispStockGoodsCount}
                                                    </Text>
                                                    <Text
                                                        style={[TextStyles.t13NT1.copyWith({ color: "#929AA6" }), { height: Sizes.dp20 }]}
                                                    >
                                                        账面数量
                                                    </Text>
                                                </View>
                                            </View>
                                        </View>
                                    );
                                })}
                            </ScrollView>
                        </View>
                    </View>,
                    `确定`
                );
            }

            return DialogIndex.positive;
        };
        if (originGoodsInfo.eqCoefficient !== goodsInfo.eqCoefficient) {
            if (!(goodsInfo.pieceUnit === "g" || goodsInfo.pieceUnit === "克")) {
                // 当量变化，但是单位不是g/克，要还原
                goodsInfo.pieceUnit = originGoodsInfo.pieceUnit;
            } else {
                try {
                    const params = {
                        offset: 0,
                        limit: 100,
                        onlyStock: 1,
                        queryType: 1,
                    };
                    const rows = await InventoryGoodsAgent.getGoodsDistribution2(goodsInfo.goodsId!, params);
                    if (goodsInfo.piecePrice === originGoodsInfo.piecePrice) {
                        flag = await showQueryDialog(
                            "当量调整后售价调整确认",
                            <View>
                                <Text style={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}>
                                    {`换算当量由颗粒:饮片 1:${originGoodsInfo.eqCoefficient}调整为 1:${
                                        goodsInfo.eqCoefficient
                                    }，请确定零售价格是否保持为“${abcI18Next.t("¥")} ${goodsInfo.piecePrice}/${goodsInfo.pieceUnit}”`}
                                </Text>
                            </View>,
                            `暂不修改`,
                            `去修改`
                        );
                        if (flag === DialogIndex.negative) return;
                        flag = await openEqCoefficientDialog(rows);
                    } else {
                        flag = await openEqCoefficientDialog(rows);
                    }
                } catch (e) {
                    console.log(e);
                }
            }
        }
        if (flag === DialogIndex.negative) return;
        this.bloc.requestSaveMedicineInfo();
    }

    /**
     * 物资基础模块
     * @private
     */
    private _renderMaterialBaseInfoView(): JSX.Element {
        const state = this.bloc.currentState,
            canEdit = state.canEdit,
            goodsInfo = state.detail;
        const _contentTextStyle = TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp24, color: !canEdit ? Colors.t3 : Colors.T1 });
        const materialInfoList = [
            {
                itemStyleType: ListSettingItemType.listSettingEditItem,
                title: "物资名称",
                starTitle: "*",
                editable: canEdit,
                showErrorBorder: state.showErrorHint && (!goodsInfo?.name || !goodsInfo.name.length),
                content: goodsInfo?.name,
                textInputStyle: _contentTextStyle,
                onChanged: (text: string) => {
                    if (!canEdit) return;
                    this.bloc.requestModifyName(text.trim());
                },
            },
            {
                itemStyleType: ListSettingItemType.listSettingItem,
                title: "药品类型",
                content: state.detail?.displayTypeName,
            },
            {
                itemStyleType: ListSettingItemType.listSettingItem,
                title: "二级分类",
                contentHint: canEdit ? "请选择二级分类" : "",
                content: state.detail.customTypeName,
                onClick: () => {
                    if (!canEdit) return;
                    this.bloc.requestModifySecondaryClassification();
                },
                itemStyle: canEdit ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal,
            },
            {
                itemStyleType: ListSettingItemType.listSettingItem,
                show: goodsInfo.isMedicalMaterial,
                title: "进口/国产",
                content: goodsInfo?.origin,
                contentTextStyle: _contentTextStyle,
                contentHint: canEdit ? "选择进口/国产" : "",
                onClick: () => {
                    if (!canEdit) return;
                    this.bloc.requestModifyOrigin();
                },
            },
            {
                itemStyleType: ListSettingItemType.listSettingItem,
                title: "生产厂家",
                content: goodsInfo?.manufacturerFull,
                contentStyle: { paddingVertical: Sizes.dp12 },
                contentHint: canEdit ? "输入生产厂家" : "",
                contentTextStyle: _contentTextStyle,
                onClick: () => {
                    if (!canEdit) return;
                    this.bloc.requestModifyManufacturerFull("manufacturerFull");
                },
            },
            {
                show: goodsInfo.isMedicalMaterial,
                itemStyleType: ListSettingItemType.listSettingItem,
                title: "供应商",
                content: goodsInfo?.lastStockInOrderSupplier,
                contentStyle: { paddingVertical: Sizes.dp12 },
                contentTextStyle: _contentTextStyle,
            },
            {
                itemStyleType: ListSettingItemType.listSettingEditItem,
                title: "条码",
                editable: canEdit,
                content: goodsInfo?.barCode,
                textInputStyle: _contentTextStyle,
                contentHint: canEdit ? "请输入条码" : "",
                endIcon: canEdit
                    ? () => (
                          <IconFontView
                              name={"scan"}
                              color={Colors.mainColor}
                              style={{ marginLeft: Sizes.dp8 }}
                              size={Sizes.dp14}
                              onClick={() => {
                                  if (!canEdit) return;
                                  this.bloc.requestScanQRCode();
                              }}
                          />
                      )
                    : undefined,
                formatter: NumberOnlyFormatter(),
                onChanged: (QRCode: string) => {
                    this.bloc.requestModifyQRCode(QRCode);
                },
            },
            {
                itemStyleType: ListSettingItemType.listSettingEditItem,
                title: "商品编码",
                contentHint: canEdit ? "系统生成或自定义" : "",
                content: goodsInfo.shortId ?? "",
                textInputStyle: _contentTextStyle,
                onChanged: (shortId: string) => this.bloc.requestUpdateMedicineShortId(shortId),
                maxLength: 20,
            },
        ];
        return (
            <View style={styles.groupContainer}>
                {materialInfoList.map((materialInfo) => {
                    if (materialInfo.show == false) return;
                    if (materialInfo.itemStyleType === ListSettingItemType.listSettingItem) {
                        return (
                            <ListSettingItem
                                key={materialInfo.title}
                                title={materialInfo.title}
                                bottomLine={true}
                                content={materialInfo.content}
                                contentStyle={materialInfo.contentStyle}
                                contentTextStyle={_contentTextStyle}
                                contentHint={materialInfo.contentHint}
                                onClick={materialInfo.onClick}
                            />
                        );
                    } else if (materialInfo.itemStyleType === ListSettingItemType.listSettingEditItem) {
                        return (
                            <ListSettingEditItem
                                key={materialInfo.title}
                                title={materialInfo.title}
                                bottomLine={true}
                                editable={materialInfo.editable}
                                content={materialInfo.content}
                                textInputStyle={_contentTextStyle}
                                contentHint={materialInfo.contentHint}
                                endIcon={materialInfo.endIcon}
                                formatter={materialInfo.formatter}
                                onChanged={materialInfo.onChanged}
                            />
                        );
                    }
                })}
            </View>
        );
    }

    /**
     * 商品(基础模块)
     * @private
     */
    private _renderGoodsInfoBaseView(): JSX.Element {
        const state = this.bloc.currentState,
            canEdit = state.canEdit,
            goodsInfo = state.detail;
        const _contentTextStyle = TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp24, color: !canEdit ? Colors.t3 : Colors.T1 });
        const goodsInfoList = [
            {
                itemStyleType: ListSettingItemType.listSettingItem,
                title: "商品名",
                bottomLine: true,
                content: goodsInfo?.name,
                contentHint: canEdit ? "输入商品名" : "",
                onClick: () => {
                    if (!canEdit) return;
                    this.bloc.requestModifyTradename();
                },
            },
            {
                itemStyleType: ListSettingItemType.listSettingItem,
                title: "药品类型",
                bottomLine: true,
                content: state.detail?.displayTypeName,
            },
            {
                itemStyleType: ListSettingItemType.listSettingItem,
                title: "二级分类",
                contentHint: canEdit ? "请选择二级分类" : "",
                bottomLine: true,
                content: state.detail.customTypeName,
                onClick: () => {
                    if (!canEdit) return;
                    this.bloc.requestModifySecondaryClassification();
                },
                itemStyle: canEdit ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal,
            },
            {
                itemStyleType: ListSettingItemType.listSettingItem,
                title: goodsInfo.isChineseMedicine ? "厂家或产地" : "生产厂家",
                bottomLine: true,
                content: goodsInfo?.manufacturerFull,
                contentStyle: { paddingVertical: Sizes.dp12 },
                contentHint: canEdit ? "输入厂家或产地" : "",
                onClick: () => {
                    if (!canEdit) return;
                    this.bloc.requestModifyManufacturerFull("manufacturerFull");
                },
            },
            {
                itemStyleType: ListSettingItemType.listSettingEditItem,
                title: "批准文号",
                contentHint: canEdit ? "系统生成或自定义" : "",
                content: goodsInfo.medicineNmpn ?? "",
                onChanged: (medicineNmpn: string) => this.bloc.requestModifyMedicineNmpn(medicineNmpn),
                bottomLine: true,
            },
            {
                itemStyleType: ListSettingItemType.listSettingEditItem,
                title: "条形码",
                bottomLine: true,
                editable: canEdit,
                content: goodsInfo?.barCode,
                contentHint: canEdit ? "输入条形码" : "",
                endIcon: canEdit
                    ? () => (
                          <IconFontView
                              name={"scan"}
                              color={Colors.mainColor}
                              style={{ marginLeft: Sizes.dp8 }}
                              size={Sizes.dp14}
                              onClick={() => {
                                  if (!canEdit) return;
                                  this.bloc.requestScanQRCode();
                              }}
                          />
                      )
                    : undefined,
                formatter: NumberOnlyFormatter(),
                onChanged: (QRCode: string) => {
                    this.bloc.requestModifyQRCode(QRCode);
                },
            },
            {
                itemStyleType: ListSettingItemType.listSettingEditItem,
                title: "商品编码",
                contentHint: canEdit ? "系统生成或自定义" : "",
                content: goodsInfo.shortId ?? "",
                onChanged: (shortId: string) => this.bloc.requestUpdateMedicineShortId(shortId),
                bottomLine: true,
                maxLength: 20,
            },
        ];
        return (
            <View style={styles.groupContainer}>
                {goodsInfoList.map((goodsInfo) => {
                    if (goodsInfo.itemStyleType == ListSettingItemType.listSettingItem) {
                        return (
                            <ListSettingItem
                                key={goodsInfo.title}
                                title={goodsInfo.title}
                                bottomLine={goodsInfo.bottomLine}
                                content={goodsInfo?.content}
                                contentStyle={goodsInfo.contentStyle}
                                contentHint={goodsInfo.contentHint}
                                contentTextStyle={_contentTextStyle}
                                onClick={goodsInfo.onClick}
                            />
                        );
                    } else if (goodsInfo.itemStyleType == ListSettingItemType.listSettingEditItem) {
                        return (
                            <ListSettingEditItem
                                key={goodsInfo.title}
                                title={goodsInfo.title}
                                bottomLine={goodsInfo.bottomLine}
                                editable={goodsInfo.editable}
                                content={goodsInfo?.content}
                                textInputStyle={_contentTextStyle}
                                contentHint={goodsInfo.contentHint}
                                endIcon={goodsInfo.endIcon}
                                formatter={goodsInfo.formatter}
                                onChanged={goodsInfo.onChanged}
                            />
                        );
                    }
                })}
            </View>
        );
    }

    /**
     * 药品(基础模块)
     */
    private _renderMedicineBaseInfoView(): JSX.Element {
        const state = this.bloc.currentState,
            goodsInfo = state.detail;
        const { canEdit } = state;
        const _contentTextStyle = TextStyles.t16NT1.copyWith({ lineHeight: Sizes.dp24, color: !canEdit ? Colors.t3 : Colors.T1 });
        const _itemStyle = canEdit ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal;
        // 药店，如果当前药品类型为配方饮片与非配方饮片，则可以两者相互更改
        const canChangeGoodsType =
            (goodsInfo.isMedicineChinesePiece || goodsInfo.isMedicineChineseNonFormula) && userCenter.clinic?.isDrugstoreButler;

        //  南京、南宁-中药类型-批准文号
        const showMedicineNmpn =
            (userCenter.shebaoConfig?.isRegionNanjing || userCenter.shebaoConfig?.isRegionNanning) && goodsInfo?.isChineseMedicine;
        //  南京、南宁-中药类型-剂型
        const showDosageFormType =
            (userCenter.shebaoConfig?.isRegionNanjing || userCenter.shebaoConfig?.isRegionNanning) && goodsInfo?.isChineseMedicine;

        // 南京-中药类型-产地
        const showMedicineProducePlace = userCenter.shebaoConfig?.isRegionNanjing && goodsInfo?.isChineseMedicine;

        const dosageFormTypeList =
            (showDosageFormType
                ? userCenter.clinicDictionaryInfo?.businessScopeDict?.chineseDrugDosageFormTypeList
                : userCenter.clinicDictionaryInfo?.businessScopeDict?.westDrugDosageFormTypeList) ?? [];

        const baseInfoList = [
            {
                itemStyleType: ListSettingItemType.listSettingItem,
                title: "通用名",
                starTitle: "*",
                showErrorBorder: state.showErrorHint && (!goodsInfo?.medicineCadn || !goodsInfo.medicineCadn.length),
                content: goodsInfo?.medicineCadn,
                onClick: () => {
                    if (!canEdit) return;
                    this.bloc.requestModifyCadn();
                },
            },
            {
                itemStyleType: ListSettingItemType.listSettingItem,
                title: "药品类型",
                content: state.detail?.displayTypeName,
                editable: canChangeGoodsType,
                itemStyle: canChangeGoodsType ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal,
                onClick: () => {
                    if (!canChangeGoodsType) return;
                    this.bloc.requestUpdateMedicineGoodsTypeId();
                },
            },
            {
                itemStyleType: ListSettingItemType.listSettingItem,
                title: "二级分类",
                contentHint: canEdit ? "请选择二级分类" : "",
                content: goodsInfo.customTypeName,
                onClick: () => {
                    if (!canEdit) return;
                    this.bloc.requestModifySecondaryClassification();
                },
                itemStyle: _itemStyle,
            },
            {
                itemStyleType: ListSettingItemType.listSettingItem,
                show: goodsInfo.isWesternMedicine || goodsInfo.isChineseWesternMedicine || goodsInfo.isChineseMedicine,
                style: ABCStyles.bottomLine,
                title: goodsInfo.displayPharmacologicSource().pharmacologicName,
                content: goodsInfo._pharmacologicDisplay({
                    medicineCategory: state.medicineCategoryItems ?? [],
                    pharmacologicId: goodsInfo.pharmacologicId ?? "",
                }),
                contentHint: canEdit ? `选择${goodsInfo.displayPharmacologicSource().pharmacologicName}` : "",
                itemStyle: canEdit ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal,
                onClick: () => {
                    if (!canEdit) return;
                    this.bloc.requestSelectPharmacologic(goodsInfo.displayPharmacologicSource().pharmacologicSourceType);
                },
            },
            {
                itemStyleType: ListSettingItemType.listSettingItem,
                show: goodsInfo.isMedicineWestAndChinesePatent,
                title: "商品名",
                content: goodsInfo?.name,
                contentHint: canEdit ? "输入商品名" : "",
                onClick: () => {
                    if (!canEdit) return;
                    this.bloc.requestModifyTradename();
                },
            },
            {
                itemStyleType: ListSettingItemType.listSettingItem,
                show: goodsInfo.isMedicineWestAndChinesePatent || showDosageFormType,
                style: ABCStyles.bottomLine,
                title: "剂型",
                content: dosageFormTypeList?.find((t) => t.typeId == goodsInfo?.dosageFormType)?.displayName,
                contentHint: canEdit ? "选择剂型" : "",
                onClick: () => {
                    if (!canEdit) return;
                    this.bloc.requestSelectPharmaceuticalForm();
                },
                itemStyle: _itemStyle,
            },
            {
                itemStyleType: ListSettingItemType.listSettingItem,
                title: goodsInfo.isChineseMedicine && !userCenter.shebaoConfig?.isRegionNanjing ? "厂家/产地" : "厂家",
                content: goodsInfo?.manufacturerFull,
                contentStyle: { paddingVertical: Sizes.dp12 },
                contentHint: canEdit ? `输入${userCenter.shebaoConfig?.isRegionNanjing ? "厂家" : "厂家或产地"}` : "",
                onClick: () => {
                    if (!canEdit) return;
                    this.bloc.requestModifyManufacturerFull("manufacturerFull");
                },
            },
            {
                show: showMedicineProducePlace,
                itemStyleType: ListSettingItemType.listSettingEditItem,
                title: "产地",
                contentHint: canEdit ? "输入产地" : "",
                content: goodsInfo.origin ?? "",
                editable: canEdit,
                onChanged: (placeOrigin: string) => this.bloc.requestModifyPlaceOfOrigin(placeOrigin),
            },
            {
                show: goodsInfo.isMedicineWestAndChinesePatent || goodsInfo.isMaterialDisinfectant || showMedicineNmpn,
                itemStyleType: ListSettingItemType.listSettingEditItem,
                title: "批准文号",
                contentHint: canEdit ? "输入批准文号" : "",
                content: goodsInfo.medicineNmpn ?? "",
                editable: canEdit,
                onChanged: (medicineNmpn: string) => {
                    this.bloc.requestModifyMedicineNmpn(medicineNmpn);
                },
            },
            {
                itemStyleType: ListSettingItemType.listSettingEditItem,
                title: "条码",
                editable: canEdit,
                content: goodsInfo?.barCode,
                contentHint: canEdit ? "输入条码" : "",
                endIcon: canEdit
                    ? () => (
                          <AbcView
                              style={[
                                  ABCStyles.rowAlignCenter,
                                  {
                                      marginLeft: Sizes.dp24,
                                      height: Sizes.dp30,
                                      borderLeftWidth: Sizes.dpHalf,
                                      borderColor: Colors.bdColor,
                                      borderRadius: DeviceUtils.isIOS() ? 1 : undefined, // 解决显示BUG
                                  },
                              ]}
                              onClick={() => {
                                  if (!canEdit) return;
                                  this.bloc.requestScanQRCode();
                              }}
                          >
                              <IconFontView name={"scan"} size={Sizes.dp18} color={Colors.mainColor} style={{ marginLeft: Sizes.dp16 }} />
                          </AbcView>
                      )
                    : undefined,
                formatter: NumberOnlyFormatter(),
                onChanged: (QRCode: string) => {
                    this.bloc.requestModifyQRCode(QRCode);
                },
            },
            {
                itemStyleType: ListSettingItemType.listSettingEditItem,
                title: "商品编码",
                editable: canEdit,
                contentHint: canEdit ? "系统生成或自定义" : "",
                content: goodsInfo.shortId ?? "",
                onChanged: (shortId: string) => {
                    this.bloc.requestUpdateMedicineShortId(shortId);
                },
                maxLength: 20,
            },
            {
                show: goodsInfo.isChineseMedicine && goodsInfo.cMSpec == "中药颗粒" && !userCenter.clinic?.isChainAdminClinic,
                itemStyleType: ListSettingItemType.listSettingRadiosItem,
                title: "智能发药",
                options: ["否", "是"],
                check: !!goodsInfo.smartDispense ? "是" : "否",
                enable: canEdit,
                onChanged: (text: string, index: number) => {
                    ignore(text);
                    this.bloc.requestModifySmartDispense(!!index);
                },
                contentStyle: { marginLeft: Sizes.listHorizontalMargin },
                marginBetweenItem: Sizes.dp48,
            },
        ];

        return (
            <View style={styles.groupContainer}>
                {baseInfoList.map((baseInfo, key) => {
                    if (baseInfo.show == false) return;
                    if (baseInfo.itemStyleType === ListSettingItemType.listSettingItem) {
                        return (
                            <ListSettingItem
                                key={key}
                                onClick={baseInfo.onClick}
                                style={baseInfo.style}
                                itemStyle={baseInfo.itemStyle}
                                title={baseInfo.title}
                                starTitle={baseInfo.starTitle}
                                content={baseInfo.content}
                                contentHint={baseInfo.contentHint}
                                contentTextStyle={_contentTextStyle}
                                bottomLine={true}
                                showErrorBorder={baseInfo.showErrorBorder}
                            />
                        );
                    } else if (baseInfo.itemStyleType === ListSettingItemType.listSettingEditItem) {
                        return (
                            <ListSettingEditItem
                                key={key}
                                title={baseInfo.title}
                                contentHint={baseInfo.contentHint}
                                content={baseInfo.content}
                                editable={baseInfo.editable}
                                onChanged={baseInfo.onChanged as (value: string) => void}
                                bottomLine={true}
                                textInputStyle={_contentTextStyle}
                                endIcon={baseInfo.endIcon}
                            />
                        );
                    } else if (baseInfo.itemStyleType === ListSettingItemType.listSettingRadiosItem) {
                        return (
                            <ListSettingRadiosItem
                                key={key}
                                title={baseInfo.title}
                                options={baseInfo.options as string[]}
                                bottomLine={true}
                                check={baseInfo.check}
                                enable={baseInfo.enable}
                                onChanged={baseInfo.onChanged as (option: string, index: number) => void}
                                contentStyle={baseInfo.contentStyle}
                                marginBetweenItem={baseInfo.marginBetweenItem}
                            />
                        );
                    }
                })}
            </View>
        );
    }

    /**
     * 药品规格（剂量模式/容量模式）
     * @private
     */
    private _renderGoodsInfoPackageView(): JSX.Element {
        const state = this.bloc.currentState,
            goodsInfo = state.detail;
        const { showErrorHint, canEdit } = state;
        const _disableStyle = TextStyles.t14NT3.copyWith({ color: Colors.t3 });
        const _textStyle = TextStyles.t14NT3.copyWith({ color: canEdit ? Colors.T1 : Colors.t3 });
        const _itemStyle = canEdit ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal;
        const _placeholderColor = canEdit ? Colors.t4 : Colors.t3;
        if (goodsInfo.specType != null && goodsInfo.specType == 1) {
            // for 容量有效成分
            return (
                <View
                    style={{
                        justifyContent: "center",
                        paddingVertical: Sizes.dp12,
                        backgroundColor: Colors.white,
                        paddingHorizontal: Sizes.listHorizontalMargin,
                    }}
                >
                    <ListSettingItem
                        key="spec-volume"
                        height={Sizes.dp24}
                        itemStyle={_itemStyle}
                        title={"规格"}
                        content={"容量:成分含量*制剂/包装"}
                        contentHint={"请选择"}
                        onClick={() => {
                            if (!canEdit) return;
                            this.bloc.requestSelectSpecification();
                        }}
                    />

                    <Text style={[TextStyles.t12NT3.copyWith({ color: Colors.t3 }), { marginLeft: Sizes.dp80 }]}>
                        {"示例： 1ml : 0.5mg * 8支 / 盒"}
                    </Text>

                    <View style={[ABCStyles.rowAlignCenter, { flex: 1, marginTop: Sizes.dp14, justifyContent: "flex-start" }]}>
                        <SizedBox width={Sizes.dp62} />
                        <CustomInput
                            value={goodsInfo.componentContentNum}
                            formatter={PrecisionLimitFormatter(InventoryConst.medicineDosageNumPrescription)}
                            placeholder={"容量"}
                            placeholderColor={_placeholderColor}
                            style={{ width: Sizes.dp64, borderBottomWidth: !canEdit ? 1 : undefined, borderColor: Colors.P1 }}
                            borderType={"bottom"}
                            onChange={(value) => this.bloc.requestUpdateComponentNum(StringUtils.parseFloat(value)!)}
                            disable={!canEdit}
                            disableStyle={_disableStyle}
                        />

                        <_RadiusButton
                            width={Sizes.dp54}
                            text={goodsInfo.componentContentUnit ?? "单位"}
                            textStyle={!!goodsInfo.componentContentUnit ? _textStyle : _textStyle.copyWith({ color: _placeholderColor })}
                            onClick={() => {
                                if (!canEdit) return;
                                this.bloc.requestSelectComponentUnit();
                            }}
                        />

                        <Text style={TextStyles.t16NT2}>:</Text>

                        <CustomInput
                            borderType={"bottom"}
                            placeholder={"成分含量"}
                            placeholderColor={_placeholderColor}
                            value={goodsInfo.medicineDosageNum}
                            style={{ width: Sizes.dp64, borderBottomWidth: !canEdit ? 1 : undefined, borderColor: Colors.P1 }}
                            formatter={PrecisionLimitFormatter(InventoryConst.medicineDosageNumPrescription)}
                            onChange={(value) => this.bloc.requestUpdateDosageNum(StringUtils.parseFloat(value)!)}
                            disable={!canEdit}
                            disableStyle={_disableStyle}
                        />

                        <_RadiusButton
                            width={Sizes.dp54}
                            text={goodsInfo.medicineDosageUnit ?? "单位"}
                            textStyle={!!goodsInfo.medicineDosageUnit ? _textStyle : _textStyle.copyWith({ color: _placeholderColor })}
                            borderColor={showErrorHint && _.isEmpty(goodsInfo.pieceUnit) ? Colors.R2 : undefined}
                            onClick={() => {
                                if (!canEdit) return;
                                const initIndex: Set<number> = new Set();
                                const initItem = WesternMedicine.dosageUnit.findIndex((item) => item.name == goodsInfo.medicineDosageUnit);
                                if (!_.isUndefined(initItem) && initItem > -1) {
                                    initIndex.add(initItem);
                                }
                                showOptionsBottomSheet({
                                    title: "选择单位",
                                    options: WesternMedicine.dosageUnit.map((item) => item.name),
                                    initialSelectIndexes: initIndex,
                                }).then((result) => {
                                    if (result && result.length) {
                                        this.bloc.requestModifyDosageUnit(WesternMedicine.dosageUnit[result[0]].name);
                                    }
                                });
                            }}
                        />

                        <Text style={TextStyles.t16NT6}>*</Text>
                    </View>

                    <View style={[ABCStyles.rowAlignCenter, { flex: 1, justifyContent: "flex-start" }]}>
                        <SizedBox width={Sizes.dp62} />
                        <CustomInput
                            value={goodsInfo.pieceNum}
                            formatter={[LengthLimitingTextInputFormatter(8), PrecisionLimitFormatter(0)]}
                            placeholder={"制剂"}
                            placeholderColor={_placeholderColor}
                            error={showErrorHint && goodsInfo.pieceNum == undefined}
                            style={{ width: Sizes.dp64, borderBottomWidth: !canEdit ? 1 : undefined, borderColor: Colors.P1 }}
                            borderType={"bottom"}
                            onChange={(value) => this.bloc.requestUpdatePieceNum(StringUtils.parseFloat(value)!)}
                            disable={!canEdit}
                            disableStyle={_disableStyle}
                        />

                        <_RadiusButton
                            width={Sizes.dp54}
                            text={goodsInfo.pieceUnit ?? "单位"}
                            error={showErrorHint && goodsInfo.pieceNum == undefined}
                            textStyle={!!goodsInfo.pieceUnit ? _textStyle : _textStyle.copyWith({ color: _placeholderColor })}
                            borderColor={showErrorHint && _.isEmpty(goodsInfo.pieceUnit) ? Colors.errorBorder : undefined}
                            onClick={() => {
                                if (!canEdit) return;
                                this.bloc.requestSelectPieceUnit();
                            }}
                        />

                        <Text style={TextStyles.t16NT6}>/</Text>

                        <_RadiusButton
                            width={Sizes.dp54}
                            text={goodsInfo.packageUnit ?? "包装"}
                            textStyle={!!goodsInfo.packageUnit ? _textStyle : _textStyle.copyWith({ color: _placeholderColor })}
                            borderColor={showErrorHint && _.isEmpty(goodsInfo.packageUnit) ? Colors.errorBorder : undefined}
                            onClick={() => {
                                if (!canEdit) return;
                                this.bloc.requestSelectPackageUnit();
                            }}
                        />
                    </View>
                </View>
            );
        } else {
            return (
                <View
                    style={{
                        backgroundColor: Colors.white,
                        paddingVertical: Sizes.dp12,
                        justifyContent: "center",
                        paddingHorizontal: Sizes.listHorizontalMargin,
                    }}
                >
                    <ListSettingItem
                        key="spec-dosage"
                        height={Sizes.dp24}
                        itemStyle={_itemStyle}
                        title={"规格"}
                        content={"剂量*制剂/包装"}
                        contentHint={"请选择"}
                        onClick={() => {
                            if (!canEdit) return;
                            this.bloc.requestSelectSpecification();
                        }}
                    />

                    <Text style={[TextStyles.t12NT3.copyWith({ color: Colors.t3 }), { marginLeft: Sizes.dp80 }]}>
                        {"示例：50mg * 10片 / 盒"}
                    </Text>

                    <View style={[ABCStyles.rowAlignCenter, { marginTop: Sizes.dp14 }]}>
                        <View style={[ABCStyles.rowAlignCenter, { flex: 1, justifyContent: "flex-end" }]}>
                            <CustomInput
                                value={goodsInfo.medicineDosageNum}
                                formatter={PrecisionLimitFormatter(InventoryConst.medicineDosageNumPrescription)}
                                placeholder={"剂量"}
                                placeholderColor={_placeholderColor}
                                style={{ width: Sizes.dp54, borderBottomWidth: !canEdit ? 1 : undefined, borderColor: Colors.P1 }}
                                borderType={"bottom"}
                                onChange={(text) => {
                                    this.bloc.requestModifyDosage(text);
                                }}
                                disable={!canEdit}
                                disableStyle={_disableStyle}
                            />

                            <_RadiusButton
                                text={goodsInfo.medicineDosageUnit ?? "单位"}
                                textStyle={!!goodsInfo.medicineDosageUnit ? _textStyle : _textStyle.copyWith({ color: _placeholderColor })}
                                width={Sizes.dp48}
                                onClick={() => {
                                    if (!canEdit) return;
                                    const initIndex: Set<number> = new Set();
                                    const initItem = WesternMedicine.dosageUnit.findIndex(
                                        (item) => item.name == goodsInfo.medicineDosageUnit
                                    );
                                    if (!_.isUndefined(initItem) && initItem > -1) {
                                        initIndex.add(initItem);
                                    }
                                    showOptionsBottomSheet({
                                        title: "选择剂量单位",
                                        options: WesternMedicine.dosageUnit.map((item) => item.name),
                                        initialSelectIndexes: initIndex,
                                    }).then((result) => {
                                        if (result && result.length) {
                                            this.bloc.requestModifyDosageUnit(WesternMedicine.dosageUnit[result[0]].name);
                                        }
                                    });
                                }}
                            />

                            <Text style={TextStyles.t16NT6}>*</Text>

                            <CustomInput
                                value={goodsInfo.pieceNum}
                                formatter={[LengthLimitingTextInputFormatter(8), PrecisionLimitFormatter(0)]}
                                placeholder={"制剂"}
                                placeholderColor={_placeholderColor}
                                error={showErrorHint && goodsInfo.pieceNum == undefined}
                                style={{ width: Sizes.dp54, borderBottomWidth: !canEdit ? 1 : undefined, borderColor: Colors.P1 }}
                                borderType={"bottom"}
                                onChange={(text) => {
                                    this.bloc.requestModifyPieceNum(text);
                                }}
                                disable={!canEdit}
                                disableStyle={_disableStyle}
                            />

                            <_RadiusButton
                                width={Sizes.dp48}
                                text={goodsInfo.pieceUnit ?? "单位"}
                                error={showErrorHint && _.isEmpty(goodsInfo.pieceUnit)}
                                textStyle={!!goodsInfo.pieceUnit ? _textStyle : _textStyle.copyWith({ color: _placeholderColor })}
                                borderColor={showErrorHint && _.isEmpty(goodsInfo.pieceUnit) ? Colors.errorBorder : undefined}
                                onClick={() => {
                                    if (!canEdit) return;
                                    const initIndex: Set<number> = new Set();
                                    const initItem = WesternMedicine.medicineDosageFormUnit.findIndex(
                                        (item) => item.name == goodsInfo.pieceUnit
                                    );
                                    if (!_.isUndefined(initItem) && initItem > -1) {
                                        initIndex.add(initItem);
                                    }
                                    showOptionsBottomSheet({
                                        title: "选择制剂单位",
                                        options: WesternMedicine.medicineDosageFormUnit.map((item) => item.name),
                                        initialSelectIndexes: initIndex,
                                    }).then((result) => {
                                        if (result && result.length) {
                                            this.bloc.requestModifyPieceUnit(WesternMedicine.medicineDosageFormUnit[result[0]].name);
                                        }
                                    });
                                }}
                            />
                            <Text style={TextStyles.t16NT6}>/</Text>

                            <_RadiusButton
                                text={goodsInfo.packageUnit ?? "包装"}
                                error={showErrorHint && _.isEmpty(goodsInfo.packageUnit)}
                                textStyle={!!goodsInfo.packageUnit ? _textStyle : _textStyle.copyWith({ color: _placeholderColor })}
                                borderColor={showErrorHint && _.isEmpty(goodsInfo.packageUnit) ? Colors.errorBorder : undefined}
                                width={Sizes.dp48}
                                onClick={() => {
                                    if (!canEdit) return;
                                    const initIndex: Set<number> = new Set();
                                    const initItem = WesternMedicine.materialUnit.findIndex((item) => item.name == goodsInfo.packageUnit);
                                    if (!_.isUndefined(initItem) && initItem > -1) {
                                        initIndex.add(initItem);
                                    }
                                    showOptionsBottomSheet({
                                        title: "选择包装单位",
                                        options: WesternMedicine.materialUnit.map((item) => item.name),
                                        initialSelectIndexes: initIndex,
                                    }).then((result) => {
                                        if (result && result.length) {
                                            this.bloc.requestModifyPackageUnit(WesternMedicine.materialUnit[result[0]].name);
                                        }
                                    });
                                }}
                            />
                        </View>
                    </View>
                </View>
            );
        }
    }
    private _renderConversionEquivalentView(): JSX.Element {
        const state = this.bloc.currentState,
            canEdit = state.canEdit,
            goodsInfo = state.detail;
        const disabledInput = !!goodsInfo.pieceUnit && !["g", "克"].includes(goodsInfo.pieceUnit);

        return (
            <View
                style={[
                    ABCStyles.rowAlignCenter,
                    ABCStyles.bottomLine,
                    { paddingVertical: Sizes.dp12, backgroundColor: Colors.white, paddingHorizontal: Sizes.listHorizontalMargin },
                ]}
            >
                <View style={[ABCStyles.rowAlignCenter, { marginRight: Sizes.dp16 }]}>
                    <Text style={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}>{"换算当量"}</Text>
                </View>
                <View style={[ABCStyles.rowAlignCenter]}>
                    <Text>1g 颗粒 = </Text>
                    <CustomInput
                        value={goodsInfo.eqCoefficient}
                        formatter={PrecisionLimitFormatter(2, 1000)}
                        placeholderColor={Colors.P1}
                        style={{ width: Sizes.dp48, height: Sizes.dp24 }}
                        disableStyle={{ fontSize: Sizes.dp14 }}
                        borderType={"bottom"}
                        disable={disabledInput || !canEdit}
                        disablePlaceholderText={""}
                        onChange={(value) => {
                            this.bloc.requestModifyEqCoefficient(StringUtils.parseFloat(value));
                        }}
                    />
                    <Text style={{ marginLeft: Sizes.dp8 }}>g 饮片</Text>
                </View>
            </View>
        );
    }

    /**
     * 商品规格
     * @private
     */
    private _renderGoodsSpecInfo(): JSX.Element {
        const state = this.bloc.currentState,
            canEdit = state.canEdit,
            goodsInfo = state.detail;
        if (!goodsInfo) return <View />;

        const _contentTextStyle = TextStyles.t16NT3.copyWith({ color: !canEdit ? Colors.t3 : Colors.T1 });
        const goodsSpecInfoList = [
            {
                itemStyleType: ListSettingItemType.listSettingEditItem,
                title: "规格",
                content: goodsInfo.extendSpec,
                textInputStyle: TextStyles.t16NT1.copyWith({ color: !canEdit ? Colors.t3 : Colors.T1 }),
                editable: canEdit,
                contentHint: canEdit ? "输入规格" : "",
                onChanged: (text: string) => {
                    if (!canEdit) return;
                    this.bloc.requestModifyExtendSpec(text);
                },
                bottomLine: true,
            },
            {
                itemStyleType: ListSettingItemType.listSettingItem,
                title: "计价单位",
                starTitle: "*",
                content: goodsInfo.pieceUnit,
                contentTextStyle: _contentTextStyle,
                bottomLine: true,
                onClick: () => {
                    if (!canEdit && !canEdit) return;
                    const initIndex: Set<number> = new Set();
                    const initItem = WesternMedicine.medicinePricingUnit.findIndex((item) => item.name == goodsInfo.pieceUnit);
                    if (!_.isUndefined(initItem) && initItem > -1) {
                        initIndex.add(initItem);
                    }
                    showOptionsBottomSheet({
                        title: "选择计价单位",
                        options: WesternMedicine.medicinePricingUnit.map((item) => item.name),
                        initialSelectIndexes: initIndex,
                    }).then((result) => {
                        if (result && result.length) {
                            this.bloc.requestModifyPieceUnit(WesternMedicine.medicinePricingUnit[result[0]].name);
                        }
                    });
                },
            },
        ];
        //  南宁-中药类型-剂量
        const showDosage = userCenter.shebaoConfig?.isRegionNanning;
        // 山东济南---中药饮片（配方饮片、非配方饮片）显示【单X重量】(子店可以直接编辑，编辑后就独立了，不再继承总部的)
        const showPieceWeight =
            userCenter.shebaoConfig?.isShandongJinan &&
            (goodsInfo.isMedicineChinesePiece || goodsInfo.isMedicineChineseNonFormula) &&
            !!goodsInfo?.pieceUnit &&
            goodsInfo?.pieceUnit != "克" &&
            goodsInfo?.pieceUnit != "g";
        return (
            <View style={styles.groupContainer}>
                {goodsSpecInfoList.map((goodsSpecInfo) => {
                    if (goodsSpecInfo.itemStyleType == ListSettingItemType.listSettingItem) {
                        return (
                            <ListSettingItem
                                key={goodsSpecInfo.title}
                                title={goodsSpecInfo.title}
                                starTitle={goodsSpecInfo.starTitle}
                                content={goodsSpecInfo.content}
                                contentTextStyle={_contentTextStyle}
                                bottomLine={true}
                                onClick={goodsSpecInfo.onClick}
                            />
                        );
                    } else if (goodsSpecInfo.itemStyleType == ListSettingItemType.listSettingEditItem) {
                        return (
                            <ListSettingEditItem
                                key={goodsSpecInfo.title}
                                title={goodsSpecInfo.title}
                                content={goodsSpecInfo.content}
                                textInputStyle={goodsSpecInfo.textInputStyle}
                                editable={goodsSpecInfo.editable}
                                contentHint={goodsSpecInfo.contentHint}
                                onChanged={goodsSpecInfo.onChanged}
                                bottomLine={true}
                            />
                        );
                    }
                })}
                {showDosage && (
                    <View
                        style={{
                            ...ABCStyles.rowAlignCenter,
                            paddingVertical: Sizes.dp12,
                            backgroundColor: Colors.white,
                        }}
                    >
                        <AbcText size={"large"} theme={"t2"} style={{ minWidth: Sizes.dp80 }}>
                            剂量
                        </AbcText>
                        <CustomInput
                            disable={!canEdit}
                            value={goodsInfo.medicineDosageNum}
                            formatter={PrecisionLimitFormatter(InventoryConst.medicineDosageNumPrescription)}
                            placeholder={"剂量"}
                            placeholderColor={Colors.P1}
                            style={{ width: Sizes.dp70 }}
                            borderType={"bottom"}
                            onChange={(value) => this.bloc.requestUpdateDosageNum(StringUtils.parseFloat(value)!)}
                        />
                        <SizedBox width={Sizes.dp12} />
                        <AbcTextInput
                            editable={canEdit}
                            style={{
                                width: Sizes.dp80,
                                ...TextStyles.t14NB.copyWith({ lineHeight: Sizes.dp18 }),
                                paddingHorizontal: DeviceUtils.isAndroid() ? -6 : undefined,
                                height: Sizes.dp28,
                                borderBottomWidth: 1,
                                borderColor: Colors.P1,
                                textAlign: "center",
                            }}
                            defaultValue={goodsInfo.medicineDosageUnit}
                            numberOfLines={1}
                            maxLength={20}
                            onChangeText={(t) => this.bloc.requestModifyDosageUnit(t)}
                        />
                    </View>
                )}
                {showPieceWeight && (
                    <InventoryPieceWeightView
                        pieceUnit={goodsInfo.pieceUnit}
                        pieceUnitWeight={goodsInfo.pieceUnitWeight}
                        onChange={(value) => this.bloc.requestUpdatePieceWeight(value)}
                    />
                )}
            </View>
        );
    }

    /**
     * 医用材料规格
     * @private
     */
    private _renderMaterialSpecView(): JSX.Element {
        const state = this.bloc.currentState,
            canEdit = state.canEdit,
            goodsInfo = state.detail;
        if (!goodsInfo) return <View />;
        const _textStyle = TextStyles.t14NT1.copyWith({ color: canEdit ? Colors.T1 : Colors.t3 });
        const _placeholderColor = canEdit ? Colors.t4 : Colors.t3;
        const _disableStyle = TextStyles.t14NT3.copyWith({ color: Colors.t3 });
        return (
            <View style={styles.groupContainer}>
                <ListSettingItem
                    key="material-spec"
                    title={"规格"}
                    minTitleWidth={Sizes.dp60}
                    contentStyle={[{ paddingVertical: Sizes.dp10 }]}
                    contentBuilder={() => {
                        return (
                            <View>
                                <View style={{ flexDirection: "row", alignItems: "center" }}>
                                    <CustomInput
                                        disable={!canEdit}
                                        key={"doasge"}
                                        borderType={"all"}
                                        type={"input-text"}
                                        placeholder={"规格"}
                                        placeholderColor={_placeholderColor}
                                        value={goodsInfo.materialSpec}
                                        style={{ flex: 3 }}
                                        onChange={(text) => {
                                            this.bloc.requestModifyMaterialSpec(text.trim());
                                        }}
                                        disableStyle={_textStyle}
                                    />
                                    <Text style={[TextStyles.t14NT2.copyWith({ lineHeight: Sizes.dp24 }), { marginHorizontal: Sizes.dp4 }]}>
                                        *
                                    </Text>
                                    <CustomInput
                                        key={"piece"}
                                        disable={!canEdit}
                                        borderType={"all"}
                                        placeholder={"包装数量"}
                                        placeholderColor={_placeholderColor}
                                        error={state.showErrorHint && !goodsInfo.pieceNum}
                                        style={{ flex: 2 }}
                                        formatter={[PrecisionLimitFormatter(5), ZeroForbidden()]}
                                        value={goodsInfo.pieceNum}
                                        textStyle={_textStyle}
                                        onChange={(text) => {
                                            this.bloc.requestModifyPieceNum(text);
                                        }}
                                        disableStyle={_disableStyle}
                                    />
                                    <SizedBox width={Sizes.dp2} />
                                    <CustomInput
                                        key={"pieceUnit"}
                                        disable={!canEdit}
                                        borderType={"all"}
                                        type={"sheet"}
                                        placeholder={"单位"}
                                        placeholderColor={_placeholderColor}
                                        style={{ flex: 2 }}
                                        value={goodsInfo.pieceUnit}
                                        error={state.showErrorHint && !goodsInfo.pieceUnit}
                                        onChange={() => {
                                            const initIndex: Set<number> = new Set();
                                            const initItem = WesternMedicine.materialDosageFormUnit.findIndex(
                                                (item) => item.name == goodsInfo.pieceUnit
                                            );
                                            if (!_.isUndefined(initItem) && initItem > -1) {
                                                initIndex.add(initItem);
                                            }
                                            showOptionsBottomSheet({
                                                title: "选择最小单位",
                                                options: WesternMedicine.materialDosageFormUnit.map((item) => item.name),
                                                initialSelectIndexes: initIndex,
                                            }).then((result) => {
                                                if (result && result.length) {
                                                    this.bloc.requestModifyPieceUnit(
                                                        WesternMedicine.materialDosageFormUnit[result[0]].name
                                                    );
                                                }
                                            });
                                        }}
                                        disableStyle={_disableStyle}
                                    />
                                    <Text style={[TextStyles.t14NT2.copyWith({ lineHeight: Sizes.dp24 }), { marginHorizontal: Sizes.dp4 }]}>
                                        /
                                    </Text>
                                    <CustomInput
                                        key={"packageUnit"}
                                        disable={!canEdit}
                                        borderType={"all"}
                                        type={"sheet"}
                                        placeholder={"包装单位"}
                                        placeholderColor={_placeholderColor}
                                        style={{ flex: 2 }}
                                        value={goodsInfo.packageUnit}
                                        error={state.showErrorHint && !goodsInfo.packageUnit}
                                        onChange={() => {
                                            const initIndex: Set<number> = new Set();
                                            const initItem = WesternMedicine.materialDosageFormUnit.findIndex(
                                                (item) => item.name == goodsInfo.packageUnit
                                            );
                                            if (!_.isUndefined(initItem) && initItem > -1) {
                                                initIndex.add(initItem);
                                            }
                                            showOptionsBottomSheet({
                                                title: "选择包装单位",
                                                options: WesternMedicine.materialDosageFormUnit.map((item) => item.name),
                                                initialSelectIndexes: initIndex,
                                            }).then((result) => {
                                                if (result && result.length) {
                                                    this.bloc.requestModifyPackageUnit(
                                                        WesternMedicine.materialDosageFormUnit[result[0]].name
                                                    );
                                                }
                                            });
                                        }}
                                        disableStyle={_disableStyle}
                                    />
                                </View>
                                <Text style={[TextStyles.t12NT3.copyWith({ color: Colors.t3 }), { marginTop: Sizes.dp14 }]}>
                                    {"示例：40mg * 8片 / 盒"}
                                </Text>
                            </View>
                        );
                    }}
                />
            </View>
        );
    }

    // 药品分类/二级分类
    private _renderGoodsInfoType(): JSX.Element {
        const bloc = this.bloc,
            state = bloc.currentState,
            canEdit = state.canEdit;
        const _contentTextStyle = TextStyles.t16NT3.copyWith({ color: !canEdit ? Colors.t3 : Colors.T1 });
        return (
            <View style={{ marginBottom: Sizes.dp8, backgroundColor: Colors.white }}>
                <ListSettingItem
                    key="medicine-type"
                    title={"药品类型"}
                    bottomLine={true}
                    content={state.detail?.displayTypeName}
                    contentTextStyle={_contentTextStyle}
                />
                <ListSettingItem
                    key="secondary-classification"
                    title={"二级分类"}
                    contentHint={canEdit ? "请选择二级分类" : ""}
                    bottomLine={true}
                    content={state.detail.customTypeName}
                    contentTextStyle={_contentTextStyle}
                    onClick={() => {
                        if (!canEdit) return;
                        this.bloc.requestModifySecondaryClassification();
                    }}
                    itemStyle={canEdit ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal}
                />
            </View>
        );
    }

    /**
     * 药品（定价）
     * @private
     */
    private _renderMedicinePriceView(): JSX.Element {
        const state = this.bloc.currentState,
            goodsInfo = state.detail;
        const { isHospital, canEdit, isGspWaitVerifyStatus, isOpenPriceAdjustmentApplication } = state;
        const isPurchasePriceType = goodsInfo.priceType == SubClinicPricePriceMode.purchaseMarkup;
        const medicinePriceInfoList = [
            {
                show: goodsInfo.isMedicineWestAndChinesePatent && !isPurchasePriceType,
                itemStyleType: ListSettingItemType.listSettingItem,
                title: "零售价格",
                endIcon: () => (
                    <Text style={[TextStyles.t14NT2, { marginLeft: Sizes.dp6, lineHeight: Sizes.dp20 }]}>
                        {!userCenter.clinic?.isNormalClinic
                            ? state.showSubSetPrice
                                ? state.packagePriceLabel.length || state.chainPackagePriceRange.length
                                    ? `范围 ${
                                          userCenter.clinic?.isChainAdminClinic
                                              ? `${state.packagePriceLabel}`
                                              : `${state.chainPackagePriceRange}`
                                      }`
                                    : ""
                                : "不支持门店自主定价"
                            : ""}
                    </Text>
                ),
                showErrorBorder: state.showErrorHint && !state.validatePackageUnitPrice().validate,
                bottomLine: true,
                contentBuilder: () => {
                    const _editable = !(userCenter.clinic?.isChainSubClinic && !state.showSubSetPrice) && !goodsInfo.v2DisableStatus;
                    return (
                        <AbcFlexInputPrice
                            unit={goodsInfo.packageUnit}
                            editable={_editable && !isGspWaitVerifyStatus}
                            defaultValue={GoodsPriceUtils.inventoryMedicineBaseInfoPriceWithRMB({
                                price: goodsInfo.packagePrice ?? 0,
                            })}
                            formatter={PrecisionLimitFormatter(4)}
                            customKeyboardBuilder={new NumberKeyboardBuilder({})}
                            style={{
                                underlineColorAndroid: Colors.white,
                                backgroundColor: Colors.white,
                                height: Sizes.listItemHeight,
                                ...(_editable ? TextStyles.t16MT1 : TextStyles.t16NT3.copyWith({ color: Colors.t3 })),
                            }}
                            showPriceIcon={false}
                            contentStyle={{ justifyContent: "flex-start" }}
                            onChangeText={(num) => this.bloc.requestModifyPackagePrice(num)}
                        />
                    );
                },
            },
            {
                show: !!goodsInfo.isSell && (goodsInfo.isMedicineWestAndChinesePatent || goodsInfo.isMedicalMaterial || goodsInfo.isGoods),
                itemStyleType: ListSettingItemType.listSettingRadiosItem,
                title: "是否拆零",
                options: ["是", "否"],
                bottomLine: true,
                marginBetweenItem: Sizes.dp48,
                check: goodsInfo.dismounting ? "是" : "否",
                enable: canEdit,
                onChanged: (status: string, index: number) => {
                    ignore(status);
                    this.bloc.requestModifyDismountingStatus(index == 0);
                },
            },
            {
                show: !!goodsInfo.dismounting || goodsInfo.isChineseMedicine,
                itemStyleType: ListSettingItemType.listSettingItem,
                title: goodsInfo.isChineseMedicine ? "零售价格" : "拆零价",
                endIcon: () =>
                    !isPurchasePriceType ? (
                        <Text style={[TextStyles.t14NT2, { marginLeft: Sizes.dp6, lineHeight: Sizes.dp20 }]}>
                            {!userCenter.clinic?.isNormalClinic && state.showSubSetPrice && goodsInfo.piecePrice
                                ? `范围 ${
                                      userCenter.clinic?.isChainAdminClinic ? `${state.piecePriceLabel}` : `${state.chainPiecePriceRange}`
                                  }`
                                : ""}
                        </Text>
                    ) : (
                        <View />
                    ),
                showErrorBorder: state.showErrorHint && !state.validatePriceUnitPrice().validate,
                contentBuilder: () => {
                    if (isPurchasePriceType) {
                        return (
                            <Text style={[TextStyles.t16NT3, { marginLeft: Sizes.dp6, lineHeight: Sizes.dp20 }]}>
                                {state.showSubSetPrice ? `${goodsInfo.dismountPriceRangeStr}` : ""}
                            </Text>
                        );
                    }
                    const _editable =
                        !!state.detail.dismounting &&
                        !(userCenter.clinic?.isChainSubClinic && !state.showSubSetPrice) &&
                        !goodsInfo.v2DisableStatus &&
                        !isGspWaitVerifyStatus;
                    return (
                        <AbcFlexInputPrice
                            unit={goodsInfo.pieceUnit}
                            editable={_editable}
                            defaultValue={GoodsPriceUtils.inventoryMedicineBaseInfoPriceWithRMB({
                                price: goodsInfo.piecePrice ?? 0,
                            })}
                            formatter={PrecisionLimitFormatter(4)}
                            customKeyboardBuilder={new NumberKeyboardBuilder({})}
                            style={{
                                underlineColorAndroid: Colors.white,
                                backgroundColor: Colors.white,
                                height: Sizes.listItemHeight,
                                ...(_editable ? TextStyles.t16MT1 : TextStyles.t16NT3.copyWith({ color: Colors.t3 })),
                            }}
                            showPriceIcon={false}
                            contentStyle={{ justifyContent: "flex-start" }}
                            onChangeText={(num) => {
                                this.bloc.requestModifyPiecePrice(num);
                            }}
                        />
                    );
                },
                bottomLine: true,
            },
        ];
        const isShowMemberPrice =
            userCenter.clinic?.isDrugstoreButler && !(goodsInfo.isMaterialLogistics || goodsInfo.isMaterialFixedAssets);
        // 是否可以编辑会员价（调价申请未开通并且零售价格存在的情况下）
        const canEditMemberPrice = !isOpenPriceAdjustmentApplication && !!goodsInfo.packagePrice;
        // 提示会员价需在零售价格存在的情况下
        const showMemberPriceHint = !isOpenPriceAdjustmentApplication && !goodsInfo.packagePrice;
        return (
            <View style={styles.groupContainer}>
                {medicinePriceInfoList.map((medicinePriceInfo) => {
                    if (medicinePriceInfo.show == false) return;
                    if (medicinePriceInfo.itemStyleType == ListSettingItemType.listSettingItem) {
                        return (
                            <ListSettingItem
                                key={medicinePriceInfo.title}
                                title={medicinePriceInfo.title}
                                endIcon={medicinePriceInfo.endIcon}
                                showErrorBorder={medicinePriceInfo.showErrorBorder}
                                bottomLine={true}
                                contentBuilder={medicinePriceInfo.contentBuilder}
                            />
                        );
                    } else if (medicinePriceInfo.itemStyleType == ListSettingItemType.listSettingRadiosItem) {
                        return (
                            <ListSettingRadiosItem
                                key={medicinePriceInfo.title}
                                title={medicinePriceInfo.title}
                                options={medicinePriceInfo.options as string[]}
                                bottomLine={true}
                                marginBetweenItem={medicinePriceInfo.marginBetweenItem}
                                check={medicinePriceInfo.check}
                                enable={medicinePriceInfo.enable}
                                onChanged={medicinePriceInfo.onChanged}
                            />
                        );
                    }
                })}
                {state.showTax && this._renderTaxView()}
                {isShowMemberPrice && (
                    <View>
                        <ListSettingItem
                            title={"会员价"}
                            content={goodsInfo?.memberPriceDesp ?? ""}
                            onClick={() => canEditMemberPrice && this.bloc.requestSelectMemberPrice()}
                            itemStyle={canEditMemberPrice ? ListSettingItemStyle.expandIcon : undefined}
                            bottomLine={true}
                            startIcon={() => {
                                if (!showMemberPriceHint) return <View />;
                                return (
                                    <AbcView
                                        style={{ marginLeft: Sizes.dp4 }}
                                        onClick={() => showConfirmDialog("", "会员价基于零售价生效，请先填写零售价").then()}
                                    >
                                        <IconFontView name={"info"} size={Sizes.dp16} color={Colors.P3} />
                                    </AbcView>
                                );
                            }}
                        />
                    </View>
                )}

                {/*{(goodsInfo.isMedicineWestAndChinesePatent ||*/}
                {/*    goodsInfo.isChineseMedicine ||*/}
                {/*    goodsInfo.isMedicalMaterial ||*/}
                {/*    goodsInfo.isGoods) && (*/}
                {/*    <ListSettingItem*/}
                {/*        title={"利润分类"}*/}
                {/*        content={goodsInfo._profitCategoryTypeDisplay}*/}
                {/*        contentHint={"选择利润分类"}*/}
                {/*        itemStyle={canEdit ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal}*/}
                {/*        onClick={() => this.bloc.requestSelectProfitCategorization()}*/}
                {/*        bottomLine={true}*/}
                {/*    />*/}
                {/*)}*/}

                {!!isHospital && (
                    <ListSettingItem
                        title={"费用类型"}
                        starTitle={"*"}
                        bottomLine={true}
                        content={state.detail?.feeTypeName}
                        contentTextStyle={TextStyles.t16NT3.copyWith({ color: !canEdit ? Colors.t3 : Colors.T1 })}
                        onClick={() => {
                            if (!canEdit) return;
                            this.bloc.requestModifyGoodsInfoFeeType();
                        }}
                    />
                )}
            </View>
        );
    }

    /**
     * 物资（定价）
     * @private
     */
    private _renderMaterialPriceView(): JSX.Element {
        const state = this.bloc.currentState,
            goodsInfo = state.detail;
        if (!goodsInfo.isMedicalMaterial && !goodsInfo.isMaterialDisinfectant) return <View />;
        return this._renderGoodsPriceView();
    }

    /**
     * 进销项税
     * @param options
     * @private
     */
    private _renderTaxView(): JSX.Element {
        const state = this.bloc.currentState,
            goodsInfo = state.detail;
        const _openMedicineInOutTax = goodsInfo.defaultInOutTax == 1;

        return (
            <View style={{ backgroundColor: Colors.white }}>
                <View
                    style={[ABCStyles.rowAlignCenter, ABCStyles.bottomLine, { paddingVertical: Sizes.dp12, backgroundColor: Colors.white }]}
                >
                    <View style={ABCStyles.rowAlignCenter}>
                        <Text style={[TextStyles.t16NT2.copyWith({ color: Colors.t2 })]}>{"默认进/销项税率"}</Text>
                        <AbcView
                            style={{ marginLeft: Sizes.dp4 }}
                            onClick={() => showConfirmDialog("", "请在「管理-定价和税率」设置").then()}
                        >
                            <IconFontView name={"info"} size={Sizes.dp16} color={Colors.P3} />
                        </AbcView>
                    </View>
                    <Spacer />
                    <AbcSwitch
                        checked={_openMedicineInOutTax}
                        disable={userCenter.clinic?.isChainSubClinic || goodsInfo.isGspWaitVerifyStatus}
                        onChange={(status) => this.bloc.requestModifyDefaultInOutTax(status ? 1 : 0)}
                    />
                </View>

                <View style={[ABCStyles.rowAlignCenter, ABCStyles.bottomLine, { height: Sizes.listItemHeight }]}>
                    <Text style={[TextStyles.t16NT2.copyWith({ color: Colors.t2 }), { marginRight: Sizes.dp16 }]}>{"进项税率"}</Text>
                    <AbcTextInput
                        editable={!_openMedicineInOutTax && !goodsInfo.isGspWaitVerifyStatus}
                        style={{
                            width: Sizes.dp48,
                            height: Sizes.dp20,
                            ...TextStyles.t14NT1.copyWith({ color: _openMedicineInOutTax ? Colors.T4 : Colors.T1 }),
                        }}
                        placeholder={"0"}
                        value={goodsInfo.inTaxRat?.toString()}
                        keyboardType={"numeric"}
                        multiline={false}
                        formatter={PrecisionLimitFormatter(0, 101)}
                        onChangeText={(inTax) => this.bloc.requestModifyInTaxRat(inTax)}
                    />
                    <Text style={[TextStyles.t16NT6, { marginLeft: Sizes.dp2 }]}>%</Text>

                    <Text style={{ flex: 1, textAlign: "center" }}>-</Text>

                    <View style={ABCStyles.rowAlignCenter}>
                        <Text style={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}>{"销项税率"}</Text>
                        <SizedBox width={Sizes.dp16} />
                        <AbcTextInput
                            editable={!_openMedicineInOutTax && !goodsInfo.isGspWaitVerifyStatus}
                            style={{
                                width: Sizes.dp48,
                                height: Sizes.dp20,
                                ...TextStyles.t14NT1.copyWith({ color: _openMedicineInOutTax ? Colors.T4 : Colors.T1 }),
                            }}
                            multiline={false}
                            keyboardType={"numeric"}
                            placeholder={"0"}
                            value={goodsInfo.outTaxRat?.toString() ?? ""}
                            formatter={PrecisionLimitFormatter(0, 101)}
                            onChangeText={(outTax) => this.bloc.requestModifyOutTaxRat(outTax)}
                        />
                        <Text style={[TextStyles.t16NT6, { marginLeft: Sizes.dp2 }]}>%</Text>
                    </View>
                </View>
            </View>
        );
    }

    private _renderMedicineSpecInfoView(): JSX.Element {
        const state = this.bloc.currentState,
            goodsInfo = state.detail;
        if (goodsInfo.isChineseMedicine) {
            return this._renderGoodsSpecInfo();
        } else if (goodsInfo.isMaterial || goodsInfo.isGoods) {
            return this._renderMaterialSpecView();
        } else {
            return this._renderGoodsInfoPackageView();
        }
    }

    private _renderMedicineChineseGranuleInfoView(): JSX.Element {
        const state = this.bloc.currentState,
            goodsInfo = state.detail;
        if (
            userCenter.enableEqCoefficient &&
            goodsInfo.isMedicineChineseGranule &&
            (goodsInfo.pieceUnit === "g" || goodsInfo.pieceUnit === "克")
        ) {
            return this._renderConversionEquivalentView();
        }

        return <View />;
    }

    /**
     * 商品（定价）
     * @private
     */
    private _renderGoodsPriceView(): JSX.Element {
        const state = this.bloc.currentState,
            canEdit = state.canEdit,
            goodsInfo = state.detail;
        const isPurchasePriceType = goodsInfo.priceType == SubClinicPricePriceMode.purchaseMarkup;
        const isShowMemberPrice =
            userCenter.clinic?.isDrugstoreButler && !(goodsInfo.isMaterialLogistics || goodsInfo.isMaterialFixedAssets);
        // 是否可以编辑会员价（零售价格存在的情况下）
        const canEditMemberPrice = !!goodsInfo.packagePrice;
        const goodsInfoList = [
            {
                itemStyleType: ListSettingItemType.listSettingRadiosItem,
                title: "允许对外销售",
                options: ["是", "否"],
                contentStyle: { marginLeft: Sizes.listHorizontalMargin },
                marginBetweenItem: Sizes.dp48,
                check: goodsInfo.isSell ? "是" : "否",
                enable: canEdit,
                onChanged: (status: string, index: number) => {
                    ignore(status);
                    this.bloc.requestModifyIsSellStatus(index == 0);
                },
            },
            {
                show: !!goodsInfo.isSell && !isPurchasePriceType,
                itemStyleType: ListSettingItemType.listSettingItem,
                title: "零售价格",
                endIcon: () => (
                    <Text style={[TextStyles.t14NT2, { marginLeft: Sizes.dp6, lineHeight: Sizes.dp20 }]}>
                        {!userCenter.clinic?.isNormalClinic
                            ? state.showSubSetPrice
                                ? state.packagePriceLabel.length || state.chainPackagePriceRange.length
                                    ? `范围 ${
                                          userCenter.clinic?.isChainAdminClinic
                                              ? `${state.packagePriceLabel}`
                                              : `${state.chainPackagePriceRange}`
                                      }`
                                    : ""
                                : "不支持门店自主定价"
                            : ""}
                    </Text>
                ),
                showErrorBorder: state.showErrorHint && !state.validatePackageUnitPrice().validate,
                contentBuilder: () => {
                    const _editable =
                        !(userCenter.clinic?.isChainSubClinic && state.showSubSetPrice && state.detail.v2DisableStatus) &&
                        !state.isGspWaitVerifyStatus;
                    return (
                        <AbcFlexInputPrice
                            unit={goodsInfo.packageUnit}
                            editable={_editable}
                            defaultValue={GoodsPriceUtils.inventoryMedicineBaseInfoPriceWithRMB({
                                price: goodsInfo.packagePrice ?? 0,
                            })}
                            formatter={PrecisionLimitFormatter(4)}
                            customKeyboardBuilder={new NumberKeyboardBuilder({})}
                            style={{
                                underlineColorAndroid: Colors.white,
                                backgroundColor: Colors.white,
                                height: Sizes.listItemHeight,
                                ...(_editable ? TextStyles.t16MT1 : TextStyles.t16NT3.copyWith({ color: Colors.t3 })),
                            }}
                            showPriceIcon={false}
                            contentStyle={{ justifyContent: "flex-start" }}
                            onChangeText={(num) => this.bloc.requestModifyPackagePrice(num)}
                        />
                    );
                },
            },
            {
                show:
                    !!goodsInfo.isSell &&
                    !goodsInfo.isChineseMedicine &&
                    !goodsInfo.isMaterialLogistics &&
                    !goodsInfo.isMaterialFixedAssets,
                itemStyleType: ListSettingItemType.listSettingRadiosItem,
                title: "是否拆零",
                options: ["是", "否"],
                marginBetweenItem: Sizes.dp48,
                check: goodsInfo.dismounting ? "是" : "否",
                enable: canEdit,
                onChanged: (status: string, index: number) => {
                    ignore(status);
                    this.bloc.requestModifyDismountingStatus(index == 0);
                },
            },
            {
                show: !!goodsInfo.isSell && !goodsInfo.isChineseMedicine && !!goodsInfo.dismounting,
                itemStyleType: ListSettingItemType.listSettingItem,
                title: "拆零价格",
                endIcon: () =>
                    !isPurchasePriceType ? (
                        <Text style={[TextStyles.t14NT2, { marginLeft: Sizes.dp6, lineHeight: Sizes.dp20 }]}>
                            {!userCenter.clinic?.isNormalClinic && state.showSubSetPrice && goodsInfo.piecePrice
                                ? `范围 ${
                                      userCenter.clinic?.isChainAdminClinic ? `${state.piecePriceLabel}` : `${state.chainPiecePriceRange}`
                                  }`
                                : ""}
                        </Text>
                    ) : (
                        <View />
                    ),
                showErrorBorder: state.showErrorHint && !state.validatePriceUnitPrice().validate,
                contentBuilder: () => {
                    if (isPurchasePriceType) {
                        return (
                            <Text style={[TextStyles.t16NT3, { marginLeft: Sizes.dp6, lineHeight: Sizes.dp20 }]}>
                                {state.showSubSetPrice ? `范围 ${goodsInfo.dismountPriceRangeStr}` : ""}
                            </Text>
                        );
                    }
                    const _editable =
                        !(userCenter.clinic?.isChainSubClinic && !state.showSubSetPrice) &&
                        !!state.detail.dismounting &&
                        !state.detail.v2DisableStatus;
                    return (
                        <AbcFlexInputPrice
                            unit={goodsInfo.pieceUnit}
                            editable={_editable}
                            defaultValue={GoodsPriceUtils.inventoryMedicineBaseInfoPriceWithRMB({
                                price: goodsInfo.piecePrice ?? 0,
                            })}
                            formatter={PrecisionLimitFormatter(4)}
                            customKeyboardBuilder={new NumberKeyboardBuilder({})}
                            style={{
                                underlineColorAndroid: Colors.white,
                                backgroundColor: Colors.white,
                                height: Sizes.listItemHeight,
                                ...(_editable ? TextStyles.t16MT1 : TextStyles.t16NT3.copyWith({ color: Colors.t3 })),
                            }}
                            showPriceIcon={false}
                            contentStyle={{ justifyContent: "flex-start" }}
                            onChangeText={(num) => {
                                if (!_editable) return;
                                this.bloc.requestModifyPiecePrice(num);
                            }}
                        />
                    );
                },
            },
            {
                show: !!state.isHospital,
                itemStyleType: ListSettingItemType.listSettingItem,
                title: "费用类型",
                starTitle: "*",
                content: state.detail?.feeTypeName,
                contentTextStyle: TextStyles.t16NT3.copyWith({ color: !canEdit ? Colors.t3 : Colors.T1 }),
                onClick: () => {
                    if (!canEdit) return;
                    this.bloc.requestModifyGoodsInfoFeeType();
                },
            },
        ];
        return (
            <View style={styles.groupContainer}>
                {goodsInfoList.map((goodsInfo) => {
                    if (goodsInfo.show == false) return;
                    if (goodsInfo.itemStyleType == ListSettingItemType.listSettingItem) {
                        return (
                            <ListSettingItem
                                key={goodsInfo.title}
                                title={goodsInfo.title}
                                starTitle={goodsInfo.starTitle}
                                endIcon={goodsInfo.endIcon}
                                showErrorBorder={goodsInfo.showErrorBorder}
                                bottomLine={true}
                                content={goodsInfo.content}
                                contentBuilder={goodsInfo.contentBuilder}
                                contentTextStyle={goodsInfo.contentTextStyle}
                                onClick={goodsInfo.onClick}
                            />
                        );
                    } else if (goodsInfo.itemStyleType == ListSettingItemType.listSettingRadiosItem) {
                        return (
                            <ListSettingRadiosItem
                                key={goodsInfo.title}
                                bottomLine={true}
                                title={goodsInfo.title}
                                options={goodsInfo.options as string[]}
                                contentStyle={goodsInfo.contentStyle}
                                marginBetweenItem={goodsInfo.marginBetweenItem}
                                check={goodsInfo.check}
                                enable={goodsInfo.enable}
                                onChanged={goodsInfo.onChanged}
                            />
                        );
                    }
                })}
                {state.showTax && !(goodsInfo.isMaterialLogistics || goodsInfo.isMaterialFixedAssets) && this._renderTaxView()}
                {isShowMemberPrice && (
                    <View>
                        <ListSettingItem
                            title={"会员价"}
                            content={goodsInfo?.memberPriceDesp ?? ""}
                            onClick={() => canEditMemberPrice && this.bloc.requestSelectMemberPrice()}
                            itemStyle={canEditMemberPrice ? ListSettingItemStyle.expandIcon : undefined}
                            bottomLine={true}
                            startIcon={() => {
                                if (canEditMemberPrice) return <View />;
                                return (
                                    <AbcView
                                        style={{ marginLeft: Sizes.dp4 }}
                                        onClick={() => showConfirmDialog("", "会员价基于零售价生效，请先填写零售价").then()}
                                    >
                                        <IconFontView name={"info"} size={Sizes.dp16} color={Colors.P3} />
                                    </AbcView>
                                );
                            }}
                        />
                    </View>
                )}
            </View>
        );
    }

    /**
     * 医保模块
     */
    renderHealthInsuranceModuleView(): JSX.Element {
        const state = this.bloc.currentState,
            goodsInfo = state.detail;
        const { medicalFeeGradeStr, covidDictFlagStr, sheBaoCodeSearchGoodsSelectItem, defaultMedicalInsuranceCodeStr } = state;
        const showSheBaoPayMode =
            goodsInfo.isMedicineWestAndChinesePatent ||
            goodsInfo.isChineseMedicine ||
            goodsInfo.isMedicalMaterial ||
            goodsInfo.isMaterialDisinfectant;
        return (
            <View style={styles.groupContainer}>
                {(goodsInfo.isWesternMedicine ||
                    goodsInfo.isChineseWesternMedicine ||
                    goodsInfo.isChineseMedicine ||
                    goodsInfo.isMedicalMaterial ||
                    goodsInfo.isMaterialDisinfectant) && (
                    <View style={sheBaoCodeSearchGoodsSelectItem ? ABCStyles.bottomLine : undefined}>
                        <ListSettingItem
                            title={"医保对码"}
                            onClick={() => this.bloc.requestUpdateMedicineSheBaoCode()}
                            itemStyle={ListSettingItemStyle.expandIcon}
                            bottomLine={!sheBaoCodeSearchGoodsSelectItem}
                            contentBuilder={() => {
                                const textColor =
                                    defaultMedicalInsuranceCodeStr !== "选择医保对码" &&
                                    defaultMedicalInsuranceCodeStr !== "不刷医保/暂无编码"
                                        ? Colors.T1
                                        : Colors.t4;
                                return (
                                    <View style={[ABCStyles.rowAlignCenter, { flex: 1, backgroundColor: Colors.S2 }]}>
                                        <View style={[ABCStyles.rowAlignCenter, { marginRight: Sizes.dp4, flexShrink: 1 }]}>
                                            <Text style={TextStyles.t16NT1.copyWith({ color: textColor })} numberOfLines={1}>
                                                {defaultMedicalInsuranceCodeStr}
                                            </Text>
                                        </View>

                                        {!!medicalFeeGradeStr && goodsInfo.shebao?.nationalCodeId && (
                                            <View
                                                style={[
                                                    ABCStyles.rowAlignCenter,
                                                    Sizes.paddingLTRB(Sizes.dp8, Sizes.dp2, Sizes.dp8, Sizes.dp2),
                                                    {
                                                        justifyContent: "center",
                                                        backgroundColor: Colors.sheBaoTag_bg,
                                                        borderRadius: Sizes.dp2,
                                                        marginRight: Sizes.dp4,
                                                    },
                                                ]}
                                            >
                                                <Text style={TextStyles.t12NT6}>{medicalFeeGradeStr}</Text>
                                            </View>
                                        )}

                                        {!!covidDictFlagStr && goodsInfo.shebao?.nationalCodeId && (
                                            <View
                                                style={[
                                                    ABCStyles.rowAlignCenter,
                                                    Sizes.paddingLTRB(Sizes.dp2, Sizes.dp2, Sizes.dp2, Sizes.dp2),
                                                    {
                                                        backgroundColor: Colors.sheBaoTag_bg,
                                                        borderRadius: Sizes.dp2,
                                                        marginRight: Sizes.dp4,
                                                    },
                                                ]}
                                            >
                                                <Text style={TextStyles.t12NT6}>{covidDictFlagStr}</Text>
                                            </View>
                                        )}
                                    </View>
                                );
                            }}
                        />
                        {goodsInfo.shebao?.nationalCodeId && (
                            <SheBaoCodeCardView
                                style={Sizes.marginLTRB(0, 0, 0, Sizes.dp12)}
                                sheBaoCardInfo={sheBaoCodeSearchGoodsSelectItem}
                                onClick={() => this.bloc.requestUpdateMedicineSheBaoCode()}
                            />
                        )}
                    </View>
                )}

                {showSheBaoPayMode && (
                    <ListSettingItem
                        title={"医保支付"}
                        startIcon={() => (
                            <AbcView
                                style={{ marginLeft: Sizes.dp4, marginRight: Sizes.dp16 }}
                                onClick={() =>
                                    showConfirmDialog(
                                        "",
                                        "统筹支付：结算时优先使用医保统筹基金进行支付\n" +
                                            "个账支付：结算时优先使用个人账户进行支付\n" +
                                            "不使用医保支付：仅对码，但收费时不使用医保进行支付",
                                        "确认",
                                        "",
                                        "left"
                                    ).then()
                                }
                            >
                                <IconFontView name={"info"} size={Sizes.dp16} color={Colors.P3} />
                            </AbcView>
                        )}
                        content={goodsInfo.displaySheBaoPayModeName}
                        contentHint={"选择医保支付"}
                        itemStyle={ListSettingItemStyle.expandIcon}
                        onClick={() => this.bloc.requestSelectMedicalInsurancePayment()}
                        bottomLine={true}
                    />
                )}
            </View>
        );
    }

    /**
     * 药品（扩展）
     */
    private _renderExtendedView(): JSX.Element {
        const state = this.bloc.currentState,
            goodsInfo = state.detail;
        const { showErrorHint, canEdit, pharmacyInfoConfig, isAllWarehouses } = state,
            isOpenMultiplePharmacy = !!pharmacyInfoConfig?.isOpenMultiplePharmacy;
        const _contentTextStyle = TextStyles.t16NT1.copyWith({ color: !canEdit ? Colors.t3 : Colors.T1 });
        const _itemStyle = canEdit ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal;
        const extendedInfoList = [
            {
                show:
                    userCenter.clinic?.isDrugstoreButler &&
                    (goodsInfo.isMedicineWestAndChinesePatent ||
                        goodsInfo.isChineseMedicine ||
                        goodsInfo.isMedicalMaterial ||
                        goodsInfo.isMaterialDisinfectant ||
                        goodsInfo.isCosmetics),
                itemStyleType: ListSettingItemType.listSettingItem,
                style: ABCStyles.bottomLine,
                title: "所属经营范围",
                content: goodsInfo._businessScopeDisplayName,
                contentHint: "选择所属经营范围",
                itemStyle: ListSettingItemStyle.expandIcon,
                onClick: () => canEdit && this.bloc.requestSelectBusinessScope(),
            },
            {
                show: goodsInfo.isMedicineWestAndChinesePatent || goodsInfo.isChineseMedicine,
                itemStyleType: ListSettingItemType.listSettingItem,
                title: "处方/OTC",
                content: goodsInfo._otcTypeDisplay,
                contentHint: canEdit ? "请选择处方OTC" : "",
                itemStyle: _itemStyle,
                onClick: () => {
                    if (!canEdit) return;
                    this.bloc.requestSelectOTCForm();
                },
                showErrorBorder: showErrorHint && goodsInfo.otcType == undefined,
            },
            {
                show: goodsInfo.isMedicineWestAndChinesePatent,
                itemStyleType: ListSettingItemType.listSettingItem,
                style: ABCStyles.bottomLine,
                title: "精麻毒放",
                content: goodsInfo._ingredientDisplay,
                contentHint: canEdit ? "请选择精麻毒放" : "",
                itemStyle: _itemStyle,
                onClick: () => {
                    if (!canEdit) return;
                    this.bloc.requestSelectNarcoticsDangerousDrugsRelease();
                },
            },
            {
                show: goodsInfo.isMedicineWestAndChinesePatent,
                itemStyleType: ListSettingItemType.listSettingItem,
                style: ABCStyles.bottomLine,
                title: "抗菌药物",
                content: goodsInfo._antibioticDisplay,
                contentHint: canEdit ? "请选择抗菌药物" : "",
                itemStyle: _itemStyle,
                onClick: () => {
                    if (!canEdit) return;
                    this.bloc.requestSelectAntibiotics();
                },
            },
            {
                show: goodsInfo.isMedicineWestAndChinesePatent,
                itemStyleType: ListSettingItemType.listSettingItem,
                title: "DDD值",
                style: ABCStyles.bottomLine,
                contentBuilder: () => {
                    const _unitArr = ["μg", "mg", "g", "U", "万U", "MU"];
                    return (
                        <View style={ABCStyles.rowAlignCenter}>
                            <CustomInput
                                borderType={"none"}
                                style={{ width: Sizes.dp70 }}
                                disable={!canEdit}
                                type={"numeric"}
                                placeholder={"0"}
                                placeholderColor={canEdit ? Colors.t4 : Colors.t3}
                                alwaysShowUtil={true}
                                value={goodsInfo.dddOfAntibiotic}
                                unit={goodsInfo.unitOfAntibiotic}
                                formatter={PrecisionLimitFormatter(3)}
                                onChange={(antibiotic) =>
                                    this.bloc.requestUpdateDefinedDailyDose({
                                        dddOfAntibiotic: Number(antibiotic),
                                        unitOfAntibiotic: goodsInfo.unitOfAntibiotic,
                                    })
                                }
                                alwaysHideUnit={false}
                                unitList={_unitArr}
                                onChangeUnit={(unitOfAntibiotic) =>
                                    this.bloc.requestUpdateDefinedDailyDose({
                                        dddOfAntibiotic: goodsInfo.dddOfAntibiotic,
                                        unitOfAntibiotic: unitOfAntibiotic,
                                    })
                                }
                                startUnitListView={
                                    <View
                                        style={{
                                            flexDirection: "row",
                                            marginLeft: Sizes.dp6,
                                            marginRight: Sizes.dp4,
                                            paddingVertical: Sizes.dp8,
                                        }}
                                    >
                                        <Text style={TextStyles.t14NT2.copyWith({ color: Colors.t2, lineHeight: Sizes.dp20 })}>
                                            {"单位切换"}
                                        </Text>
                                    </View>
                                }
                                unitListItemStyle={{ width: undefined, paddingHorizontal: Sizes.dp12 }}
                                disableColor={Colors.t3}
                            />
                            <Spacer />
                        </View>
                    );
                },
            },
            {
                show: goodsInfo.isMedicineWestAndChinesePatent || goodsInfo.isChineseMedicine,
                itemStyleType: ListSettingItemType.listSettingItem,
                title: "国家基药",
                content: goodsInfo._baseMedicineTypeDisplay,
                contentHint: canEdit ? "请选择国家基药" : "",
                style: ABCStyles.bottomLine,
                itemStyle: _itemStyle,
                onClick: () => {
                    if (!canEdit) return;
                    this.bloc.requestSelectBaseMedicineType();
                },
            },
            {
                show: goodsInfo.isMedicalMaterial,
                itemStyleType: ListSettingItemType.listSettingItem,
                title: "器械耗材分类",
                content: goodsInfo._deviceTypeDisplay,
                contentHint: canEdit ? "请选择器械耗材分类" : "",
                style: ABCStyles.bottomLine,
                itemStyle: _itemStyle,
                onClick: () => {
                    if (!canEdit) return;
                    this.bloc.requestSelectMedicalMaterialDeviceType();
                },
            },
            {
                show: goodsInfo.isMedicineWestAndChinesePatent || goodsInfo.isMedicalMaterial,
                itemStyleType: ListSettingItemType.listSettingItem,
                title: "上市许可持有人",
                content: goodsInfo.mha ?? "",
                contentHint: canEdit ? "输入上市许可持有人" : "",
                contentStyle: { marginLeft: Sizes.listHorizontalMargin },
                onClick: () => {
                    if (!state.canEdit) return;
                    this.bloc.requestModifyManufacturerFull("mha");
                },
                itemStyle: _itemStyle,
            },
            {
                show: goodsInfo.isMedicineWestAndChinesePatent || goodsInfo.isMedicalMaterial,
                itemStyleType: ListSettingItemType.listSettingItem,
                style: ABCStyles.bottomLine,
                title: "养护分类",
                content: goodsInfo._maintainTypeDisplay,
                contentHint: canEdit ? "选择养护分类" : "",
                itemStyle: _itemStyle,
                onClick: () => {
                    if (!canEdit) return;
                    this.bloc.requestSelectMaintenanceClassification();
                },
            },
            {
                show: goodsInfo.isMedicineWestAndChinesePatent || goodsInfo.isMedicalMaterial,
                itemStyleType: ListSettingItemType.listSettingItem,
                style: ABCStyles.bottomLine,
                title: "存储条件",
                content: goodsInfo.storage,
                contentHint: canEdit ? "选择存储条件" : "",
                itemStyle: _itemStyle,
                onClick: () => {
                    if (!canEdit) return;
                    this.bloc.requestSelectStorageConditions();
                },
            },
            {
                show:
                    goodsInfo.isMedicineWestAndChinesePatent ||
                    goodsInfo.isChineseMedicine ||
                    goodsInfo.isMedicalMaterial ||
                    goodsInfo.isMaterialDisinfectant,
                itemStyleType: ListSettingItemType.listSettingEditItem,
                title: "保质期",
                editable: canEdit,
                contentHint: canEdit ? "输入保质期" : "",
                content: goodsInfo.shelfLife?.toString(),
                formatter: [NumberOnlyFormatter(), LengthLimitingTextInputFormatter(6)],
                keyboardType: "phone-pad",
                onChanged: (value: string) => this.bloc.requestSelectExpirationDate(Number(value)),
                endIcon: () => (
                    <AbcView style={[ABCStyles.rowAlignCenter, { justifyContent: "flex-end" }]}>
                        <Text style={TextStyles.t16NT1}>{"个月"}</Text>
                    </AbcView>
                ),
            },
            {
                show: true,
                itemStyleType: ListSettingItemType.listSettingItem,
                style: ABCStyles.bottomLine,
                title: "标签",
                itemStyle: ListSettingItemStyle.expandIcon,
                contentBuilder: () => {
                    return <ListSettingEditItemInventoryInTagView selectTagList={goodsInfo.goodsTagList} />;
                },
                onClick: () => {
                    this.bloc.requestSelectTag();
                },
            },
            {
                show:
                    (userCenter.clinic?.isNormalClinic && !isOpenMultiplePharmacy) ||
                    (userCenter.clinic?.isNormalClinic && isOpenMultiplePharmacy && !isAllWarehouses) ||
                    (userCenter.clinic?.isChainSubClinic && !isOpenMultiplePharmacy) ||
                    (userCenter.clinic?.isChainSubClinic && isOpenMultiplePharmacy && !isAllWarehouses) ||
                    (userCenter.clinic?.isChainAdminClinic &&
                        InventoryAgent.getCurrentPharmacy()?.type == PharmacyType.normal &&
                        this.props?.isShowPosition),
                itemStyleType: ListSettingItemType.listSettingEditItem,
                title: "柜号",
                content: goodsInfo.position,
                contentHint: "输入柜号",
                onChanged: (text: string) => {
                    this.bloc.requestModifyPosition(text);
                },
            },
            {
                show:
                    goodsInfo.isMedicineWestAndChinesePatent ||
                    goodsInfo.isMedicalMaterial ||
                    goodsInfo.isGoods ||
                    goodsInfo.isMaterialDisinfectant,
                itemStyleType: ListSettingItemType.listSettingItem,
                style: ABCStyles.bottomLine,
                title: "批准文号有效期",
                content: goodsInfo._medicineNmpnExpiryDate,
                contentHint: canEdit ? "选择批准文号有效期" : "",
                itemStyle: _itemStyle,
                onClick: () => {
                    if (!canEdit) return;
                    this.bloc.requestSelectNpmnExpiryDate();
                },
            },
            {
                show: true,
                itemStyleType: ListSettingItemType.listSettingEditItem,
                title: "备注",
                content: goodsInfo.remark,
                contentHint: "输入备注",
                onChanged: (text: string) => {
                    this.bloc.requestUpdateMark(text);
                },
            },
        ];
        return (
            <View style={styles.groupContainer}>
                {extendedInfoList.map((extendedInfo) => {
                    if (extendedInfo.show == false) return;
                    if (extendedInfo.itemStyleType == ListSettingItemType.listSettingItem) {
                        return (
                            <ListSettingItem
                                key={extendedInfo.title}
                                style={extendedInfo.style}
                                contentTextStyle={_contentTextStyle}
                                contentBuilder={extendedInfo.contentBuilder}
                                title={extendedInfo.title}
                                content={extendedInfo.content}
                                contentHint={extendedInfo.contentHint}
                                itemStyle={extendedInfo.itemStyle}
                                onClick={extendedInfo.onClick}
                                bottomLine={true}
                            />
                        );
                    } else if (extendedInfo.itemStyleType == ListSettingItemType.listSettingEditItem) {
                        return (
                            <ListSettingEditItem
                                key={extendedInfo.title}
                                title={extendedInfo.title}
                                textInputStyle={_contentTextStyle}
                                editable={extendedInfo.editable}
                                contentHint={extendedInfo.contentHint}
                                content={extendedInfo.content}
                                formatter={extendedInfo.formatter}
                                keyboardType={extendedInfo.keyboardType as KeyboardType}
                                onChanged={extendedInfo.onChanged}
                                bottomLine={true}
                                endIcon={extendedInfo.endIcon}
                            />
                        );
                    }
                })}
            </View>
        );
    }

    private _renderPremiumPurchaseView(): JSX.Element {
        const state = this.bloc.currentState,
            goodsInfo = state.detail;
        const { isPurchaseMarkup } = state;
        let medicineAdditionRateRange: GoodsPkgCostPriceMakeUpMinMaxItem | undefined;
        if (!_.isNil(goodsInfo?.typeId)) {
            medicineAdditionRateRange = state.medicineAdditionRateRange(goodsInfo?.typeId);
        }
        const isPurchasePriceType = goodsInfo?.priceType == SubClinicPricePriceMode.purchaseMarkup;
        // 总部或者单店
        const corporateOrSingleStore = userCenter.clinic?.isChainAdminClinic || userCenter.clinic?.isNormalClinic;
        const showSubSetPrice =
            corporateOrSingleStore ||
            (state.showSubSetPrice && (!userCenter.clinic?.isChainAdminClinic || !userCenter.clinic?.isNormalClinic));
        // 总部限价显示条件（总部或者开启了自主定价但是单店不显示）
        const corporateLimitShow =
            userCenter.clinic?.isChainAdminClinic ||
            (state.showSubSetPrice && !userCenter.clinic?.isChainAdminClinic && !userCenter.clinic?.isNormalClinic);
        const pricingModuleInfoList = [
            {
                show:
                    (goodsInfo.isMedicineWestAndChinesePatent || goodsInfo.isChineseMedicine || goodsInfo.isMedicalMaterial) &&
                    isPurchaseMarkup,
                itemStyleType: ListSettingItemType.listSettingItem,
                title: "定价模式",
                content: goodsInfo._priceTypeDisplay,
                contentHint: "选择定价模式",
                style: [
                    ABCStyles.bottomLine,
                    { borderBottomColor: goodsInfo.priceType == undefined ? Colors.errorBorder : Colors.dividerLineColor },
                ],
                itemStyle: showSubSetPrice ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal,
                showErrorBorder: goodsInfo.priceType == undefined,
                onClick: () => showSubSetPrice && this.bloc.requestSelectPriceType(),
            },
            {
                show:
                    (goodsInfo.isMedicineWestAndChinesePatent || goodsInfo.isChineseMedicine || goodsInfo.isMedicalMaterial) &&
                    isPurchaseMarkup &&
                    isPurchasePriceType,
                itemStyleType: ListSettingItemType.listSettingItem,
                height: Sizes.dp48,
                title: "加成率",
                titleStyle: TextStyles.t16NT2.copyWith({ color: Colors.t2 }),
                contentStyle: [ABCStyles.rowAlignCenter, { paddingVertical: Sizes.dp14 }],
                showErrorBorder:
                    state.showErrorHint &&
                    (_.isNil(goodsInfo.priceMakeupPercent) ||
                        goodsInfo.priceMakeupPercent < (medicineAdditionRateRange?.min ?? 0) ||
                        goodsInfo.priceMakeupPercent > (medicineAdditionRateRange?.max ?? 99999)),
                contentBuilder: () => {
                    return (
                        <View style={ABCStyles.rowAlignCenter}>
                            <AbcTextInput
                                style={{
                                    width: Sizes.dp48,
                                    ...TextStyles.t16NT6.copyWith({
                                        color: goodsInfo.priceMakeupPercent == undefined ? Colors.T4 : Colors.T6,
                                    }),
                                    height: Sizes.dp24,
                                }}
                                ref={(ref) => (this._priceModeInput = ref)}
                                value={goodsInfo.priceMakeupPercent?.toString()}
                                keyboardType={"numeric"}
                                multiline={false}
                                formatter={[NumberOnlyFormatter(), LengthLimitingTextInputFormatter(5)]}
                                onChangeText={(value) => this.bloc.requestSelectMarkupRate(Number(value))}
                                onBlur={() =>
                                    this.bloc.requestCheckPriceModeRange(goodsInfo?.typeId, Number(this._priceModeInput?.value ?? 0))
                                }
                                editable={showSubSetPrice}
                            />
                            <Text style={[TextStyles.t16NT6, { marginLeft: Sizes.dp8 }]}>%</Text>
                        </View>
                    );
                },
                endIcon: corporateLimitShow
                    ? () => (
                          <Text style={[TextStyles.t14NT2, { marginLeft: Sizes.dp6, lineHeight: Sizes.dp20 }]}>
                              {`总部限价${medicineAdditionRateRange?.min ?? 0}%~${medicineAdditionRateRange?.max ?? 0}%`}
                          </Text>
                      )
                    : undefined,
            },
            {
                show:
                    isPurchaseMarkup &&
                    (userCenter.clinic?.isNormalHospital || userCenter.clinic?.isClinicHousekeeper) &&
                    isPurchasePriceType,
                itemStyleType: ListSettingItemType.listSettingItem,
                height: Sizes.dp48,
                title: "零售价格",
                titleStyle: TextStyles.t16NT2.copyWith({ color: Colors.t2 }),
                contentStyle: [ABCStyles.rowAlignCenter, { paddingVertical: Sizes.dp14 }],
                contentBuilder: () => {
                    return (
                        <View style={ABCStyles.rowAlignCenter}>
                            <Text style={[TextStyles.t16NT6]}>{`${goodsInfo?.retailPriceRangeStr}`}</Text>
                        </View>
                    );
                },
            },
        ];
        return (
            <View style={styles.groupContainer}>
                {pricingModuleInfoList.map((pricingModuleInfo) => {
                    if (!pricingModuleInfo.show) return;
                    switch (pricingModuleInfo.itemStyleType) {
                        case ListSettingItemType.listSettingItem:
                            return (
                                <ListSettingItem
                                    key={pricingModuleInfo.title}
                                    title={pricingModuleInfo.title}
                                    content={pricingModuleInfo.content}
                                    contentHint={pricingModuleInfo.contentHint}
                                    onClick={pricingModuleInfo.onClick}
                                    showErrorBorder={pricingModuleInfo.showErrorBorder}
                                    itemStyle={pricingModuleInfo.itemStyle}
                                    contentBuilder={pricingModuleInfo.contentBuilder}
                                    bottomLine={true}
                                    endIcon={pricingModuleInfo?.endIcon}
                                />
                            );
                        case ListSettingItemType.listSettingEditItem:
                            return (
                                <ListSettingEditItem
                                    title={pricingModuleInfo.title}
                                    content={pricingModuleInfo.content}
                                    contentHint={pricingModuleInfo.contentHint}
                                    showErrorBorder={pricingModuleInfo.showErrorBorder}
                                    bottomLine={true}
                                />
                            );
                    }
                })}
            </View>
        );
    }

    /**
     * 修改审批中的档案查看详情时展示提示信息
     */
    renderModifyFileStatusView(): JSX.Element {
        const { isGspWaitVerifyStatus } = this.bloc.currentState;
        if (!isGspWaitVerifyStatus) return <View />;
        return (
            <View
                style={[
                    Sizes.paddingLTRB(Sizes.dp20, Sizes.dp10, Sizes.dp20, Sizes.dp10),
                    ABCStyles.rowAlignCenter,
                    { backgroundColor: Colors.Y4 },
                ]}
            >
                <AssetImageView name={"info-fill"} style={{ width: Sizes.dp24, height: Sizes.dp24 }} />
                <SizedBox width={Sizes.dp4} />
                <Text style={TextStyles.t14NY1.copyWith({ lineHeight: Sizes.dp20 })}>{"修改档案审批中"}</Text>
            </View>
        );
    }

    renderContent(): JSX.Element | undefined {
        const state = this.bloc.currentState;
        return (
            <View style={{ flex: 1 }}>
                <ScrollView showsHorizontalScrollIndicator={false} showsVerticalScrollIndicator={false}>
                    {this.renderModifyFileStatusView()}
                    {state.isMedicine && this._renderMedicineBaseInfoView()}
                    {state.isMaterial && this._renderMaterialBaseInfoView()}
                    {state.isGoods && this._renderGoodsInfoBaseView()}
                    {this._renderMedicineSpecInfoView()}
                    {this._renderMedicineChineseGranuleInfoView()}
                    <GroupDivider />
                    {this._renderExtendedView()}
                    <GroupDivider />
                    {(userCenter.clinic?.isNormalHospital || userCenter.clinic?.isClinicHousekeeper) && this._renderPremiumPurchaseView()}
                    {state.isMedicine && this._renderMedicinePriceView()}
                    {state.isMaterial && this._renderMaterialPriceView()}
                    {state.isGoods && this._renderGoodsPriceView()}
                    <GroupDivider />
                    {this.renderHealthInsuranceModuleView()}
                    <GroupDivider height={Sizes.dp24} />
                </ScrollView>
                <ToolBar hideWhenKeyboardShow={true}>
                    <ToolBarButtonStyle1 text={"保存"} onClick={state.hasChange ? () => this.handleSave() : undefined} />
                </ToolBar>
            </View>
        );
    }
}
