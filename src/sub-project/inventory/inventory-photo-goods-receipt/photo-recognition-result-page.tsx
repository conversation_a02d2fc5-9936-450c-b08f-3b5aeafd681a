import React from "react";
import { BaseBlocPage } from "../../base-ui/base-page";
import { ScrollView, Text, View } from "@hippy/react";
import { ABCStyles, ABCStyleSheet, Color, Sizes, TextStyles, FontWeights } from "../../theme";
import { AnyType } from "../../common-base-module/common-types";
import { ignore } from "../../common-base-module/global";
import { PhotoRecognitionResultPageBloc } from "./photo-recognition-result-page-bloc";
import { ABCNavigator, TransitionType } from "../../base-ui/views/abc-navigator";
import { InventoryMedicineItem } from "../data/inventory-draft";
import _ from "lodash";
import { TimeUtils } from "../../common-base-module/utils";
import { IconFontView, Spacer } from "../../base-ui";
import WillPopListener from "../../base-ui/views/will-pop-listener";
import { AbcMap } from "../../base-ui/utils/abc-map";
import { AbcText } from "@app/abc-mobile-ui";
import { Colors } from "@app/theme";
import { Robot } from "../../theme/text-styles";
import FontWeight from "../../theme/font-weights";
import { AbcToolBarButtonStyle1 } from "../../base-ui/abc-app-library";
import { LoadingDialog } from "../../base-ui/dialog/loading-dialog";
import { delayed } from "../../common-base-module/rxjs-ext/rxjs-ext";
import { ABCApiNetwork } from "../../net";
import PartialJsonParser from "../../json-utils/partial-json-parser";
import { LoadingView } from "../../base-ui/views/loading-view";
import { Toast } from "../../base-ui/dialog/toast";
import { pxToDp } from "@app/utils";
import { NoMoreView } from "../../charge/retail-pharmacy/views/no-more-view";
import { AbcEmptyItemView, ABCEmptyView } from "../../base-ui/views/empty-view";
import { AbcButton } from "../../base-ui/views/abc-button";
import { URLProtocols } from "../../url-dispatcher";
import { AbcImageRecoginition, PageParam } from "../../base-ui/abc-image-recognition/abc-image-recogniation";
import SkeletonBar from "../../base-ui/skeleton/skeleton-bar";
import { showConfirmDialog } from "../../base-ui/dialog/dialog-builder";
import { AiPictureDataNotifyStatus } from "../inventory-in/inventory-in-data/inventory-in-agent";
const RobotoMediumFamily = { fontFamily: Robot, fontWeight: FontWeight.medium };

const styles = ABCStyleSheet.create({
    toolBar: {
        backgroundColor: Colors.white,
        paddingHorizontal: Sizes.dp16,
        paddingVertical: Sizes.dp12,
        borderTopWidth: Sizes.border_normal,
        borderTopColor: Colors.border_color_normal,
    },
    toolBarContent: {
        ...ABCStyles.rowAlignCenterSpaceBetween,
    },
    toolBarLeft: {
        ...ABCStyles.rowAlignCenter,
        flex: 1,
        marginRight: Sizes.dp12,
    },
    submitButton: {
        width: Sizes.dp120,
        height: Sizes.dp40,
        borderRadius: Sizes.border_radius_small,
        alignItems: "center",
        justifyContent: "center",
    },
    item: {
        ...ABCStyles.bottomLine,
        paddingVertical: Sizes.dp12,
        backgroundColor: Colors.white,
    },
    itemHeader: {
        ...ABCStyles.rowAlignCenter,
        marginBottom: Sizes.dp2,
    },
    itemTextSize: "mini",
    betweenTextColor: { color: Colors.t2 },
    marginLeftItemStyle: {
        marginLeft: Sizes.dp8, // 添加左间距8px
    },
    itemTextWidth: {
        width: Sizes.dp115,
    },
    itemBottomTextSize: "small",
    itemBatch: {
        ...TextStyles.t14NT2,
        color: Colors.T3,
    },
    itemExpiry: {
        ...TextStyles.t14NT2,
        color: Colors.T3,
    },
    itemQuantity: {
        ...TextStyles.t14NT1,
        color: Colors.T1,
    },
    toolBarText: {
        ...TextStyles.t14NB,
        color: Colors.T1,
        lineHeight: Sizes.dp20,
    },
    toolBarAmount: {
        ...TextStyles.t16MB,
        color: Colors.t1,
        ...RobotoMediumFamily,
    },
    sizedBoxWidth: {
        width: Sizes.dp8,
    },
});

// {
//     "商品名称": "法半夏",
//     "规格": "统制（一级），0.5kg/袋",
//     "上市许可持有人": "null",
//     "生产厂家/产地": "成都兴洋瑞中药饮片有限责任公司 甘肃",
//     "剂型": "中药饮片",
//     "单位": "袋",
//     "数量": "1",
//     "单价": "61.50",
//     "金额": "61.50",
//     "批号": "21041401",
//     "生产日期": "2021-04-20",
//     "有效期": "null",
//     "件数零散": "0 1",
//     "批准文号": "null",
//     "指数": "0.00"
// }
export interface StreamListItem {
    商品名称: string;
    规格: string;
    生产厂家: string;
    类型: string;
    单位: string;
    数量: string;
    单价: string;
    金额: string;
    批号: string;
    生产日期: string;
    效期: string;
}

export interface PhotoRecognitionResultPageProps {
    getInventoryMedicineItems: (taskId?: string) => Promise<InventoryMedicineItem[]>;
    from?: "PC" | "APP"; // PC 表示从PC端跳转，APP 表示从APP端跳转
    type: string; // 0 入库单 1 收库单
    orderDraftId: string; // 随货同行单id
    imageUri: string; // 图片路径
    imageWidth: number; // 图片宽度
    imageHeight: number; // 图片高度
    originalImagePath: string; // 原图路径 注意这里要上传
    onResult?: (result: PhotoRecognitionResultPageProps) => void;
    onUpdateStreamList?: (streamList: StreamListItem[]) => void;
    _goodsSet?: AbcMap<string, string[]>; // 用于存储草稿id和商品id的映射关系 做拍照识别去重
}

interface PhotoRecognitionResultPageState {
    streamList: StreamListItem[];
    isLoadingFromSSE: boolean;
    isDone: boolean;
    isFinishRecognition: boolean;
}

export class PhotoRecognitionResultPage extends BaseBlocPage<
    PhotoRecognitionResultPageProps,
    PhotoRecognitionResultPageBloc,
    PhotoRecognitionResultPageState
> {
    static show(options: PhotoRecognitionResultPageProps): Promise<PhotoRecognitionResultPageProps | undefined> {
        return ABCNavigator.navigateToPage(<PhotoRecognitionResultPage {...options} />, {
            transitionType: TransitionType.inFromRight,
            ohosWillPop: true,
        });
    }

    _taskId: undefined | string = undefined;
    _isDestroyed = false;
    _pagePageParam: PageParam;

    constructor(props: PhotoRecognitionResultPageProps) {
        super(props);
        this.bloc = new PhotoRecognitionResultPageBloc();

        this.state = {
            streamList: [],
            isLoadingFromSSE: true,
            isDone: false,
            isFinishRecognition: false,
        };

        this._pagePageParam = {
            name: "PhotoRecognitionResultPage",
            id: this.props.orderDraftId,
        };

        AbcImageRecoginition.addPageParam(this._pagePageParam);
    }

    // 括号成对出现
    isBracesBalanced(str: string): boolean {
        // 去掉中括号前面的内容
        const start = str.indexOf("[");
        if (start !== -1) {
            str = str.substring(start);
        }

        // 去掉中括号后面的内容
        const end = str.indexOf("]");
        if (end !== -1) {
            str = str.substring(0, end + 1);
        }

        let count = 0;
        for (const char of str) {
            if (char === "{") count++;
            else if (char === "}") count--;

            if (count < 0) return false; // 出现 } 比 { 多
        }
        return count === 0; // 最终必须对称
    }

    isFinishRecognition(str: string): boolean {
        return str.includes("[") && str.includes("]");
    }

    componentDidMount() {
        super.componentDidMount();
        let content = "";
        ABCApiNetwork.connectSSE(`ai/business/goods/stock-in-order/entity-resolution/${this.props.orderDraftId}`, {
            method: "POST",
            body: {
                imageUrls: [this.props.imageUri],
            },
            listeners: {
                onopen: () => {
                    console.log("sse open");
                },
                onclose: () => {
                    if (this._isDestroyed) {
                        return;
                    }
                    console.log("sse close");
                    this.setState({
                        isLoadingFromSSE: false,
                        isFinishRecognition: true,
                    });
                },
                onmessage: (message) => {
                    if (this._isDestroyed) {
                        return;
                    }
                    // {
                    //     "商品名称": "法半夏",
                    //     "规格": "统制（一级），0.5kg/袋",
                    //     "上市许可持有人": "null",
                    //     "生产厂家/产地": "成都兴洋瑞中药饮片有限责任公司 甘肃",
                    //     "剂型": "中药饮片",
                    //     "单位": "袋",
                    //     "数量": "1",
                    //     "单价": "61.50",
                    //     "金额": "61.50",
                    //     "批号": "21041401",
                    //     "生产日期": "2021-04-20",
                    //     "有效期": "null",
                    //     "件数零散": "0 1",
                    //     "批准文号": "null",
                    //     "指数": "0.00"
                    // }
                    let data = {
                        data: {
                            answer: "",
                            done: false,
                            metadata: {
                                taskId: "",
                            },
                        },
                    };
                    try {
                        if (message?.data) {
                            data = JSON.parse(message.data);
                            if (data.data.answer) {
                                content += data.data.answer;
                                const parser = new PartialJsonParser(content);
                                const parsedData: {
                                    completed: string;
                                    list?: StreamListItem[];
                                } = parser.parse();
                                console.log(parsedData.completed);
                                if (`${parsedData.completed}` === "0") {
                                    this.setState({
                                        isFinishRecognition: true,
                                        isDone: true,
                                        isLoadingFromSSE: false,
                                    });
                                    return;
                                }

                                if (this.isFinishRecognition(content)) {
                                    this.setState({
                                        isFinishRecognition: true,
                                    });
                                }

                                if (this.isBracesBalanced(content)) {
                                    if (parsedData.list?.length === 1) {
                                        this.setState({
                                            isLoadingFromSSE: false,
                                        });
                                    }
                                    this.setState({
                                        streamList: parsedData?.list || [],
                                    });
                                }
                            }

                            if (data.data.metadata?.taskId) {
                                this._taskId = data.data.metadata.taskId;
                            }

                            if (data.data.done) {
                                console.log("done .....");
                                this.setState({
                                    isDone: true,
                                });
                            }
                        }
                    } catch (error) {
                        console.error("JSON 解析失败", error);
                    }
                },
                onerror: (error) => {
                    if (this._isDestroyed) {
                        return;
                    }
                    console.error(error);
                    this.setState({
                        isLoadingFromSSE: false,
                        isFinishRecognition: true,
                    });
                },
            },
        })
            .then((res) => {
                console.log(res);
            })
            .catch((err) => {
                console.error(err);
            });
    }

    componentWillUnmount() {
        super.componentWillUnmount();
        this._isDestroyed = true;
        AbcImageRecoginition.removePageParam(this._pagePageParam);
    }

    getBackgroundColor(): Color {
        return Colors.white;
    }

    getAppBarBackIcon(): JSX.Element | undefined {
        return (
            <View style={{ marginHorizontal: Sizes.dp16, paddingVertical: Sizes.dp12 }} onClick={() => ABCNavigator.pop()}>
                <IconFontView key={"back"} name={"s-backto-line"} size={Sizes.dp24} color={Colors.t1} />
            </View>
        );
    }

    getAPPBarCustomTitle(): JSX.Element | undefined {
        return <AbcText style={[TextStyles.t18MW.copyWith({ color: Colors.T1 })]}>{"识别结果"}</AbcText>;
    }

    getAppBarBottomLine(): boolean {
        return false;
    }

    private _retake(): void {
        AbcImageRecoginition.popUtilImageRecoginition();
        setTimeout(() => {
            AbcImageRecoginition.dataNotify(AiPictureDataNotifyStatus.UPLOADING, this.props.orderDraftId, this.props.type);
            ABCNavigator.navigateToPage(
                <AbcImageRecoginition
                    draftId={this.props.orderDraftId}
                    type={this.props.type ?? ""}
                    from={this.props.from}
                    onResult={this.props.onResult}
                />,
                {
                    transitionType: TransitionType.inFromBottom,
                }
            );
        }, 300);
    }

    renderContent(): JSX.Element {
        const { type, onResult } = this.props;
        const streamList = this.state.streamList;
        console.log(streamList);

        // 计算总品种数
        const total = streamList?.length ?? 0;

        // 计算总金额（单价 * 数量 的总和）
        const amount = streamList?.reduce((sum, item) => {
            const useUnitCostPrice = isNaN(+item["单价"]) ? 0 : +item["单价"];
            const useCount = isNaN(+item["数量"]) ? 1 : +item["数量"];
            return sum + useUnitCostPrice * useCount;
        }, 0);
        const fromText = type == "0" ? "入库" : "收货";
        return (
            <View style={{ flex: 1 }}>
                {false ? (
                    <View style={[ABCStyles.centerChild]}>
                        <LoadingView size={Sizes.dp24} />
                    </View>
                ) : (
                    <ScrollView
                        accessibilityLabel={"abc-list-view"}
                        style={{
                            flex: 1,
                            paddingHorizontal: Sizes.dp16,
                            backgroundColor: Colors.white,
                        }}
                    >
                        {this.state.isFinishRecognition && !streamList.length && (
                            <View
                                style={[
                                    {
                                        flex: 1,
                                    },
                                    ABCStyles.centerChild,
                                ]}
                            >
                                <AbcEmptyItemView tips={"未识别到随货同行单"}></AbcEmptyItemView>
                                <View style={{ height: Sizes.dp16 }}></View>
                                <AbcButton
                                    text={"重新拍照"}
                                    style={{ height: Sizes.dp36 }}
                                    textStyle={TextStyles.t16NW}
                                    onClick={() => this._retake()}
                                />
                            </View>
                        )}

                        {streamList?.map((item, index) => (
                            <View key={`item-${index}`}>{this._renderRow(item, index)}</View>
                        ))}

                        {!this.state.isFinishRecognition && this._renderIdleRow()}

                        {this.state.isFinishRecognition && !!streamList.length && <NoMoreView></NoMoreView>}
                    </ScrollView>
                )}

                <View style={styles.toolBar}>
                    <View style={styles.toolBarContent}>
                        <View style={styles.toolBarLeft}>
                            <AbcText style={styles.toolBarText}>{`${total} 个品种，`}</AbcText>
                            <View style={{ flexDirection: "row", alignItems: "flex-end" }}>
                                <AbcText style={[styles.toolBarAmount, { fontSize: Sizes.font_size_small }]}>¥</AbcText>
                                <AbcText style={[styles.toolBarAmount, { fontSize: Sizes.font_size_large, lineHeight: Sizes.dp24 }]}>
                                    {`${Math.floor(amount || 0)}.`}
                                </AbcText>
                                <AbcText style={[styles.toolBarAmount, { fontSize: Sizes.font_size_small }]}>
                                    {amount?.toFixed(2).split(".")[1] || "00"}
                                </AbcText>
                            </View>
                        </View>
                        <AbcToolBarButtonStyle1
                            text={
                                this.state.isDone || (this.state.isFinishRecognition && !streamList.length)
                                    ? `加入${fromText}单`
                                    : `识别并匹配商品中`
                            }
                            showIndicator={!this.state.isDone || !streamList.length}
                            onClick={async () => {
                                const dialog = new LoadingDialog("请稍后");
                                dialog.show();
                                // 在页面关闭后自动隐藏加载框
                                setTimeout(() => dialog.hide(), 300);
                                await delayed(300).toPromise();
                                const inventoryMedicineItems = await this.props.getInventoryMedicineItems(this._taskId);

                                if (!inventoryMedicineItems.length) {
                                    showConfirmDialog("未检测到图片中的随货同行单", "", "我知道了");
                                    return;
                                }

                                // PC发起拍照入库收库时直接返回数据不依赖当前页面
                                if (this.props.from == "PC") {
                                    this.bloc.addInventory({
                                        inventoryMedicineItems: inventoryMedicineItems || [],
                                        orderDraftId: this.props.orderDraftId,
                                        type: this.props.type,
                                        imageUri: this.props.imageUri,
                                        imageWidth: this.props.imageWidth,
                                        imageHeight: this.props.imageHeight,
                                        originalImagePath: this.props.originalImagePath,
                                        from: this.props.from,
                                    });
                                } else {
                                    onResult?.({
                                        inventoryMedicineItems: inventoryMedicineItems || [],
                                        orderDraftId: this.props.orderDraftId,
                                        type: this.props.type,
                                        imageUri: this.props.imageUri,
                                        imageWidth: this.props.imageWidth,
                                        imageHeight: this.props.imageHeight,
                                        originalImagePath: this.props.originalImagePath,
                                        from: this.props.from,
                                        _goodsSet: this.props._goodsSet,
                                    });
                                    ABCNavigator.popUntil("InventoryInCreatePage");
                                }
                            }}
                            style={{ height: Sizes.dp40, borderRadius: Sizes.border_radius_small }}
                        />
                    </View>
                </View>
            </View>
        );
    }

    /**
     * 安全地获取值，如果值为 null 或 undefined 则返回默认值
     * @param value 要检查的值
     * @param defaultValue 默认值，默认为空字符串
     * @returns 安全的值
     */
    private safeValue(value?: string | number | Date | null, defaultValue = "-"): string {
        // 检查 null 或 undefined
        if (_.isNil(value)) {
            return defaultValue;
        }

        // 转换为字符串并去除首尾空格
        const strValue = String(value).trim();

        // 检查空字符串或 "null" 或 "undefined"
        if (strValue === "" || strValue === "null" || strValue === "undefined") {
            return defaultValue;
        }

        return strValue;
    }

    private _renderIdleRow(): JSX.Element {
        return (
            <View
                style={[
                    {
                        paddingVertical: Sizes.dp16,
                        height: Sizes.dp112,
                        flex: 1,
                        justifyContent: "space-between",
                        ...ABCStyles.bottomLine,
                    },
                ]}
            >
                <SkeletonBar height={Sizes.dp16} width={pxToDp(185)} borderRadius={Sizes.dp4}></SkeletonBar>
                <SkeletonBar height={Sizes.dp16} width={pxToDp(124)} borderRadius={Sizes.dp4}></SkeletonBar>
                <SkeletonBar height={Sizes.dp16} width={pxToDp(250)} borderRadius={Sizes.dp4}></SkeletonBar>
                <SkeletonBar height={Sizes.dp16} width={pxToDp(343)} borderRadius={Sizes.dp4}></SkeletonBar>
            </View>
        );
    }

    private _renderRow(data: StreamListItem, unknown?: AnyType, index?: number): JSX.Element {
        ignore(unknown, index);
        let totalCost = 0;

        const packageSpecStr = data["规格"] || "规格: -";
        const manufacturerStr = data["生产厂家"] || "厂家: -";
        const typeStr = data["类型"] || "类型: -";
        const starDate = data["生产日期"] ? TimeUtils.formatDate(new Date(data["生产日期"] || ""), "yyyy-MM-dd", "生产日期") : "生产日期";
        const endDate = data["效期"] ? TimeUtils.formatDate(new Date(data["效期"] || ""), "yyyy-MM-dd", "有效日期") : "有效日期";
        const deteTimeStr = `${starDate} ~ ${endDate}`;
        const useUnitCostPrice = isNaN(+data["单价"]) ? 0 : +data["单价"];
        const useCount = isNaN(+data["数量"]) ? 1 : +data["数量"];
        totalCost += useUnitCostPrice * useCount;
        return (
            <View style={styles.item}>
                <View style={styles.itemHeader}>
                    <AbcText size={"normal"} style={{ color: Colors.t1 }} fontWeight={FontWeights.bold}>
                        {data["商品名称"] || ""}
                    </AbcText>
                </View>
                <View style={{ paddingTop: Sizes.dp2 }}>
                    <View style={[ABCStyles.rowAlignCenter, { flexShrink: 1, flexWrap: "wrap" }]}>
                        <AbcText size={styles.itemTextSize} style={styles.betweenTextColor}>
                            {packageSpecStr}
                        </AbcText>
                        <AbcText size={styles.itemTextSize} style={[styles.marginLeftItemStyle, styles.betweenTextColor]}>
                            {manufacturerStr}
                        </AbcText>
                        <AbcText size={styles.itemTextSize} style={[styles.marginLeftItemStyle, styles.betweenTextColor]}>
                            {typeStr}
                        </AbcText>
                    </View>
                    <View style={[{ paddingBottom: Sizes.dp2 }, ABCStyles.rowAlignCenter]}>
                        <AbcText size={styles.itemTextSize} style={[styles.itemTextWidth, styles.betweenTextColor]} numberOfLines={1}>
                            {`批号: ${data["批号"] || ""}`}
                        </AbcText>
                        <AbcText size={styles.itemTextSize} style={[styles.marginLeftItemStyle, styles.betweenTextColor]} numberOfLines={1}>
                            {deteTimeStr}
                        </AbcText>
                    </View>
                    <View style={ABCStyles.rowAlignCenter}>
                        <AbcText size={styles.itemBottomTextSize} style={styles.itemTextWidth} numberOfLines={1}>
                            {`进价: ${this.safeValue((useUnitCostPrice ?? 0).toFixed(2))}/${data["单位"] || "-"}`}
                        </AbcText>
                        <AbcText
                            size={styles.itemBottomTextSize}
                            style={[styles.marginLeftItemStyle, styles.itemTextWidth]}
                            numberOfLines={1}
                        >
                            {`数量: ${useCount}`}
                        </AbcText>
                        <Spacer />
                        <AbcText size={styles.itemBottomTextSize} style={[styles.marginLeftItemStyle]} numberOfLines={1}>
                            {`金额: ${totalCost.toFixed(2)}`}
                        </AbcText>
                    </View>
                </View>
            </View>
        );
    }
}
