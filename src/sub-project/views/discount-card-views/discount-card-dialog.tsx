import React from "react";
import { MarketCardItem } from "../../charge/view/market-card-item";
import { ScrollView, Text, View } from "@hippy/react";
import {
    ChargeInvoiceDetailData,
    ChargePromotionUseType,
    ChargeStatus,
    CouponPromotion,
    GiftPromotion,
    PatientCardPromotion,
    PatientPointsInfo,
    Promotion,
} from "../../charge/data/charge-beans";
import _ from "lodash";
import { PromotionCardViewProps, SelectMemberInfo } from "../../charge/view/promotion-card-view";
import { DividerLine, SizedBox, Spacer, UniqueKey } from "../../base-ui";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import { BottomSheetHelper } from "../../base-ui/abc-app-library";
import { AbcView } from "../../base-ui/views/abc-view";
import { RightArrowView } from "../../base-ui/iconfont/iconfont-view";
import { ABCUtils } from "../../base-ui/utils/utils";
import { SearchMemberItem } from "../../charge/data/charge-agent";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { MemberChoosePage } from "./discount-details-related-page/member-choose-page";
import { userCenter } from "../../user-center";
import { EditionType } from "../../user-center/data/bean";
import { showConfirmDialog } from "../../base-ui/dialog/dialog-builder";
import { JsonMapper } from "../../common-base-module/json-mapper/json-mapper";
import { CardChoicePage } from "./discount-details-related-page/card-choice-page";
import { AbcMap } from "../../base-ui/utils/abc-map";
import { Pair } from "../../base-ui/utils/value-holder";
import { AnyType } from "../../common-base-module/common-types";
import { PromotionChoosePage } from "./discount-details-related-page/promotion-choose-page";
import { ChargeUtils } from "../../charge/utils/charge-utils";
import { switchMap } from "rxjs/operators";
import { ABCError } from "../../common-base-module/common-error";
import { Subject } from "rxjs";
import { SafeAreaBottomView } from "../../base-ui/safe_area_view";

export class DiscountCardDialog extends MarketCardItem {
    private detailData: ChargeInvoiceDetailData;
    private memberInfo?: SelectMemberInfo;
    _calculatePriceTrigger: Subject<number> = new Subject<number>();
    private totalPrice = 0; //总优惠
    constructor(props: PromotionCardViewProps) {
        super(props);
        this.detailData = props.detailData;
        this.memberInfo = props.memberInfo;
    }
    componentDidMount(): void {
        this._calculatePriceTrigger
            .pipe(
                switchMap(() => {
                    this.detailData?.fillKeyIds();
                    return ChargeUtils.calculatingPrice(this.detailData)
                        .catch((e) => new ABCError(e))
                        .toObservable();
                })
            )
            .subscribe((rsp) => {
                if (!rsp) return;
                if (rsp instanceof ABCError) {
                } else {
                    ChargeUtils.syncChargeInvoiceDetail(this.detailData, rsp);
                }
                this.setState({});
            })
            .addToDisposableBag(this);
    }

    createPromotionCategory(
        promotions?: Array<Promotion | PatientCardPromotion>,
        type?: string,
        rightCont?: string,
        onSwitchPageBtn?: () => void
    ): JSX.Element {
        const { canModify } = this.props;
        return (
            <View
                key={UniqueKey()}
                style={[
                    canModify ? Sizes.paddingLTRB(0, 0, Sizes.dp12, Sizes.dp16) : Sizes.paddingLTRB(0, 0, Sizes.dp16, Sizes.dp16),
                    ABCStyles.rowAlignCenter,
                    { backgroundColor: Colors.white },
                ]}
            >
                <AbcView
                    style={[ABCStyles.rowAlignCenter, { flex: 1, justifyContent: "space-between", marginLeft: Sizes.dp16 }]}
                    onClick={onSwitchPageBtn}
                >
                    <View key={UniqueKey()} style={[ABCStyles.rowAlignCenter, { marginRight: Sizes.dp73 }]}>
                        <Text style={TextStyles.t16NT1}>{`${type}`}</Text>
                    </View>

                    <View style={[ABCStyles.rowAlignCenter, { flex: 1, justifyContent: "flex-end" }]}>
                        {rightCont && (
                            <Text
                                numberOfLines={1}
                                style={[
                                    rightCont.indexOf("选择") > -1
                                        ? TextStyles.t16NM.copyWith({ color: Colors.selectTipColor })
                                        : TextStyles.t16NT1,
                                    { flexShrink: 1 },
                                ]}
                            >
                                {rightCont ?? ""}
                            </Text>
                        )}
                        {canModify && onSwitchPageBtn && <SizedBox width={Sizes.dp4} />}
                        {canModify && onSwitchPageBtn && <RightArrowView color={Colors.t3} />}
                    </View>
                </AbcView>
            </View>
        );
    }

    _createDiscountsItem(
        name: string,
        discountPrice: number,
        type: boolean, // 是否显示有可用类型
        bottomLine = true,
        detailData: ChargeInvoiceDetailData,
        icon?: string,
        description?: string,
        key?: string,
        promotions?: Array<Promotion | GiftPromotion | CouponPromotion | PatientCardPromotion>,
        isClick = true // 是否可点击
    ): JSX.Element {
        const { canModify } = this.props;
        return (
            <View key={key} style={[ABCStyles.rowAlignCenter, bottomLine ? ABCStyles.bottomLine : {}, { marginBottom: Sizes.dp16 }]}>
                <AbcView
                    style={[ABCStyles.rowAlignCenter, { flex: 1 }]}
                    onClick={() => {
                        canModify && isClick
                            ? this._onClickPromotion({
                                  promotions: promotions,
                                  canModify: true,
                                  detailData: detailData,
                              })
                            : undefined;
                    }}
                >
                    <View style={[ABCStyles.rowAlignCenter, { flex: 1 }]}>
                        <Text style={[TextStyles.t16NT1]}>{name}</Text>
                        {description && <SizedBox width={Sizes.dp8} />}
                        {!type && isClick && (
                            <Text style={[TextStyles.t14NT6, { flexShrink: 1 }]} numberOfLines={1}>
                                {description ?? ""}
                            </Text>
                        )}
                    </View>
                    {(type || (!type && !isClick)) && canModify && (
                        <Text style={TextStyles.t16NR2.copyWith({ color: !isClick ? Colors.selectTipColor : Colors.R2 })}>
                            {`${!isClick ? "无可用" : "有可用"}${name}`}
                        </Text>
                    )}
                    {/*{(type || isClick) && <SizedBox width={Sizes.dp24} />}*/}
                    {!type && isClick && <Text style={TextStyles.t16NT1}>{ABCUtils.formatPriceWithRMB(discountPrice, false)}</Text>}

                    {canModify && isClick && <SizedBox width={Sizes.dp4} />}
                    {canModify && isClick && <RightArrowView color={Colors.t3} />}
                </AbcView>
            </View>
        );
    }

    _createPromotionItem(
        name: string,
        discountPrice: number,
        type: string,
        icon?: string,
        bottomLine = true,
        key?: string,
        onClickCancelButton?: () => void
    ): JSX.Element {
        return (
            <View key={key} style={[ABCStyles.rowAlignCenter, bottomLine ? ABCStyles.bottomLine : {}, { paddingVertical: Sizes.dp8 }]}>
                <View style={[ABCStyles.rowAlignCenter, { flex: 1 }]}>
                    <Text style={TextStyles.t16NT1}>{name}</Text>
                    {!!type && <SizedBox width={Sizes.dp8} />}
                    {!!type && (
                        <Text style={[TextStyles.t14NT6, { flexShrink: 1 }]} numberOfLines={1}>
                            {type}
                        </Text>
                    )}

                    {onClickCancelButton && (
                        <AbcView style={{ marginLeft: Sizes.dp8 }} onClick={onClickCancelButton}>
                            <Text style={TextStyles.t14NT6}>取消</Text>
                        </AbcView>
                    )}
                </View>
                <Text style={TextStyles.t16NT1}>{ABCUtils.formatPriceWithRMB(discountPrice)}</Text>
            </View>
        );
    }
    //积分
    _renderPatientPointInfo(): JSX.Element {
        const { canModify } = this.props;
        const { patientPointsInfo } = this.props.detailData;

        if (!patientPointsInfo) return <View />;
        const checked = !!patientPointsInfo.checked;
        return (
            <AbcView
                style={[
                    ABCStyles.rowAlignCenter,
                    { marginLeft: Sizes.dp16, paddingRight: canModify ? Sizes.dp12 : Sizes.dp16, marginBottom: Sizes.dp8 },
                ]}
                onClick={canModify ? this._onClickPatientPointInfo.bind(this) : undefined}
            >
                {checked && (
                    <View style={{ flex: 1 }}>
                        {this._createPromotionItem(
                            "积分",
                            -(patientPointsInfo.checkedDeductionPrice ?? 0),
                            `使用${patientPointsInfo.checkedDeductionPrice! * patientPointsInfo.pointsDeductionRat!}积分抵扣`,
                            "integral",
                            false
                        )}
                    </View>
                )}
                {!checked && (
                    <View style={[ABCStyles.rowAlignCenter, { flex: 1 }]}>
                        <Text style={TextStyles.t16NT1}>{`积分`}</Text>
                        <SizedBox width={Sizes.dp8} />
                        <Text style={[TextStyles.t14NT6, { flexShrink: 1 }]} numberOfLines={1}>
                            {`可抵扣${ABCUtils.formatPrice(patientPointsInfo.maxDeductionPrice ?? 0)}元`}
                        </Text>
                        <Spacer />
                        <Text style={TextStyles.t16NR2}>{`有可用积分`}</Text>
                    </View>
                )}
                {canModify && <SizedBox width={Sizes.dp4} />}
                {canModify && <RightArrowView />}
            </AbcView>
        );
    }

    _renderTotalFeeCountView(): JSX.Element {
        const { chargeSheetSummary, status: chargeStatus } = this.props.detailData;
        let needPay = chargeSheetSummary?.needPayFee ?? 0;
        if ((chargeStatus ?? 0) >= ChargeStatus.charged) {
            needPay = chargeSheetSummary?.receivableFee ?? 0;
        }

        return (
            <View style={[Sizes.paddingLTRB(Sizes.dp16, Sizes.dp24, Sizes.dp18, 0), ABCStyles.topLine, { paddingTop: Sizes.dp20 }]}>
                <View style={[ABCStyles.rowAlignCenter]}>
                    <View style={[ABCStyles.rowAlignCenter, { flex: 1 }]}>
                        <Text style={[TextStyles.t18MT1]}>应收</Text>
                    </View>
                    <Text style={[TextStyles.t18MT1]}>{`${ABCUtils.formatPriceWithRMB(needPay, false)}`}</Text>
                </View>
            </View>
        );
    }

    render(): JSX.Element {
        const { canModify, mode } = this.props;
        const isChargeMode = mode == "charge";
        const { detailData } = this;
        let { memberInfo } = this;
        const promotions = detailData?.promotions;
        const giftRulePromotions = detailData?.giftRulePromotions;
        const couponPromotions = detailData?.couponPromotions;

        const patientCardPromotions = detailData?.patientCardPromotions ?? [];
        const cardItemsName: string[] = [],
            selectPatientCardPromotion: PatientCardPromotion[] = [];
        const isShowServiceDeduct = !_.isNil(patientCardPromotions.find((t) => t.checked)) ? true : false; // 只要卡项有被选择，则服务抵扣显示
        if (patientCardPromotions && !_.isEmpty(patientCardPromotions)) {
            patientCardPromotions
                .filter((item) => item.checked)
                ?.forEach((item) => {
                    cardItemsName.push(item.name ?? "");
                    selectPatientCardPromotion.push(item);
                });
        }
        //判断是否显示卡项一栏
        const showCardName = !_.isEmpty(cardItemsName) ? cardItemsName.join("、") : canModify ? "选择卡项" : "";
        memberInfo = memberInfo ?? SelectMemberInfo.fromChargeDetail(detailData);
        let memberText = "";
        if (memberInfo) {
            if (!_.isNil(memberInfo.mobile)) {
                let mobile = memberInfo.mobile;
                const length = mobile?.length ?? 0;
                if (length > 4) {
                    mobile = mobile!.substring(length - 4);
                }
                memberText = `${memberInfo.memberType}${mobile ? "(" + mobile + ")" : ""}`;
            } else {
                memberText = `${memberInfo.memberType ? memberInfo.memberType : canModify ? "选择会员" : ""}`;
            }
        } else if (canModify) {
            memberText = "选择会员";
        }

        const { totalDeductPrice, serviceDeductName, isExistService } = this.computedTotalDeductPrice();
        const { giftNames, isCanGift } = this.filterGiftRulePromotionName(giftRulePromotions ?? []);
        const { couponNames } = this.filterCouponPromotionName(couponPromotions ?? []);
        const { promotionNames, isCanPromotion } = this.filterPromotionName(promotions ?? []);
        const isHasCoupon = !!couponPromotions?.some((t) => t.isCanBeUsed == ChargePromotionUseType.canUsed); // 是否有可用优惠券

        const { draftAdjustmentFee, oddFee, outpatientAdjustmentFee } = detailData.chargeSheetSummary ?? {};

        //总共优惠价格
        this.totalPrice =
            totalDeductPrice +
            this.computedDiscountPricce(promotions!) +
            this.computedDiscountPricce(giftRulePromotions!) +
            this.computedDiscountPricce(couponPromotions!) +
            -(detailData.patientPointsInfo?.checkedDeductionPrice ?? 0) +
            (oddFee ?? 0) +
            (outpatientAdjustmentFee ?? 0) +
            (draftAdjustmentFee ?? 0);
        const isShowBottom = !!detailData.patientPointsInfo || !!oddFee || !!outpatientAdjustmentFee || !!draftAdjustmentFee;

        return (
            <View style={{ flex: 1, backgroundColor: Colors.white }}>
                {BottomSheetHelper.createTitleBar("优惠明细", undefined, () => {
                    ABCNavigator.pop(this.totalPrice);
                })}

                <ScrollView style={{ backgroundColor: Colors.white, flex: 1 }}>
                    <SizedBox height={Sizes.dp24} />
                    {!!memberText &&
                        this.createPromotionCategory(
                            promotions,
                            "会员",
                            memberText,
                            canModify ? this._onSwitchMemberBtnTap.bind(this) : undefined
                        )}
                    {!!showCardName &&
                        this.createPromotionCategory(
                            patientCardPromotions,
                            "卡项",
                            showCardName,
                            canModify ? this._onSwitchPatientCardList.bind(this) : undefined
                        )}
                    {isShowServiceDeduct && (
                        <View style={{ marginLeft: Sizes.dp16, paddingRight: canModify ? Sizes.dp12 : Sizes.dp16 }}>
                            {this._createDiscountsItem(
                                "服务抵扣",
                                totalDeductPrice,
                                !_.isEmpty(serviceDeductName) ? false : isExistService ? true : false,
                                false,
                                detailData,
                                "deduction",
                                !_.isEmpty(serviceDeductName)
                                    ? serviceDeductName.join("、")
                                    : isExistService
                                    ? "选择服务抵扣"
                                    : "无可用服务抵扣",
                                UniqueKey(),
                                patientCardPromotions,
                                !_.isEmpty(serviceDeductName) ? true : isExistService ? true : false
                            )}
                        </View>
                    )}

                    {!_.isEmpty(promotions) && (
                        <View style={{ marginLeft: Sizes.dp16, paddingRight: canModify ? Sizes.dp12 : Sizes.dp16 }}>
                            {this._createDiscountsItem(
                                "折扣",
                                this.computedDiscountPricce(promotions!),
                                !_.isEmpty(promotionNames) ? false : canModify && isCanPromotion ? true : false,
                                false,
                                detailData,
                                "discount",
                                !_.isEmpty(promotionNames)
                                    ? promotionNames.join("、")
                                    : canModify && isCanPromotion
                                    ? "有可用折扣"
                                    : "无可用折扣",
                                UniqueKey(),
                                promotions,
                                !_.isEmpty(promotionNames) ? true : canModify && isCanPromotion ? true : false
                            )}
                        </View>
                    )}

                    {!_.isEmpty(giftRulePromotions) && (
                        <View style={{ marginLeft: Sizes.dp16, paddingRight: canModify ? Sizes.dp12 : Sizes.dp16 }}>
                            {this._createDiscountsItem(
                                "满减返",
                                this.computedDiscountPricce(giftRulePromotions!),
                                !_.isEmpty(giftNames) ? false : isCanGift ? true : false,
                                false,
                                detailData,
                                "return",
                                !_.isEmpty(giftNames) ? giftNames.join("、") : "",
                                UniqueKey(),
                                giftRulePromotions
                            )}
                        </View>
                    )}

                    {!_.isEmpty(couponPromotions) && (
                        <View
                            style={{
                                marginLeft: Sizes.dp16,
                                paddingRight: canModify ? Sizes.dp12 : Sizes.dp16,
                            }}
                        >
                            {this._createDiscountsItem(
                                "优惠券",
                                this.computedDiscountPricce(couponPromotions!),
                                !couponNames?.length && !!isHasCoupon,
                                false,
                                detailData,
                                "coupon",
                                !!couponNames?.length ? couponNames.join("、") : !isHasCoupon ? "无可用优惠券" : "有可用优惠券",
                                UniqueKey(),
                                couponPromotions,
                                !!couponNames?.length || !!isHasCoupon
                            )}
                        </View>
                    )}
                    <View style={{ marginTop: isShowBottom ? -Sizes.dp8 : 0 }}>
                        {this._renderPatientPointInfo()}

                        {!!oddFee && (
                            <View style={{ marginLeft: Sizes.dp16, paddingRight: Sizes.dp16 }}>
                                {this._createPromotionItem("抹零/凑整", oddFee!, "", "collect", false, UniqueKey())}
                            </View>
                        )}
                        {!!outpatientAdjustmentFee && (
                            <View style={{ marginLeft: Sizes.dp16, paddingRight: Sizes.dp16 }}>
                                {this._createPromotionItem("医生议价", outpatientAdjustmentFee!, "", "bargain", false, UniqueKey())}
                            </View>
                        )}
                        {!!draftAdjustmentFee && (
                            <View style={{ marginLeft: Sizes.dp16, paddingRight: Sizes.dp16 }}>
                                {this._createPromotionItem(
                                    "整单议价",
                                    draftAdjustmentFee!,
                                    "",
                                    "bargain",
                                    false,
                                    UniqueKey(),
                                    canModify ? () => this._cancelAdjustmentFeeClick() : undefined
                                )}
                            </View>
                        )}
                    </View>
                    {!!this.totalPrice && (
                        <View
                            style={[
                                ABCStyles.rowAlignCenterSpaceBetween,
                                { paddingLeft: Sizes.dp16, paddingRight: Sizes.dp18, marginVertical: Sizes.dp8, marginBottom: Sizes.dp16 },
                            ]}
                        >
                            <Text style={TextStyles.t16NT1}>优惠合计</Text>
                            <Text style={TextStyles.t16NT1}>{ABCUtils.formatPriceWithRMB(this.totalPrice)}</Text>
                        </View>
                    )}
                    {isChargeMode && <DividerLine style={{ marginHorizontal: Sizes.dp16 }} />}
                    {isChargeMode && this._renderTotalFeeCountView()}
                    <SizedBox height={Sizes.dp24} />
                </ScrollView>
                <SafeAreaBottomView />
            </View>
        );
    }

    //会员选择
    async _onSwitchMemberBtnTap(): Promise<void> {
        const member: SearchMemberItem | undefined = await ABCNavigator.navigateToPage(<MemberChoosePage />);
        if (member) {
            const memberInfo = new SelectMemberInfo();
            memberInfo.id = member.id;
            memberInfo.mobile = member.mobile;
            memberInfo.memberType = member.memberTypeName;
            this.memberInfo = JsonMapper.deserialize(SelectMemberInfo, { ...memberInfo });
            SelectMemberInfo.updateChargeDetail(this.detailData, this.memberInfo);
            this._calculatePriceTrigger.next();
            this.setState({});
        }
    }

    //卡项选择
    async _onSwitchPatientCardList(): Promise<void> {
        //判断当前诊所是否处于基础版范围，如果是则直接提示"诊所尚未购买本模块"
        await userCenter.getClinicEdition().catchIgnore();
        const clinicEditionConfig = userCenter.clinicEdition?.edition;
        if ((clinicEditionConfig?.id ?? 0) <= EditionType.basic) {
            showConfirmDialog("", "诊所尚未购买本模块", "我知道了");
            return;
        }

        const { detailData } = this.props;
        const patientCardPromotions = detailData?.patientCardPromotions ?? [];
        const cardList: PatientCardPromotion[] | [] = await ABCNavigator.navigateToPage(
            <CardChoicePage patient={detailData?.patient} patientCardPromotions={patientCardPromotions} />
        );
        const lastPatientCardPromotions: PatientCardPromotion[] = [];
        if (!_.isEmpty(cardList)) {
            const patientCard = cardList.filter((t) => t?.isOutOfUseRangeCard == 0); // 患者卡项
            const otherCard = cardList.filter((t) => t.isOutOfUseRangeCard != 0); // 其他人卡项
            if (!_.isEmpty(patientCard)) {
                patientCard.forEach((item) => {
                    lastPatientCardPromotions.push(
                        JsonMapper.deserialize(PatientCardPromotion, {
                            ...item,
                        })
                    );
                });
            }
            if (!_.isEmpty(otherCard)) {
                otherCard.forEach((k) => {
                    lastPatientCardPromotions.push({
                        checked: k.checked,
                        id: k.id,
                        name: k.name,
                        patientName: k.patient?.name,
                        patientMobile: k.patient?.mobile,
                    });
                });
            }
            const data: PatientCardPromotion[] = [];
            lastPatientCardPromotions.forEach((subitem) => {
                data.push(
                    JsonMapper.deserialize(PatientCardPromotion, {
                        ...subitem,
                    })
                );
            });
            this.detailData!.patientCardPromotions = data;
            this._calculatePriceTrigger.next();
            this.setState({});
        }
    }

    _cancelAdjustmentFeeClick(): void {
        this.detailData!.chargeSheetSummary!.draftAdjustmentFee = 0;
        this.detailData!.chargeSheetSummary!.expectedTotalPrice__ = undefined;
        this.detailData!.chargeSheetSummary!._isAdjustTotalAmount = false;
        this._calculatePriceTrigger.next();
    }

    //服务抵扣、折扣、活动
    async _onClickPromotion(params: {
        promotions?: Array<Promotion | GiftPromotion | CouponPromotion | PatientPointsInfo | PatientCardPromotion>;
        detailData: ChargeInvoiceDetailData;
        canModify: boolean;
    }): Promise<void> {
        const { promotions, detailData, canModify } = params;
        if (_.isEmpty(promotions)) return;
        let promotionsSelects: number[] | undefined;
        let giftPromotionsSelects: number[] | undefined;
        let couponPromotionsSelects: AbcMap<CouponPromotion, Pair<boolean, number>> | undefined; //key -> (select, currentCount) couponPromotionsSelects;
        let selectMemberPointPromotion: PatientPointsInfo | undefined;
        let patientCardPromotionsSelects: PatientCardPromotion[] | [];
        const first = _.first(promotions);

        if (first instanceof Promotion)
            promotionsSelects = await PromotionChoosePage.promotionShow(promotions as AnyType, !canModify, canModify, detailData);
        else if (first instanceof GiftPromotion)
            giftPromotionsSelects = await PromotionChoosePage.giftPromotionShow(promotions as AnyType, !canModify, canModify, detailData);
        else if (first instanceof CouponPromotion)
            couponPromotionsSelects = await PromotionChoosePage.couponPromotionShow(!canModify, canModify, detailData);
        else if (first instanceof PatientPointsInfo) {
            selectMemberPointPromotion = await PromotionChoosePage.memberPointPromotion(!canModify, canModify, detailData);
        } else if (first instanceof PatientCardPromotion) {
            patientCardPromotionsSelects = await PromotionChoosePage.patientCardPromotion(!canModify, detailData);
            if (!_.isNil(patientCardPromotionsSelects)) {
                // 解决点击退出按钮时(没有传递卡项内容)，避免再次调用计算接口
                this.detailData.patientCardPromotions = patientCardPromotionsSelects;
            }
        }

        if (!canModify || (!promotionsSelects && !giftPromotionsSelects && !couponPromotionsSelects && !selectMemberPointPromotion)) return;

        if (!_.isEmpty(promotionsSelects)) {
            if (promotionsSelects!.indexOf(promotions!.length) >= 0) promotionsSelects = [];
        }

        if (!_.isEmpty(giftPromotionsSelects)) {
            if (giftPromotionsSelects!.indexOf(promotions!.length) >= 0) giftPromotionsSelects = [];
        }

        ChargeUtils.markPromotionsCheckStatus(this.detailData.promotions, promotionsSelects, true);
        ChargeUtils.markPromotionsCheckStatus(this.detailData!.giftRulePromotions, giftPromotionsSelects);
        ChargeUtils.markCouponPromotionCheckStatus(this.detailData!.couponPromotions, couponPromotionsSelects);
        if (!_.isNil(selectMemberPointPromotion)) {
            this.detailData!.patientPointsInfo = JsonMapper.deserialize(PatientPointsInfo, {
                ...selectMemberPointPromotion,
            });
        }
        this._calculatePriceTrigger.next();
        this.setState({});
    }

    async _onClickPatientPointInfo(): Promise<void> {
        const { detailData, canModify } = this.props;
        const selectMemberPointPromotion = await PromotionChoosePage.memberPointPromotion(!canModify, canModify, detailData);
        if (!canModify || !selectMemberPointPromotion) return;
        ChargeUtils.markPromotionsCheckStatus(this.detailData.promotions, undefined, true);
        ChargeUtils.markPromotionsCheckStatus(this.detailData!.giftRulePromotions, undefined);
        ChargeUtils.markCouponPromotionCheckStatus(this.detailData!.couponPromotions, undefined);
        if (!_.isNil(selectMemberPointPromotion)) {
            this.detailData!.patientPointsInfo = JsonMapper.deserialize(PatientPointsInfo, {
                ...selectMemberPointPromotion,
            });
        }
        this._calculatePriceTrigger.next();
        this.setState({});
    }
}
