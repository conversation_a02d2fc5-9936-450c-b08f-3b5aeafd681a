import { BaseComponent } from "../../../base-ui/base-component";
import { Colors, Sizes, TextStyles } from "../../../theme";
import { merge, timer } from "rxjs";
import { StyleSheet, Text, View } from "@hippy/react";
import AbcMotion from "../../../base-ui/abc-app-library/transform/abc-motion";
import { AbcView } from "../../../base-ui/views/abc-view";
import React from "react";
import { PermissionEvent } from "../../../common-base-module/permission/permission-event";
import { PermissionPurposeType } from "../../../common-base-module/permission/data";
const styles = StyleSheet.create({
    shadow: {
        shadowColor: Colors.S1, // 阴影颜色
        shadowOffset: {
            width: 0,
            height: 2,
        }, // 阴影偏移
        shadowOpacity: 0.11, // 阴影不透明度
        shadowRadius: 10, // 圆角
    },
});

interface PermissionPurposeNotificationProps {
    type?: PermissionPurposeType;
    onCancel(key?: string): void;
}
const descriptionList = [
    {
        type: PermissionPurposeType.cameraPermissions,
        title: "相机权限使用说明：",
        content: "用于快速查询单据、拍摄病历、扫码入库等场景",
    },
    {
        type: PermissionPurposeType.mediaPermissions,
        title: "照片和媒体权限使用说明：",
        content: "用于上传病历图片等场景",
    },
    {
        type: PermissionPurposeType.audioPermissions,
        title: "音频权限使用说明：",
        content: "用于微信沟通、描述病情等场景",
    },
];

interface PermissionPurposeNotificatioState {
    show: boolean;
}

export class PermissionPurposeNotification extends BaseComponent<PermissionPurposeNotificationProps, PermissionPurposeNotificatioState> {
    private rectTop: number;
    private timer: number;
    constructor(props: PermissionPurposeNotificationProps) {
        super(props);
        this.rectTop = Sizes.dp54;

        this.state = {
            show: false,
        };
    }

    componentDidMount(): void {
        merge(timer(20000), PermissionEvent.PermissionCloseObservable)
            .subscribe(() => {
                this.props.onCancel("permissionPurposeNotificationKey");
            })
            .addToDisposableBag(this);

        // @ts-ignore
        // 延迟显示 避免快速动画导致的闪烁
        this.timer = setTimeout(() => {
            this.setState({
                show: true,
            });
        }, 500);
    }

    componentWillUnmount(): void {
        super.componentWillUnmount();
        clearTimeout(this.timer);
    }

    render(): JSX.Element {
        const { type } = this.props;
        const relativeContent = descriptionList.filter((t) => t.type == type)?.[0];
        if (!this.state.show) {
            return <View />;
        }
        if (!relativeContent) return <View />;
        return (
            <View
                style={[
                    styles.shadow,
                    {
                        position: "absolute",
                        top: this.rectTop,
                        left: 0,
                        right: 0,
                        backgroundColor: Colors.transparent,
                    },
                ]}
            >
                <AbcMotion
                    animation={{
                        opacity: {
                            value: [0, 1],
                            repeatCount: 1,
                            duration: 400,
                        },
                    }}
                >
                    <AbcView
                        style={[
                            { marginHorizontal: Sizes.dp8, padding: Sizes.dp16 },
                            { backgroundColor: Colors.white, borderRadius: Sizes.dp6 },
                        ]}
                    >
                        <View>
                            <Text style={TextStyles.t14NT1.copyWith({ lineHeight: Sizes.dp20 })}>{relativeContent?.title ?? ""}</Text>
                            <Text style={TextStyles.t14NT1.copyWith({ lineHeight: Sizes.dp20 })}>{relativeContent?.content ?? ""}</Text>
                        </View>
                    </AbcView>
                </AbcMotion>
            </View>
        );
    }
}
