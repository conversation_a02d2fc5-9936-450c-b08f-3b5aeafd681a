/**
 * create by dengjie
 * desc:
 * create date 2020/5/11
 */
import _, { cloneDeep, isNil } from "lodash";
import {
    ChargeForm,
    ChargeFormItem,
    ChargeFormItemStatus,
    ChargeInvoiceDetailData,
    ChargeSheetSummary,
    ChargeSourceFormType,
    ChargeStatus,
    CouponPromotion,
    GiftPromotion,
    MedicineProcessType,
    PatientPointsInfo,
    ProcessInfo,
    ProcessRule,
    Promotion,
} from "../data/charge-beans";
import { DirectChargeMedicineAddInfo, DirectChargeUsage } from "../direct-charge-medicine-add-page";
import {
    ChineseGoodType,
    ChineseMedicineSpecType,
    DecoctionProductInfo,
    DeliveryCompany,
    DeliveryInfo,
    DeliveryPayType,
    GoodsInfo,
    GoodsSubType,
    GoodsType,
    GoodsTypeId,
    Patient,
    UsageInfo,
} from "../../base-business/data/beans";
import { errorSummary, errorToStr, TimeUtils, UUIDGen } from "../../common-base-module/utils";
import { LogUtils } from "../../common-base-module/log";
import {
    CalculateProcessPriceRsp,
    ChargeAgent,
    ChargeCalculatePayType,
    ChargeCalculateRspData,
    ChargeInvoiceCreateReq,
    ChargeInvoiceSaveDraftReq,
    ChargePayReq,
    GetPushOrderReq,
    GetPushOrderRsp,
    UsageScopesItem,
} from "../data/charge-agent";
import { AbcMap } from "../../base-ui/utils/abc-map";
import { Pair } from "../../base-ui/utils/value-holder";
import { Toast } from "../../base-ui/dialog/toast";
import { StringUtils } from "../../base-ui/utils/string-utils";
import { LoadingDialog } from "../../base-ui/dialog/loading-dialog";
import { ChargeSendDialog } from "../charge-send-dialog";
import { MedicineProcessCostPage } from "../medicine-process-cost-page";
import { DeliveryInfoEditPage } from "../delivery-info-edit-page";
import { ABCUtils } from "../../base-ui/utils/utils";
import { AliPayThirdPartyPayParam, WXThirdPartyPayParam } from "../data/wallet-bean";
import { WxApi, WxPayResultCode } from "../../base-business/wxapi/wx-api";
import { AliPay, AliPayResultStatus } from "../../base-business/alipay/ali-pay";
import { PrescriptionFormItem } from "../../outpatient/data/outpatient-beans";
import { JsonMapper } from "../../common-base-module/json-mapper/json-mapper";
import { ChinesePrescriptionUsage } from "../../outpatient/data/medicine-add-bean";
import { ChineseMedicineConfigProvider, ChineseUsageItemInfo } from "../../outpatient/data/chinese-medicine-config";
import { MedicineUsage } from "../../outpatient/medicine-add-page/medicine-add-page-bean";
import {
    AirPharmacyCalculateForm,
    AirPharmacyCalculateReq,
    AirPharmacyCalculateRspForm,
    AirPharmacyDeliveryRule,
    MedicineScopeId,
    PharmacyType,
    UsageScopeId,
} from "../data/charge-bean-air-pharmacy";
import { ABCError } from "../../common-base-module/common-error";
import { AirPharmacyUtils } from "../../outpatient/air-pharmacy/utils/air-pharmacy-utils";
import { ValidatorUtils } from "../../base-ui/utils/validator-utils";
import { ChargeUiUtils } from "./charge-ui-utils";
import { DialogIndex } from "../../base-ui/dialog/dialog-builder";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { URLProtocols } from "../../url-dispatcher";
import { userCenter } from "../../user-center";
import { InventoryClinicConfig } from "../../inventory/data/inventory-bean";
import { getLastWithdrawalTime } from "../../data/memory-operation";

export class ChargeUtils {
    static maxGroupId = 999999;
    private static _currentCalculateRequestId?: string; //  当前算费请求ID

    static splitToGroupByGroupId<T>(items: Array<T>): Map<number, Array<T>> {
        const groups: Map<number, Array<T>> = new Map();

        for (const item of items) {
            // @ts-ignore
            const groupId = item.groupId ?? this.maxGroupId;
            groups.set(groupId, groups.get(groupId) ?? []);
            groups.get(groupId)?.push(item);
        }

        return groups;
    }

    static groupChargeForms(chargeData: ChargeInvoiceDetailData): Map<ChargeSourceFormType, ChargeForm[]> {
        const groups = new Map<ChargeSourceFormType, ChargeForm[]>();

        chargeData.chargeForms?.forEach((form) => {
            let group = groups.get(form.sourceFormType!);
            if (!group) {
                group = [];
                groups.set(form.sourceFormType!, group);
            }

            group.push(form);
        });
        return groups;
    }

    static groupMedicines(medicines: DirectChargeMedicineAddInfo): Map<number, GoodsInfo[]> {
        const westernItems: GoodsInfo[] = [];
        const chineseItems: GoodsInfo[] = [];
        const treatmentItems: GoodsInfo[] = [];
        const examinationItems: GoodsInfo[] = [];
        const goods: GoodsInfo[] = [];
        const materials: GoodsInfo[] = [];
        const packges: GoodsInfo[] = [];
        const otherFees: GoodsInfo[] = [];
        const glasses: GoodsInfo[] = [];
        const nurseProducts: GoodsInfo[] = [];
        // 非配方饮片
        const nonFormulatedPills: GoodsInfo[] = [];
        const isDrugstoreButler = userCenter.clinic?.isDrugstoreButler;

        for (const item of medicines.goods) {
            if (isDrugstoreButler && item.isChineseMedicineNonFormula) nonFormulatedPills.push(item);
            else if (item.isChineseMedicine) chineseItems.push(item);
            else if (item.isWesternMedicine || item.isChineseWesternMedicine) westernItems.push(item);
            else if (item.type == GoodsType.treatment) treatmentItems.push(item);
            else if (item.type == GoodsType.package) packges.push(item);
            else if (item.type == GoodsType.examination) examinationItems.push(item);
            else if (item.type == GoodsType.otherGoods49) otherFees.push(item);
            else if (item.type == GoodsType.glasses) glasses.push(item);
            else if (item.isGoods) goods.push(item);
            else if (item.isNurseFee) nurseProducts.push(item);
            else if (item.isMedicalMaterial) {
                materials.push(item);
            }
        }

        const groups = new Map<number, GoodsInfo[]>();

        const entries: [ChargeSourceFormType, GoodsInfo[]][] = [
            [ChargeSourceFormType.westernPrescription, westernItems],
            [ChargeSourceFormType.chinesePrescription, chineseItems],
            [ChargeSourceFormType.treatment, treatmentItems],
            [ChargeSourceFormType.examination, examinationItems],
            [ChargeSourceFormType.material, materials],
            [ChargeSourceFormType.goods, goods],
            [ChargeSourceFormType.package, packges],
            [ChargeSourceFormType.otherFee, otherFees],
            [ChargeSourceFormType.glasses, glasses],
            [ChargeSourceFormType.nurseProductFee, nurseProducts],
        ];

        // 目前只有药店才有非配方饮片
        if (isDrugstoreButler) {
            entries.push([ChargeSourceFormType.nonFormulaDecoctionPieces, nonFormulatedPills]);
        }
        new Map(entries).forEach((value, key) => {
            if (!_.isEmpty(value)) {
                groups.set(key, value);
            }
        });

        return groups;
    }

    static toDirectChargeMedicineAddInfo(detailData: ChargeInvoiceDetailData, localAdd = true): DirectChargeMedicineAddInfo {
        const result = new DirectChargeMedicineAddInfo();
        //将收费单的药品，收集起来，带到药品选择界面
        if (!_.isEmpty(detailData.chargeForms)) {
            for (const form of detailData.chargeForms!) {
                if (_.isEmpty(form.chargeFormItems) || form.isDecoction || form.isDelivery || form.isAirPharmacy || form.isVirtualPharmacy)
                    continue;

                let dosageCount = 1;
                for (const formItem of form.chargeFormItems!) {
                    const localItem = formItem.localAdd ?? false;
                    if (localAdd && !localItem) continue;
                    const goods = formItem?.goodsInfo;
                    if (!goods) continue;
                    result.goods.push(goods as GoodsInfo);
                    result.inputCounts.set(goods as GoodsInfo, new DirectChargeUsage(formItem.unit!, formItem.unitCount!));
                    dosageCount = formItem.doseCount!;
                }

                if (form.isChinesePrescription) {
                    result.dosageCount = dosageCount;
                }
            }
        }

        return result;
    }

    static addMedicinesToChargeInvoice(options: {
        detailData: ChargeInvoiceDetailData;
        medicines: DirectChargeMedicineAddInfo;
        oldChargeFormItems?: ChargeFormItem[];
        copyAddMedicine?: boolean;
        departmentId?: string;
    }): void {
        const isDrugstoreButler = userCenter?.clinic?.isDrugstoreButler;
        const groups = ChargeUtils.groupMedicines(options?.medicines);
        const westernItems = groups.get(ChargeSourceFormType.westernPrescription);
        const chineseItems = groups.get(ChargeSourceFormType.chinesePrescription);
        const nonFormulatedPieces = groups.get(ChargeSourceFormType.nonFormulaDecoctionPieces);
        const treatmentItems = groups.get(ChargeSourceFormType.treatment);
        const examinationItems = groups.get(ChargeSourceFormType.examination);
        const goods = groups.get(ChargeSourceFormType.goods);
        const materials = groups.get(ChargeSourceFormType.material);
        const packages = groups.get(ChargeSourceFormType.package);
        const othersFees = groups.get(ChargeSourceFormType.otherFee);
        const glasses = groups.get(ChargeSourceFormType.glasses);
        const nurseProductFee = groups.get(ChargeSourceFormType.nurseProductFee);
        const singlePromotionGift = groups.get(ChargeSourceFormType.SINGLE_PROMOTION_GIFT);

        function createChargeForm(sourceFromType: number, goods: GoodsInfo[]) {
            const { detailData, oldChargeFormItems, medicines, copyAddMedicine, departmentId } = options;
            let chargeForm: ChargeForm | undefined;
            if (copyAddMedicine) {
                //零售收费添加处方时，给最后一个处方添加商品
                chargeForm = _.findLast(detailData.chargeForms, (item) => {
                    return item.sourceFormType == sourceFromType;
                });
            } else {
                chargeForm = detailData.chargeForms?.find((item) => item.sourceFormType == sourceFromType);
            }

            if (chargeForm == undefined) {
                chargeForm = new ChargeForm();
                chargeForm.keyId = UUIDGen.generate();
                chargeForm.sourceFormType = sourceFromType;
                chargeForm.chargeFormItems = [];
                chargeForm.pharmacyType = 0;
                chargeForm.pharmacyNo = 0;

                //完善form的药房信息
                let goodsTypeId = undefined;
                switch (sourceFromType) {
                    case ChargeSourceFormType.westernPrescription: {
                        goodsTypeId = GoodsTypeId.medicineWest;
                        break;
                    }
                    case ChargeSourceFormType.chinesePrescription: {
                        goodsTypeId = GoodsTypeId.medicineChinesePiece;
                        break;
                    }
                    case ChargeSourceFormType.goods: {
                        goodsTypeId = GoodsTypeId.goods;
                        break;
                    }
                    case ChargeSourceFormType.material: {
                        goodsTypeId = GoodsTypeId.material;
                        break;
                    }
                    // 非配方饮片
                    case ChargeSourceFormType.nonFormulaDecoctionPieces: {
                        goodsTypeId = GoodsTypeId.medicineChineseNonFormula;
                        break;
                    }
                }
                if (!!goodsTypeId) {
                    const defaultPharmacyInfo = userCenter.inventoryClinicConfig?.getDefaultPharmacy({
                        departmentId: departmentId,
                        goodsInfo: {
                            typeId: goods?.[0]?.typeId ?? goodsTypeId,
                        },
                    });
                    if (!!defaultPharmacyInfo) {
                        chargeForm.pharmacyType = defaultPharmacyInfo.type;
                        chargeForm.pharmacyNo = defaultPharmacyInfo.no;
                        chargeForm.pharmacyName = defaultPharmacyInfo.name;
                    }
                }

                detailData.chargeForms = detailData.chargeForms ?? [];
                detailData.chargeForms.push(chargeForm);
            }

            for (const item of goods) {
                if (chargeForm!.chargeFormItems == undefined) {
                    chargeForm!.chargeFormItems = [];
                }
                const oldChargeForm = oldChargeFormItems?.find((chargeFormItem) => chargeFormItem.goodsInfo.id === item.id);
                chargeForm!.chargeFormItems!.push(medicines.toChargeFormItem(item, oldChargeForm));
            }
        }

        const entries: [ChargeSourceFormType, GoodsInfo[] | undefined][] = [
            [ChargeSourceFormType.westernPrescription, westernItems],
            [ChargeSourceFormType.chinesePrescription, chineseItems],
            [ChargeSourceFormType.treatment, treatmentItems],
            [ChargeSourceFormType.examination, examinationItems],
            [ChargeSourceFormType.material, materials],
            [ChargeSourceFormType.goods, goods],
            [ChargeSourceFormType.package, packages],
            [ChargeSourceFormType.otherFee, othersFees],
            [ChargeSourceFormType.glasses, glasses],
            [ChargeSourceFormType.nurseProductFee, nurseProductFee],
            [ChargeSourceFormType.SINGLE_PROMOTION_GIFT, singlePromotionGift],
        ];

        // 目前只有药店才有非配方饮片
        if (isDrugstoreButler) {
            entries.push([ChargeSourceFormType.nonFormulaDecoctionPieces, nonFormulatedPieces]);
        }
        new Map(entries).forEach((value, key) => {
            if (!_.isEmpty(value)) {
                createChargeForm(key, value!);
            }
        });
    }

    //收费单计算费用
    static async calculatingPrice(detailData: ChargeInvoiceDetailData): Promise<ChargeCalculateRspData | undefined> {
        // 生成新的请求ID并设置为当前请求
        const requestId = UUIDGen.generate();
        this._currentCalculateRequestId = requestId;

        detailData.fillKeyIds();
        if (_.isEmpty(detailData.chargeForms)) return;
        const selects = new AbcMap<ChargeForm, ChargeFormItem[]>();
        for (const chargeForm of detailData.chargeForms!) {
            if (chargeForm.chargeFormItems != null) {
                const items: ChargeFormItem[] = [];
                for (const formItem of chargeForm.chargeFormItems) {
                    const cloneFormItem = _.cloneDeep(formItem);
                    const { pharmacyType, productInfo } = cloneFormItem || {};
                    const isLocalPharmacy = pharmacyType === PharmacyType.normal;
                    const { typeId, pieceUnit } = (productInfo as GoodsInfo) || {};
                    const isMedicineChineseGranuleG =
                        GoodsTypeId.medicineChineseGranule === typeId && (pieceUnit === "g" || pieceUnit === "克");
                    if (!(userCenter.enableEqCoefficient && isLocalPharmacy && isMedicineChineseGranuleG)) {
                        delete cloneFormItem.eqCoefficient;
                    }
                    if (formItem.checked ?? true) {
                        items.push(cloneFormItem);
                    }
                }
                selects.set(chargeForm, items);
            }
        }

        const promotions: Promotion[] = [];
        detailData.promotions?.forEach((value) => {
            const promotion = new Promotion();
            promotion.id = value.id;
            promotion.checked = value.checked;
            promotions.push(promotion);
        });

        const giftRulePromotions: GiftPromotion[] = [];
        detailData.giftRulePromotions?.forEach((value) => {
            const promotion = new GiftPromotion();
            promotion.id = value.id;
            promotion.expectedChecked = value.expectedChecked;
            promotion.checked = value.checked;
            giftRulePromotions.push(promotion);
        });

        const couponPromotions: CouponPromotion[] = [];
        detailData.couponPromotions?.forEach((value) => {
            const promotion = new CouponPromotion();
            promotion.id = value.id;
            promotion.expectedChecked = value.expectedChecked;
            promotion.totalCount = value.totalCount;
            promotion.currentCount = value.currentCount;
            promotion.availableCount = value.availableCount;
            promotion.checked = value.checked;

            couponPromotions.push(promotion);
        });

        const { expectedAdjustmentFee__, expectedTotalPrice__, _isAdjustTotalAmount } = detailData.chargeSheetSummary ?? {};

        //会员卡使用状态，默认为使用，有memberId时为指定会员卡，为memberInfo为null时，为不使用会员卡
        let useMemberFlag = 0;
        if (detailData._localMemberInfo === null) {
            useMemberFlag = 20;
        } else if (!!detailData._localMemberInfo) {
            useMemberFlag = 10;
        } else if (!!detailData?.useMemberFlag) {
            useMemberFlag = detailData.useMemberFlag;
        }

        try {
            const result = await ChargeAgent.postChargeInvoiceCalculate({
                payMode: ChargeCalculatePayType.paid,
                paymentMethod: detailData._payMode,
                chargeSheetId: detailData.id,
                selects: selects,
                promotions: promotions,
                giftPromotions: giftRulePromotions,
                couponPromotions: couponPromotions,
                memberId: (!!useMemberFlag ? detailData.memberId : "") ?? "",
                useMemberFlag,
                patientId: detailData.patientId ?? detailData.patient?.id,
                deliveryInfo: detailData.deliveryInfo,
                patientPointsInfo: detailData.patientPointsInfo,
                expectedAdjustmentFee: userCenter.clinic?.isDrugstoreButler ? undefined : expectedAdjustmentFee__,
                patientCardPromotions: detailData.patientCardPromotions ?? [],
                isNeedPatientCardBalance: detailData?.isNeedPatientCardBalance ?? 1,
                patientPointDeductProductPromotions: detailData?.patientPointDeductProductPromotions ?? [],
                expectedTotalFee: userCenter.clinic?.isDrugstoreButler ? expectedTotalPrice__ : undefined,
                retailType: detailData.retailType,
                adjustmentPriceFlag: !isNil(_isAdjustTotalAmount) ? (!!_isAdjustTotalAmount ? 1 : 0) : detailData?.adjustmentPriceFlag ?? 0,
                requestId: requestId,
            });

            // 检查返回的数据中的requestId是否与当前请求的requestId匹配
            if (result?.requestId !== requestId) {
                LogUtils.d(`算费请求ID不匹配，忽略返回结果: 请求requestId=${requestId}, 返回requestId=${result.requestId}`);
                return undefined;
            }

            return result;
        } catch (error) {
            // 即使出错也要检查请求ID，避免老请求的错误覆盖新请求
            if (this._currentCalculateRequestId !== requestId) {
                LogUtils.d(
                    `算费请求错误但请求ID不匹配，忽略错误: requestId=${requestId}, currentRequestId=${this._currentCalculateRequestId}`
                );
                return undefined;
            }
            throw error; // 重新抛出错误
        }
    }

    static syncChargeInvoiceDetail(detailData?: ChargeInvoiceDetailData, calculateRspData?: ChargeCalculateRspData): void {
        if (detailData == null || calculateRspData == null) return;
        detailData.promotions = calculateRspData.promotions;
        detailData.giftRulePromotions = calculateRspData.giftRulePromotions;
        detailData.couponPromotions = calculateRspData.couponPromotions;
        detailData.patientPointsInfo = calculateRspData.patientPointsInfo;
        detailData.patientCardPromotions = calculateRspData.patientCardPromotions;
        detailData.canPaidPatientCards = calculateRspData.canPaidPatientCards;
        detailData.patientPointDeductProductPromotions = calculateRspData.patientPointDeductProductPromotions;

        detailData.memberId = calculateRspData.memberId ?? detailData.memberId;
        detailData.memberInfo = calculateRspData.memberInfo ?? detailData.memberInfo;

        if (!detailData.chargeSheetSummary) detailData.chargeSheetSummary = new ChargeSheetSummary();
        const chargeSheetSummary = detailData.chargeSheetSummary;
        chargeSheetSummary.totalFee = calculateRspData.totalFee;
        chargeSheetSummary.adjustmentFee = calculateRspData.adjustmentFee;
        chargeSheetSummary.oddFee = calculateRspData.oddFee;
        chargeSheetSummary.discountFee = calculateRspData.discountFee;
        chargeSheetSummary.needPayFee = calculateRspData.needPayFee;
        chargeSheetSummary.receivableFee = calculateRspData.receivableFee;
        chargeSheetSummary.draftAdjustmentFee = calculateRspData.draftAdjustmentFee;
        chargeSheetSummary.excludeDraftAndOddFee = calculateRspData.excludeDraftAndOddFee;
        chargeSheetSummary.afterRoundingDiscountedTotalFee = calculateRspData.afterRoundingDiscountedTotalFee;
        chargeSheetSummary.beforeAdjustmentTotalFee = calculateRspData.beforeAdjustmentTotalFee;
        chargeSheetSummary._adjustmentPriceFlag =
            detailData.adjustmentPriceFlag ?? (detailData.status == ChargeStatus.draft ? 0 : undefined); // 如果是零售收费单，默认为0，采用beforeAdjustmentTotalFee字段计算差值
        chargeSheetSummary.canAdjustmentFee = calculateRspData.canAdjustmentFee;
        chargeSheetSummary.canAdjustment = calculateRspData.canAdjustment;
        chargeSheetSummary._isAdjustTotalAmount =
            chargeSheetSummary._isAdjustTotalAmount ?? (!isNil(detailData?.adjustmentPriceFlag) && !!detailData?.adjustmentPriceFlag);

        //expectedTotalPrice__只争对整单议价的总价，但是存在整单议价后，在重新增删卡项操作，调用算费，导致传给后台expectedAdjustmentFee的金额不对，收费失败
        chargeSheetSummary.expectedTotalPrice__ = userCenter.clinic?.isDrugstoreButler ? undefined : calculateRspData.needPayFee;
        chargeSheetSummary.discountTotalFee = calculateRspData.discountTotalFee;
        chargeSheetSummary.patientTotalFee = calculateRspData.patientTotalFee;
        chargeSheetSummary.promotionDiscountTotalFee = calculateRspData.promotionDiscountTotalFee;
        chargeSheetSummary.singleDiscountTotalFee = calculateRspData.singleDiscountTotalFee;

        for (const form of detailData.chargeForms!) {
            const calculateForm = calculateRspData?.chargeForms?.find((item) => item.compareKey() == form.compareKey());
            if (calculateForm == undefined) continue;
            form.totalPrice = calculateForm.totalPrice;
            form.medicineTotalPrice = calculateForm.medicineTotalPrice;
            form.sourceMedicineTotalPrice = calculateForm.sourceMedicineTotalPrice;
            form.totalDiscountPrice = calculateForm.totalDiscountPrice;
            form.processRule = calculateForm.processRule;
            if (calculateRspData) {
                const missmatchFormItems: ChargeFormItem[] = []; //用来收集后台算费删除的项
                for (const formItem of form.chargeFormItems ?? []) {
                    const calculateFormItem = calculateForm.chargeFormItems?.find((item) => item.compareKey() == formItem.compareKey());

                    if (!calculateFormItem) {
                        missmatchFormItems.push(formItem);
                        continue;
                    }

                    formItem.unitCount = calculateFormItem.unitCount;
                    formItem.unitPrice = calculateFormItem.unitPrice;
                    formItem.doseCount = calculateFormItem.doseCount;
                    formItem.discountPrice = calculateFormItem.discountPrice;
                    formItem.totalPrice = calculateFormItem.totalPrice;
                    formItem.discountedTotalPrice = calculateFormItem.discountedTotalPrice;
                    formItem.sourceUnitPrice = calculateFormItem.sourceUnitPrice;
                    formItem.sourceTotalPrice = calculateFormItem.sourceTotalPrice;
                    formItem.expectedUnitPrice = calculateFormItem.expectedUnitPrice;
                    formItem.expectedTotalPrice = calculateFormItem.expectedTotalPrice;
                    formItem.expectedTotalPriceRatio = calculateFormItem.expectedTotalPriceRatio;
                    formItem.totalPriceRatio = calculateFormItem.totalPriceRatio;
                    formItem.chargeFormItemBatchInfos = calculateFormItem.chargeFormItemBatchInfos;
                    formItem.canAdjustment = calculateFormItem.canAdjustment;
                    formItem.singlePromotions = calculateFormItem.singlePromotions;
                    formItem.singlePromotionedTotalPrice = calculateFormItem.singlePromotionedTotalPrice;
                    formItem.singlePromotionedUnitPrice = calculateFormItem.singlePromotionedUnitPrice;
                    formItem.isExpectedBatch = calculateFormItem.isExpectedBatch;
                    formItem.grossProfitRate = calculateFormItem.grossProfitRate;
                    formItem.originalChargeFormItemIds = calculateFormItem.originalChargeFormItemIds;
                    formItem.expectedDoseCount = calculateFormItem.expectedDoseCount;
                    formItem.composeChildren = calculateFormItem.composeChildren;
                    // 目前只有本地药房才有pharmacyGoodsStockList
                    if (!form?.pharmacyType) {
                        // 需要将库存信息进行更新
                        calculateFormItem?.resetStockByPharmacyNo();
                        // 挂号费、加工费、快递费没有库存说法，不需要调用
                        if (!formItem.isRegistration && !formItem.isDecoction && !formItem?.isDelivery) {
                            formItem.productInfo = Object.assign(calculateFormItem.productInfo ?? {}, calculateFormItem.productInfo);
                            formItem.resetStockByPharmacyNo();
                        }
                    }
                    if (formItem.isDelivery) {
                        if (detailData.deliveryInfo) detailData.deliveryInfo!.deliveryFee__ = calculateFormItem.unitPrice;
                    }

                    if (formItem.isDecoction && calculateForm.processRule) {
                        formItem.productInfo = JsonMapper.deserialize(DecoctionProductInfo, calculateForm.processRule);
                    }
                }

                //如果算费清除了辅料费，删除本地对应项目
                missmatchFormItems.forEach((item) => item.isIngredient && _.remove(form.chargeFormItems ?? [], item));

                //处理算费后产生的新的收费项
                const newFormItems = calculateForm.chargeFormItems?.filter(
                    (item) => !form.chargeFormItems?.find((originalItem) => originalItem.compareKey() == item.compareKey())
                );

                for (const newFormItem of newFormItems ?? []) {
                    if (newFormItem.isIngredient) {
                        newFormItem.productInfo = calculateForm.processRule?.ingredient;
                        form.chargeFormItems!.push(newFormItem);
                    }
                }
            }
        }
        // 药店有赠品的说法，所以calculateRspData中可能会包含单项优惠赠品，需要追加到detailData.chargeForms中，但是需要判断是否已经存在了，不存在才追加
        const giftForm = this.processSinglePromotionGift(
            detailData?.chargeForms?.filter((t) => t.isSinglePromotionGift),
            calculateRspData?.chargeForms?.filter((t) => t.isSinglePromotionGift)
        );
        const index = detailData.chargeForms?.findIndex((t) => t.isSinglePromotionGift) ?? -1;
        if (!!giftForm?.length) {
            const existGiftForm = detailData?.chargeForms?.filter((t) => t.isSinglePromotionGift);
            if (existGiftForm?.length) {
                detailData.chargeForms![index!] = giftForm[0];
            } else {
                detailData.chargeForms = detailData.chargeForms?.concat(giftForm);
            }
        } else {
            if (index > -1) {
                _.remove(detailData.chargeForms, (item) => item.isSinglePromotionGift);
            }
        }
    }

    static processSinglePromotionGift(detailDataGiftForm?: ChargeForm[], rspGiftDataForm?: ChargeForm[]): ChargeForm[] | undefined {
        // 处理单项优惠赠品（药店场景）
        const copyDetailDataGiftForm = cloneDeep(detailDataGiftForm),
            copyRspGiftDataForm = cloneDeep(rspGiftDataForm)?.map((item) => {
                item.keyId = item.keyId ?? UUIDGen.generate();
                item.chargeFormItems?.map((formItem) => {
                    formItem.keyId = formItem?.keyId ?? UUIDGen.generate();
                    return formItem;
                });
                return item;
            });
        let originGiftForm: ChargeForm[] = [];
        if (!copyRspGiftDataForm?.length) return;
        // 原来form中没有单项优惠
        if (copyRspGiftDataForm?.length && !copyDetailDataGiftForm?.length) {
            originGiftForm = originGiftForm.concat(copyRspGiftDataForm);
            return originGiftForm;
        }
        for (const form of copyRspGiftDataForm) {
            const existForm = copyDetailDataGiftForm?.find((t) => t.compareKey() == form.compareKey());
            if (!existForm) continue;
            originGiftForm = originGiftForm.concat([form]);
        }
        return originGiftForm;
    }

    static markPromotionsCheckStatus(promotions?: Array<Promotion | GiftPromotion>, selects?: number[], updateCheckFlag = false): void {
        if (selects == undefined || _.isEmpty(promotions)) return;
        const length = promotions!.length;
        for (let i = 0; i < length; ++i) {
            const promotion = promotions![i];
            promotion.checked = selects.indexOf(i) >= 0;
            if (!updateCheckFlag) {
                if (promotion instanceof GiftPromotion) promotion.expectedChecked = selects.indexOf(i) >= 0;
            }
        }
    }

    static markCouponPromotionCheckStatus(promotions?: CouponPromotion[], selects?: AbcMap<CouponPromotion, Pair<boolean, number>>): void {
        if (selects == undefined || _.isEmpty(promotions)) return;
        const length = promotions!.length;
        for (let i = 0; i < length; ++i) {
            const promotion = promotions![i];
            const info = selects.get(promotion);
            promotion.expectedChecked = info!.first;
            promotion.currentCount = info!.second;
        }
    }

    static updatePromotions(
        chargeSheet: ChargeInvoiceDetailData,
        promotions: {
            selectPromotions?: number[];
            selectGiftPromotions?: number[];
            selectCouponPromotions?: AbcMap<CouponPromotion, Pair<boolean, number>>;
            selectMemberPointPromotion?: PatientPointsInfo;
        }
    ): void {
        ChargeUtils.markPromotionsCheckStatus(chargeSheet!.promotions, promotions.selectPromotions, true);
        ChargeUtils.markPromotionsCheckStatus(chargeSheet!.giftRulePromotions, promotions.selectGiftPromotions);
        ChargeUtils.markCouponPromotionCheckStatus(chargeSheet!.couponPromotions, promotions.selectCouponPromotions);
        if (!_.isNil(promotions.selectMemberPointPromotion)) {
            chargeSheet!.patientPointsInfo = JsonMapper.deserialize(PatientPointsInfo, {
                ...promotions.selectMemberPointPromotion,
            });
        }
    }

    static setDeliveryInfo(data: ChargeInvoiceDetailData, deliveryToHome?: boolean, deliveryInfo?: DeliveryInfo): void {
        const deliveryType = deliveryToHome ? 1 : 0;
        data.deliveryInfo = deliveryInfo ?? new DeliveryInfo();
        data.deliveryInfo.deliveryFee__ = data.deliveryInfo.deliveryFee__ ?? 0;
        data.deliveryInfo.deliveryPayType = data.deliveryInfo.deliveryPayType ?? DeliveryPayType.cashNow;

        if (data.deliveryInfo?.deliveryPayType == DeliveryPayType.freightCollect) data.deliveryInfo.deliveryFee__ = 0.0;
        if (deliveryType == 0) {
            _.remove(data.chargeForms!, (item) => item.sourceFormType == ChargeSourceFormType.delivery);

            const deliveryChargeFormItem = data.getDeliveryChargeFormItem();
            if (deliveryChargeFormItem) {
                deliveryChargeFormItem.checked = false;
            }
            return;
        }

        let deliveryForm = data.chargeForms?.find((item) => item.sourceFormType == ChargeSourceFormType.delivery);
        if (deliveryForm == undefined) {
            deliveryForm = new ChargeForm();
            deliveryForm.status = ChargeStatus.unCharged;
            deliveryForm.sourceFormType = ChargeSourceFormType.delivery;
            deliveryForm.keyId = UUIDGen.generate();
            data.chargeForms = data.chargeForms ?? [];
            data.chargeForms.push(deliveryForm);
        }

        ChargeUtils.setDeliveryInfoWithChargeForm(deliveryForm, deliveryToHome, deliveryInfo);
    }

    static setDeliveryInfoWithChargeForm(deliveryForm: ChargeForm, deliveryToHome?: boolean, deliveryInfo?: DeliveryInfo): void {
        deliveryForm.deliveryInfo = deliveryInfo;
        let deliveryFormItem = deliveryForm.chargeFormItems?.find((item) => item.productType === GoodsType.deliveryFee);
        if (!deliveryFormItem) {
            deliveryFormItem = new ChargeFormItem();
            deliveryFormItem.status = ChargeFormItemStatus.unCharged;
            deliveryFormItem.productType = GoodsType.deliveryFee;
            deliveryFormItem.productSubType = GoodsSubType.deliveryFeeDefault;
            deliveryFormItem.keyId = UUIDGen.generate();
            deliveryFormItem.name = "快递费";
            deliveryForm.chargeFormItems = deliveryForm.chargeFormItems ?? [];
            deliveryForm.chargeFormItems.push(deliveryFormItem);
        }

        deliveryFormItem.checked = deliveryToHome ?? false;
        deliveryFormItem.unitCount = 1;
        deliveryFormItem.doseCount = 1;
        deliveryFormItem.sourceUnitPrice = undefined;
        deliveryFormItem.sourceTotalPrice = undefined;
        deliveryFormItem.unitPrice = deliveryInfo?.deliveryFee__ ?? 0.0;
        deliveryFormItem.expectedUnitPrice = deliveryInfo?.expectDeliverFee__;
        deliveryFormItem.expectedTotalPrice = deliveryInfo?.expectDeliverFee__;
        deliveryFormItem.productInfo = deliveryInfo;
        deliveryFormItem.totalPrice = deliveryInfo?.deliveryFee__ ?? 0.0;
    }

    static fillChargeRequest(
        request: ChargeInvoiceCreateReq | ChargePayReq | ChargeInvoiceSaveDraftReq | GetPushOrderReq,
        invoiceData: ChargeInvoiceDetailData
    ): void {
        //只有在送药上门时，才添加快递信息
        if (invoiceData.deliveryType__ === 1) {
            request.deliveryInfo = invoiceData.deliveryInfo ?? new DeliveryInfo();
            request.deliveryInfo.deliveryFee__ = undefined;
        }

        request.retailType = invoiceData.retailType;
        request.chiefComplaint = invoiceData.chiefComplaint;
        request.diagnosis = invoiceData.diagnosis;
        request.diagnosisInfos = invoiceData.diagnosisInfos;
        request.departmentId = invoiceData.departmentId;
        request.departmentName = invoiceData.departmentName;
        request.doctorId = invoiceData.doctorId;
        request.doctorName = invoiceData.doctorName;
        request.promotions = invoiceData.promotions ?? [];
        request.giftRulePromotions = invoiceData.giftRulePromotions ?? [];
        request.couponPromotions = invoiceData.couponPromotions ?? [];
        request.patientPointsInfo = invoiceData.patientPointsInfo;
        request.batchExtractOriginalSheetIds = invoiceData.batchExtractOriginalSheetIds;
    }

    static removeUnCheckFormItem(invoiceData: ChargeInvoiceDetailData): void {
        invoiceData.chargeForms?.forEach((form) => {
            if (form.chargeFormItems) _.remove(form.chargeFormItems, (formItem) => formItem.checked == false);
        });

        if (invoiceData.chargeForms) _.remove(invoiceData.chargeForms, (form) => _.isEmpty(form.chargeFormItems));
    }

    static checkDeliveryAndDecoctionForCharge(data: ChargeInvoiceDetailData): boolean {
        const deliveryType = data.deliveryType__;
        const deliveryInfo = data.deliveryInfo;

        if (deliveryType == 1) {
            if (_.isEmpty(deliveryInfo?.displayAddress("/", true))) {
                Toast.show("收货地址不能为空", { warning: true });
                return false;
            }

            if (_.isEmpty(deliveryInfo?.deliveryMobile)) {
                Toast.show("收货人电话号码不能为空", { warning: true });
                return false;
            }

            if (!StringUtils.validateMobile(deliveryInfo?.deliveryMobile ?? "", deliveryInfo?.deliveryCountryCode)) {
                Toast.show("收货人电话号码格式错误", { warning: true });
                return false;
            }

            if (_.isEmpty(deliveryInfo?.deliveryCompany?.id)) {
                Toast.show("请选择快递公司", { warning: true });
                return false;
            }

            if (deliveryInfo?.deliveryPayType == DeliveryPayType.cashNow && data.deliveryFee == undefined) {
                Toast.show("快递费不能为空", { warning: true });
                return false;
            }
        }

        return true;
    }

    //验证收费单的快递设置是否正确
    static validateDeliveryInfoForCharge(chargeData: ChargeInvoiceDetailData): boolean {
        const deliveryType = chargeData.deliveryType__;
        const deliveryInfo = chargeData.deliveryInfo;
        if (deliveryType == 1) {
            if (_.isEmpty(deliveryInfo?.displayAddress("/", true))) {
                return false;
            }

            if (_.isEmpty(deliveryInfo?.deliveryMobile)) {
                return false;
            }

            if (!StringUtils.validateMobile(deliveryInfo?.deliveryMobile ?? "", deliveryInfo?.deliveryCountryCode)) {
                return false;
            }

            if (_.isEmpty(deliveryInfo?.deliveryCompany?.id)) {
                return false;
            }

            if (deliveryInfo?.deliveryPayType == DeliveryPayType.cashNow && chargeData?.deliveryFee == undefined) {
                return false;
            }
        }

        return true;
    }

    //删除收费单里本地添加的项目（收费里新加的）
    static removeAllLocalAddItems(detailData: ChargeInvoiceDetailData): void {
        if (detailData.chargeForms == undefined) return;

        for (const form of detailData.chargeForms!) {
            form.chargeFormItems = form.chargeFormItems!.filter((item) => !(item.localAdd ?? false));
        }

        detailData.chargeForms = detailData.chargeForms!.filter((form) => !_.isEmpty(form.chargeFormItems));
    }

    static async pushChargeOrder(invoiceData: ChargeInvoiceDetailData, wxBindStatus?: boolean): Promise<string | undefined> {
        let loadingDialog = new LoadingDialog("正在获取推送信息");
        loadingDialog.show();
        let rsp: GetPushOrderRsp;
        try {
            rsp = await ChargeAgent.getPushOrderDetail(invoiceData);

            await loadingDialog.hide();
        } catch (e) {
            await loadingDialog.fail(`获取失败：${errorToStr(e)}`);
            return;
        }
        //原价为凑整抹零后的价格
        const oldNeedPay =
            (!isNil(invoiceData.chargeSheetSummary?._adjustmentPriceFlag)
                ? invoiceData.chargeSheetSummary?.beforeAdjustmentTotalFee
                : invoiceData.chargeSheetSummary?.afterRoundingDiscountedTotalFee) ?? 0;
        const needPay = await ChargeSendDialog.show(rsp!, invoiceData, wxBindStatus);
        if (needPay == undefined) return;

        if (needPay != parseFloat(rsp!.needPay ?? "0")) {
            const chargeSheetSummary = invoiceData.chargeSheetSummary ?? new ChargeSheetSummary();
            chargeSheetSummary.expectedTotalPrice__ = needPay;
        }

        loadingDialog = new LoadingDialog("正在推送");
        loadingDialog.show();
        try {
            const chargeSheetId = await ChargeAgent.pushOrder({
                invoiceData: invoiceData,
                sellerId: invoiceData?.sellerId,
                expectedAdjustmentFee: needPay - oldNeedPay,
                receivableFee: needPay, //实收价格为输入的价格
            });
            await loadingDialog.success("推送成功");
            return chargeSheetId;
        } catch (e) {
            await loadingDialog.fail(`推送失败：${errorToStr(e)}`);
            return;
        }
    }

    static getProcessRule(processRules: ProcessRule[], type: number, subType: number): ProcessRule | undefined {
        const rule = processRules.find((item) => item.type == type && item.subType == subType);
        if (rule) return rule;

        for (const rule of processRules) {
            if (rule.children) {
                const matchRule = this.getProcessRule(rule.children, type, subType);
                if (matchRule) return matchRule;
            }
        }
    }

    /**
     * 将计算的加工费信息写入到收费单中
     * @param chargeData
     * @param calculateProcessPriceRsp 后台计算的加工费
     */
    static syncChargeDataWithProcessCalculate(
        chargeData: ChargeInvoiceDetailData,
        calculateProcessPriceRsp: CalculateProcessPriceRsp,
        mutable = false
    ): void {
        const map = new Map<string, ProcessInfo>();
        calculateProcessPriceRsp.processInfos?.forEach((item) => {
            if (!!item?.keyId) map.set(item.keyId!, item);
        });
        chargeData.chargeForms?.forEach((form) => {
            if (form.isDecoction) {
                form.processInfo = JsonMapper.deserialize(ProcessInfo, {
                    ...map.get(form.processInfo!.keyId!),
                    chargeFormId: form.processInfo?.chargeFormId,
                    isNeedAssignmentTime: true,
                });
                if (mutable) {
                    const originTakeMedicineTime = form?.processInfo?.takeMedicationTime;
                    const takeMedicationTime = TimeUtils.formatTimeForDateParse(originTakeMedicineTime ?? "");
                    const { lastTakeMedicineTime, isExistStoreTime } = getLastWithdrawalTime(form?.processInfo?.takeMedicationTime);
                    const withdrawalDate = !originTakeMedicineTime && !isExistStoreTime ? "" : takeMedicationTime;
                    const hourTime = (() => {
                            const currentTime = !!originTakeMedicineTime
                                ? takeMedicationTime
                                : isExistStoreTime
                                ? lastTakeMedicineTime
                                : "";
                            if (!currentTime) {
                                return "";
                            }
                            // 获取小时数并格式化
                            const hours = currentTime.getHours();
                            return hours < 10 ? "0" + hours : String(hours);
                        })(),
                        minutesTime = (() => {
                            const currentTime = !!originTakeMedicineTime
                                ? takeMedicationTime
                                : isExistStoreTime
                                ? lastTakeMedicineTime
                                : "";
                            if (!currentTime) {
                                return "";
                            }
                            // 获取小时数并格式化
                            const minutes = currentTime.getMinutes();
                            return minutes < 10 ? "0" + minutes : String(minutes);
                        })();
                    if (!withdrawalDate) {
                        form.processInfo.takeMedicationTime = undefined;
                    } else {
                        withdrawalDate.setHours(Number(hourTime));
                        withdrawalDate.setMinutes(Number(minutesTime));
                        form.processInfo.takeMedicationTime = withdrawalDate;
                    }
                }
                const formItem = form.getFormItem(GoodsType.decoctionFee);
                if (formItem) formItem.name = form.processInfo?.processUsageInfo?.processName ?? form?.processInfo?.name ?? "加工费";
            }
        });
    }

    /**
     * 取药时间--今天显示今天，其他显示对应日期
     */
    static getWithdrawalTimeStr(time: Date): string {
        let str = "";
        const isToday = TimeUtils.getStartOfDate(time).getTime() == TimeUtils.getStartOfDate(new Date()).getTime();
        if (isToday) {
            str = `今天 ${time?.format("HH:mm")}`;
        } else {
            str = `${time?.format("MM-dd HH:mm")}`;
        }
        return str;
    }

    /**
     * 获取加工的取药时间
     */
    static getProcessTime(chargeData?: ChargeInvoiceDetailData, chargeForm?: ChargeForm): string {
        let withdrawalTime = "";
        const infos: string[] = [];
        const chinesePrescription = chargeData?.chargeForms?.filter((form) => form.isChinesePrescription);
        const decoctionForm = chargeData?.chargeForms?.find((t) => t.isDecoction);
        if (!chinesePrescription?.length && !!decoctionForm && !!decoctionForm?.processInfo?.takeMedicationTime) {
            const takeMedicationTime = TimeUtils.formatTimeForDateParse(decoctionForm?.processInfo?.takeMedicationTime);
            infos.push(`取药时间:${this.getWithdrawalTimeStr(takeMedicationTime)}`);
        } else {
            chinesePrescription?.forEach((form) => {
                if ((!!form.id && form.id !== chargeForm?.processInfo?.chargeFormId) || infos.length) return;
                const processInfo = chargeData?.chargeForms?.find(
                    (item) => item.isDecoction && (item.processInfo?.chargeFormId == form.id || form.id === undefined)
                )?.processInfo;
                if (!processInfo) return;
                if (!!processInfo?.takeMedicationTime) {
                    const withdrawalTimeCont = TimeUtils.formatTimeForDateParse(processInfo.takeMedicationTime);
                    withdrawalTime = `取药时间:${this.getWithdrawalTimeStr(withdrawalTimeCont)}`;
                }
            });
        }

        return withdrawalTime;
    }
    /**
     * 获取显示用的加工信息：e.g:
     * @param chargeData 收费单
     * @param chargeForm 加工费form
     */
    static getProcessInfoDescription(chargeData: ChargeInvoiceDetailData, chargeForm: ChargeForm): string {
        if (!chargeData) return "";
        const rules = ChargeAgent.getProcessRulesSync();

        if (!rules) return "";
        //暂时注释掉--注释原因：解决零售收费有多个加工信息时，显示信息不准确
        // const retail = chargeData.isLocalDraft || chargeData.source == ChargeInvoiceSource.retail;
        const infos: string[] = [];
        const chinesePrescriptionList = chargeData.chargeForms?.filter((form) => form.isChinesePrescription);
        const decoctionForm = chargeData.chargeForms?.find((t) => t.isDecoction);
        if (_.isEmpty(chinesePrescriptionList) && !_.isUndefined(decoctionForm)) {
            const processInfo = `1剂煎${decoctionForm?.processInfo?.processBagUnitCount}袋${
                !!decoctionForm?.processInfo?.totalProcessCount ? "，共" + decoctionForm?.processInfo?.totalProcessCount + "袋" : ""
            }`;
            infos.push(processInfo);
        } else {
            const chinesePrescription = chargeData.chargeForms?.filter((form) => form.isChinesePrescription);
            chinesePrescription?.forEach((form, index) => {
                //form.id为undefined说明是直接收费草稿状态，后台还没有分配id
                if ((!!form.id && form.id !== chargeForm.processInfo?.chargeFormId) || infos.length) return;
                // if ((form.id !== chargeForm.processInfo?.chargeFormId && !retail) || infos.length) return;
                const medicineNamePrefix = `${
                    (chinesePrescription?.length ?? 0) > 1 ? "中药处方" + ABCUtils.toChineseNum(index + 1) + "：" : ""
                }`;
                const processInfo = chargeData.getProcessInfo(form.id);
                if (!processInfo) return;
                const rule = ChargeUtils.getProcessRule(rules, processInfo!.type!, processInfo!.subType!);
                if (!rule) return;

                let str = medicineNamePrefix;
                // let itemCount = 0;
                // form.chargeFormItems?.forEach((item) => {
                //     if (item.checked ?? true) itemCount++;
                // });
                // if (!_.isEmpty(form.chargeFormItems)) {
                //     str += `1剂${itemCount}味，共${_.first(form.chargeFormItems)!.doseCount!}剂`;
                // }
                //特殊处理，只能蕉药类型，才能袋的单位
                if (rule.type === MedicineProcessType.decoction && !!processInfo?.processBagUnitCount)
                    str += `1剂煎${processInfo.processBagUnitCount}袋${
                        !!processInfo.totalProcessCount ? "，共" + processInfo.totalProcessCount + "袋" : ""
                    }`;

                infos.push(str);
            });
        }

        return infos.join("；");
    }

    static async setDecoctionSwitch(
        chargeData: ChargeInvoiceDetailData,
        decoction: boolean,
        isOpenTakeMedicine?: boolean
    ): Promise<boolean> {
        if (decoction) {
            const data = await MedicineProcessCostPage.show(chargeData, undefined, isOpenTakeMedicine);
            if (!data) {
                return false;
            }
            ChargeUtils.setDecoctionInfo(
                chargeData!,
                data.contactMobile,
                data.chargeForms?.filter((form) => form.isDecoction)
            );
        } else {
            _.remove(chargeData.chargeForms!, (item) => item.sourceFormType == ChargeSourceFormType.decoction);
        }

        return true;
    }

    static async setDeliverySwitch(chargeData: ChargeInvoiceDetailData, delivery?: boolean): Promise<boolean> {
        if (delivery === true && chargeData.deliveryType__ !== 1) {
            const result = await DeliveryInfoEditPage.show({
                patient: chargeData.patient ?? new Patient(),
                deliveryInfo: chargeData.deliveryInfo ?? new DeliveryInfo(),
                payTypeMutable: true,
                addressMutable: true,
                deliverFeeEnable: true,
                chargeData: chargeData,
            });

            if (!result || !result.deliverInfo) {
                return false;
            }
            chargeData.deliveryInfo = result.deliverInfo;
        }

        ChargeUtils.setDeliveryInfo(chargeData, delivery, chargeData!.deliveryInfo);
        return true;
    }

    /**
     * 第三方支付（微信，支付宝）
     * @param payParam 支付参数
     * @returns  {success: boolean //表示成功与否
     *             errorStr:string //出错信息}
     */
    static async thirdPartyPay(
        payParam?: WXThirdPartyPayParam | AliPayThirdPartyPayParam
    ): Promise<{ success: boolean; errorStr: string }> {
        LogUtils.liveLog("开始生成订单 payParam= " + JSON.stringify(payParam));
        if (!payParam) {
            return {
                success: false,
                errorStr: "payParam参数为空",
            };
        }

        let errorStr = "";
        let success = false;
        do {
            //微信支付
            if (payParam instanceof WXThirdPartyPayParam) {
                const rsp = await WxApi.payment({
                    ...payParam,
                    packageValue: payParam.package,
                });
                if (!rsp) {
                    errorStr = "微信支付响应空";
                    break;
                }

                LogUtils.liveLog("WxApi.payment rsp: " + JSON.stringify(rsp));
                //支付失败
                if (rsp.errCode !== WxPayResultCode.success) {
                    errorStr = `（${rsp.errCode}）`;
                    if (rsp.errStr) {
                        errorStr += "-" + rsp.errStr;
                    }
                    break;
                }
            } else {
                //支付宝支付
                const rsp = await AliPay.payment({ orderInfo: payParam.body! });
                if (!rsp) {
                    errorStr = "支付宝返回空";
                    break;
                }

                LogUtils.liveLog("AliPay.payment rsp: " + JSON.stringify(rsp));
                if (rsp.resultStatus !== AliPayResultStatus.success && rsp.resultStatus !== AliPayResultStatus.waitingProcess) {
                    //支付成功
                    errorStr = `${rsp.resultStatus}`;
                    break;
                }
            }

            success = true;
        } while (0);

        return {
            success: success,
            errorStr: errorStr,
        };
    }

    /**
     * 将一个ChargeFormItem结构转为PrescriptionFormItem结构
     * @param chargeFormItem
     * @param defaultType
     * @param defaultSubtype
     */
    static chargeFormItemsToPrescriptionFormItems(
        chargeFormItem: ChargeFormItem[],
        defaultType?: number,
        defaultSubtype?: number
    ): PrescriptionFormItem[] {
        const formItems: PrescriptionFormItem[] = [];
        chargeFormItem.forEach((item) => {
            if (item.isDecoction || item.isDelivery || item.isIngredient) {
                return;
            }
            formItems.push(ChargeUtils.chargeFormItemToPrescriptionFormItem(item, defaultType, defaultSubtype));
        });

        return formItems;
    }

    /**
     * 将一个ChargeFormItem结构转为PrescriptionFormItem结构
     * @param prescriptionFormItems
     * @param defaultType
     * @param defaultSubtype
     */
    static prescriptionFormItemsToChargeFormItems(
        prescriptionFormItems: PrescriptionFormItem[],
        defaultType?: number,
        defaultSubtype?: number
    ): ChargeFormItem[] {
        const formItems: ChargeFormItem[] = [];
        prescriptionFormItems.forEach((item) => {
            formItems.push(ChargeUtils.prescriptionFormItemToChargeFormItem(item, defaultType, defaultSubtype));
        });

        return formItems;
    }

    /**
     * 将一个ChargeFormItem结构转为PrescriptionFormItem结构
     * @param chargeFormItem
     * @param defaultType
     * @param defaultSubtype
     */
    static chargeFormItemToPrescriptionFormItem(
        chargeFormItem: ChargeFormItem,
        defaultType?: number,
        defaultSubtype?: number
    ): PrescriptionFormItem {
        //这里忽略类型错误，将chargeFormItem里的属性按字段写到PrescriptionFormItem中
        // @ts-ignore
        const prescriptionFormItem = JsonMapper.deserialize(PrescriptionFormItem, chargeFormItem);
        prescriptionFormItem.type = chargeFormItem.productType ?? defaultType;
        prescriptionFormItem.subType = chargeFormItem.productSubType ?? defaultSubtype;

        return prescriptionFormItem;
    }

    /**
     * 将一个ChargeFormItem结构转为PrescriptionFormItem结构
     * @param prescriptionFormItem
     * @param defaultType
     * @param defaultSubtype
     */
    static prescriptionFormItemToChargeFormItem(
        prescriptionFormItem: PrescriptionFormItem,
        defaultType?: number,
        defaultSubtype?: number
    ): ChargeFormItem {
        //这里忽略类型错误，将chargeFormItem里的属性按字段写到PrescriptionFormItem中
        // @ts-ignore
        const chargeFormItem = JsonMapper.deserialize(ChargeFormItem, prescriptionFormItem);

        chargeFormItem.productType = prescriptionFormItem.type ?? prescriptionFormItem.productInfo?.type ?? defaultType;
        chargeFormItem.productSubType = prescriptionFormItem.subType ?? prescriptionFormItem.productInfo?.subType ?? defaultSubtype;

        return chargeFormItem;
    }

    /**
     * 将一个ChargeForm转为一个门诊用法数据结构
     * @param chargeForm
     */
    static toChinesePrescriptionUsage(chargeForm: ChargeForm): ChinesePrescriptionUsage {
        return JsonMapper.deserialize(ChinesePrescriptionUsage, {
            specificationType: ChineseMedicineSpecType.typeFromName(chargeForm.specification!),
            // specification : form.specification
            count: chargeForm.doseCount,
            requirement: chargeForm.usageInfo?.requirement,
            usage: JsonMapper.deserialize(ChineseUsageItemInfo, {
                name: chargeForm.usageInfo?.usage,
            }),
            dailyDosage: JsonMapper.deserialize(ChineseUsageItemInfo, {
                name: chargeForm.usageInfo?.dailyDosage,
            }),
            freq: JsonMapper.deserialize(ChineseUsageItemInfo, {
                name: chargeForm.usageInfo?.freq,
            }),
            usageLevel: JsonMapper.deserialize(ChineseUsageItemInfo, {
                name: chargeForm.usageInfo?.usageLevel,
            }),
            usageDays: JsonMapper.deserialize(ChineseUsageItemInfo, {
                name: chargeForm.usageInfo?.usageDays,
            }),

            medicineStateScopeId: chargeForm.medicineStateScopeId,
            usageScopeId: chargeForm.usageScopeId,
            pharmacyType: chargeForm.pharmacyType,
            pharmacyNo: chargeForm.pharmacyNo,
            pharmacyName: chargeForm.pharmacyName,
            totalProcessCount: chargeForm.usageInfo?.totalProcessCount,
            processBagUnitCount: chargeForm.usageInfo?.processBagUnitCount,
            processRemark: chargeForm.usageInfo?.processRemark,
            vendorInfo: chargeForm?.vendor__,
            vendorId: chargeForm?.vendorId ?? chargeForm?.vendor__?.vendorId,
            vendorName: chargeForm?.vendorName ?? chargeForm?.vendor__?.vendorName,
            vendorUsageScopeId: chargeForm?.vendor__?.vendorUsageScopeId,
        });
    }

    static fillChargeFromItem(formItem: ChargeFormItem, medicine: GoodsInfo, usage?: MedicineUsage): void {
        formItem.productId = medicine.id;
        formItem.productType = medicine.type;
        formItem.productSubType = medicine.subType;
        formItem.name = medicine.displayName;
        formItem.medicineCadn = medicine.medicineCadn;
        formItem.productInfo = medicine;
        const usageInfo = (formItem.usageInfo = formItem.usageInfo ?? {});
        usageInfo.usage = usage?.usage?.name;
        // usageInfo.usageLevel = usage?.;
        usageInfo.dosageUnit = usage?.dosage?.dosageUnit;
        usageInfo.dosage = usage?.dosage?.count?.toString();
        usageInfo.freq = usage?.freq?.en;
        usageInfo.days = usage?.days;
        usageInfo.specialRequirement = usage?.specialRequirement?.name;
        formItem.unit = usage?.unit;
        formItem.unitCount = usage?.unitCount;
        formItem.unitPrice = medicine.unitPriceWithUnit(usage!.unit!);
        formItem.useDismounting = medicine.useDismounting(usage!.unit!) ? 1 : 0;

        formItem.ast = _.isBoolean(usage?.ast ?? false) ? (usage?.ast ? 1 : 0) : null;
    }

    static autoFillAirPharmacyDeliveryInfo(
        chargeForms?: ChargeForm[],
        deliveryInfo?: DeliveryInfo,
        skipIfHaveDeliveryAddress = false
    ): Promise<boolean> {
        if (!deliveryInfo) return Promise.resolve(false);

        const tasks: Promise<{ rsp: AirPharmacyDeliveryRule[] | undefined; chargeForm: ChargeForm } | ABCError>[] = [];
        chargeForms?.forEach((form) => {
            //如果已经有快递地址，跳过
            if (skipIfHaveDeliveryAddress && !_.isEmpty(form.deliveryInfo?.displayAddress())) return;

            //查找空中药房处方快递地址为空的项，并尝试自动填入
            const task = ChargeUtils.getAirPharmacyAvailableCompanyWithChargeForm(form, deliveryInfo)
                .then((rsp) => {
                    return {
                        chargeForm: form,
                        rsp: rsp,
                    };
                })
                .catch((error) => new ABCError(error));

            tasks.push(task);
        });

        if (tasks.length == 0) return Promise.resolve(false);

        return Promise.all(tasks).then((tasksRsp) => {
            let needUpdate = false;
            tasksRsp.forEach((rsp) => {
                if (rsp instanceof ABCError) {
                    return;
                }

                const deliveryCompanyRule = _.first(rsp.rsp);
                if (deliveryCompanyRule == undefined) return;

                const company = new DeliveryCompany();
                const newDeliveryInfo = JsonMapper.deserialize(DeliveryInfo, deliveryInfo);
                newDeliveryInfo.deliveryCompany = company;
                newDeliveryInfo.deliveryPayType = DeliveryPayType.cashNow;
                if (deliveryCompanyRule) {
                    company.id = deliveryCompanyRule.id;
                    company.name = deliveryCompanyRule.name;
                    newDeliveryInfo.deliveryFee__ = deliveryCompanyRule.deliveryPrice;
                }

                ChargeUtils.setDeliveryInfoWithChargeForm(rsp.chargeForm, true, newDeliveryInfo);
                needUpdate = true;
            });

            return needUpdate;
        });
    }

    static getAirPharmacyAvailableCompanyWithChargeForm(
        chargeForm: ChargeForm,
        deliveryInfo?: DeliveryInfo
    ): Promise<AirPharmacyDeliveryRule[] | undefined> {
        //查找空中药房处方快递地址为空的项，并尝试自动填入
        if (!chargeForm.isAirPharmacy) return Promise.resolve(undefined);
        deliveryInfo = deliveryInfo ?? chargeForm.getDeliveryInfo();
        const address = deliveryInfo?.displayAddress();

        if (_.isEmpty(address)) return Promise.resolve(undefined);

        const isPiece = (() => {
            if (!!chargeForm?.specification) {
                return ChineseMedicineSpecType.chinesePiece == ChineseMedicineSpecType.typeFromName(chargeForm.specification);
            }
            return chargeForm.medicineStateScopeId != MedicineScopeId.keLi;
        })();
        return ChargeAgent.getAirPharmacyAvailableCompany({
            ...deliveryInfo,
            goodsTypeId: isPiece ? ChineseGoodType.chinesePiece : ChineseGoodType.chineseGranule,
            orderItems:
                chargeForm.chargeFormItems
                    ?.filter((item) => !item.isDelivery && !item.isDecoction)
                    ?.map((item) => {
                        return {
                            productId: item.productId,
                            name: item.name,
                            unitPrice: item.unitPrice,
                            unit: item.unit,
                            unitCount: item.unitCount,
                            doseCount: item.doseCount,
                            totalPrice: item.totalPrice,
                        };
                    }) ?? [],
            usageScopeId: chargeForm?.usageScopeId,
            vendorId: chargeForm?.vendorId,
            medicineStateScopeId: chargeForm?.medicineStateScopeId ?? "",
        });
    }

    static getAirPharmacyDeliveryWithChargeForm(
        chargeForm: ChargeForm,
        deliveryInfo?: DeliveryInfo,
        otherAirPharmacyCalculateForm?: AirPharmacyCalculateForm[],
        airPharmacySort?: number
    ): Promise<AirPharmacyCalculateRspForm[] | undefined> {
        //查找空中药房处方快递地址为空的项，并尝试自动填入
        if (!chargeForm.isAirPharmacy) return Promise.resolve(undefined);
        deliveryInfo = deliveryInfo ?? chargeForm.getDeliveryInfo();
        const address = deliveryInfo?.displayAddress();

        if (_.isEmpty(address)) return Promise.resolve(undefined);

        const isPiece = (() => {
            if (!!chargeForm?.specification) {
                return ChineseMedicineSpecType.chinesePiece == ChineseMedicineSpecType.typeFromName(chargeForm.specification);
            }
            return chargeForm.medicineStateScopeId != MedicineScopeId.keLi;
        })();
        const params = new AirPharmacyCalculateReq();
        params.forms = [
            {
                deliveryInfo: deliveryInfo,
                deliveryPrimaryFormId: "",
                goodsTypeId: isPiece ? ChineseGoodType.chinesePiece : ChineseGoodType.chineseGranule,
                items:
                    chargeForm.chargeFormItems
                        ?.filter((item) => !item.isDelivery && !item.isDecoction)
                        ?.map((item) => {
                            return {
                                productId: item.productId,
                                name: item.name,
                                unitPrice: item.unitPrice,
                                unit: item.unit,
                                unitCount: item.unitCount,
                                doseCount: item.doseCount,
                                totalPrice: item.totalPrice,
                            };
                        }) ?? [],
                keyId: chargeForm.keyId,
                medicineStateScopeId: chargeForm.medicineStateScopeId,
                // totalProcessCount?: number;
                usageInfo: chargeForm?.usageInfo,
                usageScopeId: chargeForm.usageScopeId,
                vendorId: chargeForm.vendorId,
                sort: airPharmacySort,
            },
        ];
        for (const formItem of otherAirPharmacyCalculateForm ?? []) {
            params.forms?.push(formItem);
        }

        params.forms?.sort((a, b) => {
            const val1 = a?.sort,
                val2 = b?.sort;
            if (!val1 && val1 != 0 && val2) {
                if (val1 == undefined) return -9999;
                return -999;
            }
            if (!val2 && val2 != 0 && val1) {
                if (val2 == undefined) return 9999;
                return 999;
            }
            return val1! - val2!;
        });

        return ChargeAgent.getAirPharmacyCalculate(params);
    }

    static async fixAirPharmacyMedicalState(chargeForms?: ChargeForm[]): Promise<void> {
        //有可能出现单子里只有药态id,没有对应的name,这里需要获取修改下
        const needFixAirPharmacyChargeForms: ChargeForm[] = [];
        chargeForms?.forEach((form) => {
            if (form.isAirPharmacy && !_.isEmpty(form.medicineStateScopeId) && _.isEmpty(form.medicineStateScopeName)) {
                needFixAirPharmacyChargeForms.push(form);
            }
        });

        if (needFixAirPharmacyChargeForms.length != 0) {
            const scopes = await ChargeAgent.getUsageScopes(true).catch((error) => new ABCError(error));

            if (scopes && !(scopes instanceof ABCError)) {
                const children: UsageScopesItem[] = [];
                scopes.forEach((item) => {
                    if (item.children) children.push(...item.children);
                });
                needFixAirPharmacyChargeForms.forEach((form) => {
                    form.medicineStateScopeName = children.find((scope) => form.medicineStateScopeId == scope.id)?.name;
                });
            }
        }
    }

    public static async refreshAirPharmacy(chargeForms?: ChargeForm[]): Promise<{ priceChanged: boolean; deliveryUpdate: boolean }> {
        const update = { priceChanged: false, deliveryUpdate: false };
        for (const form of chargeForms ?? []) {
            if (!form.isAirPharmacy) continue;

            const deliveryInfo = form.getDeliveryInfo();

            const oldFormItems: ChargeFormItem[] = [];
            form.chargeFormItems?.forEach((item) => {
                if (item.isDecoction || item.isDelivery || item.isIngredient) return;
                oldFormItems.push(item);
            });
            const vendors = await ChargeAgent.getVendors({
                goodsTypeId: AirPharmacyUtils.specificationToType(form.specification ?? ""),
                vendorId: form.vendorId,
                medicineStateScopeId: form.medicineStateScopeId,
                usageScopeId: form.usageScopeId,
                doseCount: form.doseCount,
                airPharmacyFormItems: ChargeUtils.chargeFormItemsToPrescriptionFormItems(oldFormItems),
                pharmacyNo: form.pharmacyNo,
            });
            const selectVendor = vendors.find((vendor) => vendor.checked);
            if (!selectVendor) continue;

            selectVendor.orderItems?.map((item, index) => {
                const oldItem = oldFormItems[index];
                if (oldItem.unitPrice != item.unitPrice) update.priceChanged = true;
                if (oldItem.status == 1) {
                    const storeTotalPrice = oldItem.totalPrice;
                    Object.assign(oldItem, item, { totalPrice: storeTotalPrice });
                } else {
                    Object.assign(oldItem, item);
                }
            });

            //有快递公司
            if (deliveryInfo?.deliveryCompany?.id != undefined) {
                const rsp = await this.getAirPharmacyAvailableCompanyWithChargeForm(form).catch((error) => new ABCError(error));
                if (rsp instanceof ABCError) {
                    continue;
                }

                deliveryInfo!.deliveryCompany = undefined;
                const first = _.first(rsp);
                if (first) {
                    const company = new DeliveryCompany();
                    deliveryInfo!.deliveryCompany = company;
                    company.id = first.id;
                    company.name = first.name;
                    deliveryInfo!.deliveryFee__ = first.deliveryPrice;
                }

                update.deliveryUpdate = true;
            }
        }

        return update;
    }

    public static createChargeFormItem(
        goodsInfo: GoodsInfo,
        usage?: {
            unit?: string;
            count?: number;
            dosageCount?: number;
            doctorId?: string;
            doctorName?: string;
            nurseId?: string;
            nurseName?: string;
            departmentId?: string;
            departmentName?: string;
        }
    ): ChargeFormItem {
        const formItem = new ChargeFormItem();
        const { unit = goodsInfo.unit, count = 1, dosageCount = 1 } = usage ?? {};

        formItem.keyId = UUIDGen.generate();
        formItem.name = goodsInfo.displayName;
        formItem.unitCount = count;
        formItem.unit = unit;
        formItem.unitPrice = goodsInfo.unitPriceWithUnit(unit);
        formItem.productId = goodsInfo.id;
        formItem.productType = goodsInfo.type;
        formItem.productSubType = goodsInfo.subType;
        formItem.localAdd = true; //本地添加标志
        formItem.status = ChargeFormItemStatus.unCharged;
        formItem.doseCount = dosageCount;
        formItem.useDismounting = goodsInfo.useDismounting(unit) ? 1 : 0;
        formItem.productInfo = goodsInfo;

        formItem.doctorId = usage?.doctorId;
        formItem.doctorName = usage?.doctorName;
        formItem.nurseId = usage?.nurseId;
        formItem.nurseName = usage?.nurseName;
        formItem.departmentId = usage?.departmentId;
        formItem.departmentName = usage?.departmentName;

        return formItem;
    }

    public static async validateChargeDetailData(detailData?: ChargeInvoiceDetailData, hasProcessRule?: boolean): Promise<boolean> {
        if (_.isEmpty(detailData?.chargeForms)) {
            await Toast.show("未添加收费项", { warning: true });
            return false;
        }

        const patient = detailData!.patient;
        const mobile = patient?.mobile;
        const hasAirPharmacyPrescription = detailData!.chargeForms?.find((item) => item.isAirPharmacy) != undefined;

        if (!ValidatorUtils.validatePhoneNumber(mobile, true)) return false;

        if (!_.isEmpty(patient?.name) && patient?.age == undefined) {
            await Toast.show("年龄不能为空", { warning: true });
            return false;
        }
        if ((hasAirPharmacyPrescription || patient?.age?.isValid || !_.isEmpty(patient?.mobile)) && _.isEmpty(patient?.name)) {
            await Toast.show("姓名不能为空", { warning: true });
            return false;
        }

        //加工袋数不能为空或者0
        for (const form of detailData?.chargeForms ?? []) {
            if (form.isDecoction) {
                const formItem = form.getFormItem(GoodsType.decoctionFee);
                if (formItem && form.processInfo) {
                    const isDecoction = form.processInfo.type == MedicineProcessType.decoction;
                    if (
                        !!hasProcessRule &&
                        isDecoction &&
                        (!form.processInfo?.processBagUnitCount || !form.processInfo?.totalProcessCount)
                    ) {
                        Toast.show("加工袋数不能为空").then();
                        return false;
                    }
                }
            } else if (form.isAirPharmacy && form.medicineStateScopeId == MedicineScopeId.daiJian) {
                // 空中药房-代煎-加工袋数不能为空
                const formItem = form.getFormItem(GoodsType.decoctionFee);
                if (formItem && form.usageInfo) {
                    if (!!hasProcessRule && (!form.usageInfo?.processBagUnitCount || !form.usageInfo?.totalProcessCount)) {
                        Toast.show("加工袋数不能为空").then();
                        return false;
                    }
                }
            }
        }

        return true;
    }

    //设置加工信息
    static setDecoctionInfo(chargeData: ChargeInvoiceDetailData, mobile?: string, decoctionForms?: ChargeForm[]): void {
        chargeData.contactMobile = mobile;
        _.remove(chargeData.chargeForms ?? [], (form) => form.isDecoction);
        decoctionForms && chargeData?.chargeForms?.push(...decoctionForms);
    }

    public static async closeChargeSheetWithUIAsk(chargeId: string, isNetworkDraft: boolean): Promise<void> {
        const select = await ChargeUiUtils.showCloseChargeSheetQueryDlg(isNetworkDraft);
        if (select !== DialogIndex.positive) return;

        const loadingDialog = new LoadingDialog("正在关闭...");
        loadingDialog.show();
        const rsp = await ChargeAgent.closeChargeSheet(chargeId).catch((error) => new ABCError(error));
        if (rsp instanceof ABCError) {
            await loadingDialog.fail(`关闭失败：${errorSummary(rsp)}`);
            return;
        }

        await loadingDialog.success(`关闭成功`);
        ABCNavigator.popUntil(URLProtocols.CHARGE_TAB, URLProtocols.CHARGE_TAB).then();
    }

    /**
     * 计处处方单剂重量
     * @param chargeForm
     */
    public static computePrescriptionDosageUnitWeight(chargeForm: ChargeForm): number {
        let dosageWeight = 0;
        for (const chargeFormItem of chargeForm.chargeFormItems!) {
            if (chargeFormItem.isDecoction || chargeFormItem.isDelivery) continue;
            if (chargeFormItem.unit == "g") {
                dosageWeight += chargeFormItem.unitCount ?? 0;
            }
        }

        return dosageWeight;
    }

    /**
     * 计算处方重量
     * @param chargeForm
     */
    public static computePrescriptionDosageWeight(chargeForm: ChargeForm): number {
        return this.computePrescriptionDosageUnitWeight(chargeForm) * (chargeForm.doseCount ?? 1);
    }

    /**
     * 根据处方重量，成品率计算服用天数
     */
    public static calcUsageDays(params: {
        totalDoseWeight: number;
        finishedRate?: number;
        isAirPharmacy: boolean;
        usageScopeId?: string;
        medicineStateScopeId?: MedicineScopeId;
        usageInfo?: UsageInfo;
    }): string | undefined {
        const { totalDoseWeight, finishedRate, isAirPharmacy, usageInfo, usageScopeId, medicineStateScopeId } = params;
        if (_.isNil(finishedRate)) return;
        const { usageLevel, freq } = usageInfo ?? {};

        // 空中药房才做处理
        if (!isAirPharmacy) return;

        // 制膏-袋装
        // 制膏-瓶装
        // 制丸-全部
        // 打粉-全部
        // 以上类型 才算服用天数
        if (
            usageScopeId === UsageScopeId.zhiWan ||
            usageScopeId === UsageScopeId.daFen ||
            medicineStateScopeId === MedicineScopeId.pingZhuang ||
            medicineStateScopeId === MedicineScopeId.daiZhuang
        ) {
            const matchedUL = usageLevel?.match(/^每次([0-9]+)g$/);
            const _usageLevelValue = matchedUL ? matchedUL[1] : 0;

            const matchedFreq = freq?.match(/^1日([0-9]+)次$/);
            const _freqValue = matchedFreq ? matchedFreq[1] : 0;

            const value = (totalDoseWeight * finishedRate) / (Number(_usageLevelValue) * Number(_freqValue));

            if (value && !isNaN(value) && isFinite(value)) {
                return `约服${Math.ceil(value)}天`;
            } else {
                return "";
            }
        }

        return;
    }

    /**
     * 刷新空中药房的usage,在供应商变化（打粉，药态)时重新计算用法
     * @param chargeForm
     */
    public static refreshAirAirPharmacyUsageInfo(chargeForm: ChargeForm): void {
        if (!chargeForm.isAirPharmacy) return;
        chargeForm.usageInfo = Object.assign(
            {},
            chargeForm.usageInfo ?? {},
            ChineseMedicineConfigProvider.getChinesePRWithUsage(chargeForm.usageScopeName__ ?? "") ??
                ChineseMedicineConfigProvider.getChinesePRWithSpecification(
                    ChineseMedicineSpecType.typeFromName(chargeForm.specification ?? "")
                )
        );

        ChargeUtils.refreshUsageDays(chargeForm);
        debugger;
    }

    public static refreshUsageDays(chargeForm: ChargeForm): void {
        if (chargeForm.usageInfo)
            chargeForm.usageInfo.usageDays =
                ChargeUtils.calcUsageDays({
                    totalDoseWeight: ChargeUtils.computePrescriptionDosageWeight(chargeForm),
                    finishedRate: chargeForm.vendor__?.finishedRate,
                    isAirPharmacy: true,
                    usageScopeId: chargeForm.usageScopeId,
                    medicineStateScopeId: chargeForm.medicineStateScopeId,
                    usageInfo: chargeForm.usageInfo,
                }) ?? chargeForm.usageInfo.usageDays;
    }

    public static calcMinDosageCount(chargeForm: ChargeForm): number {
        let minCount = 1;
        if (
            chargeForm.isAirPharmacy &&
            ChineseMedicineSpecType.typeFromName(chargeForm.specification ?? "") == ChineseMedicineSpecType.chinesePiece &&
            chargeForm.medicineStateScopeId == MedicineScopeId.daiJian
        ) {
            minCount = AirPharmacyUtils.MIN_DOSE_COUNT_WHEN_PIECE;
        }

        return minCount;
    }

    static filterRegistrationPromotions(detail: ChargeInvoiceDetailData): Promotion[] | undefined {
        let registrationPromotionsId: string | undefined = undefined;
        for (const chargeForm of detail.chargeForms ?? []) {
            if (chargeForm.sourceFormType == ChargeSourceFormType.registration) {
                registrationPromotionsId = chargeForm.chargeFormItems?.[0].id;
            }
        }
        const newPromotions: Promotion[] = [];
        detail.promotions?.forEach((item) => {
            const newItem = _.cloneDeep(item);
            newItem.discountPrice = item.discountPrice ?? 0;
            newItem.productItems = newItem.productItems?.filter((productItem) => productItem.id == registrationPromotionsId);
            // 营销卡项删除？？？
            // newItem.productItems?.forEach((productItem) => {
            //     newItem.discountPrice = (newItem.discountPrice ?? 0) + (productItem.discountPrice ?? 0);
            // });
            newPromotions.push(newItem);
        });
        return newPromotions;
    }

    static updateChargeChineseFormPharmacyWithProcess(
        detailData?: ChargeInvoiceDetailData,
        pharmacyInfoConfig?: InventoryClinicConfig
    ): void {
        let initGoodsTypeId: GoodsTypeId | undefined = undefined,
            pharmacyNo: number | undefined;
        detailData?.chargeForms?.forEach((form) => {
            if (form.isChinesePrescription) {
                initGoodsTypeId =
                    form?.specification == ChineseMedicineSpecType.fullNames()[1]
                        ? GoodsTypeId.medicineChineseGranule
                        : GoodsTypeId.medicineChinesePiece;
                pharmacyNo = form?.pharmacyNo;
                const processInfo = detailData?.chargeForms?.find(
                    (_form) => _form.isDecoction && (_form.processInfo?.chargeFormId == form.id || form.id === undefined)
                )?.processInfo;
                if (!!processInfo) {
                    Object.assign(processInfo, {
                        usageType: processInfo.type,
                        usageSubType: processInfo.subType,
                    });
                }
                const formItem = form.chargeFormItems?.[0];
                const transPharmacy = pharmacyInfoConfig?.getDefaultPharmacy({
                    departmentId: detailData?.departmentId,
                    goodsInfo: {
                        typeId: formItem?.goodsInfo?.typeId ?? initGoodsTypeId,
                        customTypeId: formItem?.goodsInfo?.customTypeId,
                    },
                    processInfo: processInfo,
                });
                if (!!transPharmacy && transPharmacy?.no != pharmacyNo) {
                    form.handleChangePharmacy(transPharmacy);
                }
            }
        });
    }
}
