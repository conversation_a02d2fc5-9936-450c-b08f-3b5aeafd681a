import React from "react";
import { Text, View } from "@hippy/react";
import { Color, Colors, Sizes, TextStyles } from "../../theme";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { BaseBlocPage } from "../../base-ui/base-page";
import { RealNameVerifyBloc } from "./real-name-verify-bloc";
import { AbcToolBarButtonStyle1 } from "../../base-ui/abc-app-library";
import FontWeight from "../../theme/font-weights";
import { SettingInputItem } from "./views/setting-view/setting-input-item";
import { SettingMultipleInputView } from "./views/setting-view/setting-multiple-input-view";
import { SettingIdCardItem } from "./views/setting-view/setting-idcard-item";
import { SettingPhoneView } from "./views/setting-view/setting-phone-view";
import { SettingRadioItem } from "./views/setting-view/setting-radio-item";
import { Patient } from "../../base-business/data/beans";
import { ABCStyles } from "@app/theme";
import { KeyboardListenerView } from "../../base-ui/views/keyboard-listener-view";
import { AbcView } from "@app/abc-mobile-ui";
import { IconFontView } from "../../base-ui";

interface RealNameVerifyProps {
    realNameId?: string;
    patient?: Patient;
    chargeSheetId?: string;
}
export class RealNameVerify extends BaseBlocPage<RealNameVerifyProps, RealNameVerifyBloc> {
    static async show(props?: RealNameVerifyProps): Promise<string> {
        return ABCNavigator.navigateToPage(<RealNameVerify {...props} />);
    }

    constructor(props: RealNameVerifyProps) {
        super(props);
        this.bloc = new RealNameVerifyBloc({ realNameId: props?.realNameId, patient: props?.patient, chargeSheetId: props?.chargeSheetId });
        this.addDisposable(this);
    }

    getStatusBarColor(): Color {
        return Colors.retail_theme_topbar;
    }
    getAppBarBgColor(): Color {
        return Colors.retail_theme_topbar;
    }
    getAppBarBtnColor(): Color {
        return Colors.white;
    }
    getBackgroundColor(): Color {
        return Colors.retail_bg_grey2;
    }

    getTouchEmptyBlurTextInput(): boolean {
        return true;
    }

    getAPPBarCustomTitle(): JSX.Element | undefined {
        return <Text style={[TextStyles.t18MW.copyWith({ lineHeight: Sizes.dp26 })]}>{"实名登记"}</Text>;
    }

    getAppBarBackIcon(): JSX.Element | undefined {
        return (
            <AbcView style={{ paddingHorizontal: Sizes.dp16, paddingVertical: Sizes.dp12 }} onClick={() => ABCNavigator.pop()}>
                <IconFontView name={"s-backto-line"} size={Sizes.dp24} color={Colors.white} />
            </AbcView>
        );
    }

    _renderBottomView(): JSX.Element {
        return (
            <KeyboardListenerView>
                <View
                    style={[
                        ABCStyles.topLine,
                        Sizes.paddingLTRB(Sizes.dp16, Sizes.dp12, Sizes.dp16, Sizes.dp8),
                        { backgroundColor: Colors.white, borderColor: Colors.border_color_normal },
                    ]}
                >
                    <AbcToolBarButtonStyle1
                        text={"保存"}
                        style={{
                            backgroundColor: Colors.B7,
                            fontColor: Colors.white,
                            fontWeight: FontWeight.medium,
                            borderRadius: Sizes.dp6,
                            borderWidth: 0,
                        }}
                        onClick={() => this.bloc.requestConfirm()}
                    />
                </View>
            </KeyboardListenerView>
        );
    }

    renderRegisterView(): JSX.Element {
        const state = this.bloc.currentState;
        const registerIdentity = state.registerIdentity;
        const ageList = [
            {
                unit: "岁",
                value: registerIdentity?.age?.year,
                max: 200,
                onBlur: (value?: string) => {
                    this.bloc.requestChangeAge(value);
                },
            },
            {
                unit: "月",
                value: registerIdentity?.age?.month,
                max: 12,
                onBlur: (value?: string) => {
                    this.bloc.requestChangeAge(undefined, value);
                },
            },
            {
                unit: "天",
                max: 31,
                value: registerIdentity?.age?.day,
                onBlur: (value?: string) => {
                    this.bloc.requestChangeAge(undefined, undefined, value);
                },
            },
        ];
        return (
            <View style={{ flex: 1 }}>
                <View style={{ backgroundColor: Colors.white }}>
                    <SettingInputItem
                        key={"real_name"}
                        autoFocus={true}
                        title={"患者姓名"}
                        content={registerIdentity?.name}
                        placeholder={"输入姓名"}
                        showBottomLine={true}
                        required={true}
                        maxLength={64}
                        onChange={(text: string) => {
                            this.bloc.requestChangeRegisterName(text);
                        }}
                    />
                    <SettingMultipleInputView key={"real_age"} title={"年龄"} data={ageList} showBottomLine={true} />
                    <SettingRadioItem
                        key={"real_sex"}
                        title={"性别"}
                        options={["男", "女"]}
                        check={registerIdentity?.sex}
                        showBottomLine={true}
                        onChange={(value) => this.bloc.requestChangeSex(value)}
                    />
                    <SettingIdCardItem
                        key={"real_idcard"}
                        showBottomLine={true}
                        idCard={registerIdentity?.idCard}
                        idCardType={registerIdentity?.idCardType}
                        title={"证件号"}
                        onChangeIdCardType={(value) => this.bloc.requestUpdateIdCardType(value)}
                        onChangeIdCard={(value) => this.bloc.requestUpdateIdCard(value)}
                    />
                    <SettingPhoneView
                        key={"real_phone"}
                        title={"手机"}
                        mobile={registerIdentity?.mobile}
                        showCountryCode={false}
                        onChangeMobile={(value) => this.bloc.requestUpdateMobile(value)}
                    />
                </View>
            </View>
        );
    }

    renderContent(): JSX.Element {
        return (
            <View style={{ flex: 1 }}>
                {this.renderRegisterView()}
                {this._renderBottomView()}
            </View>
        );
    }
}
