import React from "react";
import { Text, View } from "@hippy/react";
import { AbcScrollView } from "../../../base-ui/views/abc-scroll-view";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../../theme";
import { DividerLine, IconFontView, SizedBox } from "../../../base-ui";
import { AbcView } from "../../../base-ui/views/abc-view";
import { CustomInput } from "../../../base-ui/input/custom-input";
import { PrecisionLimitFormatter } from "../../../base-ui/utils/formatter";
import { SwitchView } from "../../../base-ui/views/switch-view";
import { AbcToolBarButtonStyle1 } from "../../../base-ui/abc-app-library";
import FontWeight from "../../../theme/font-weights";
import { ChargeFormItem, ChargeInvoiceDetailData } from "../../data/charge-beans";
import { showBottomPanel } from "../../../base-ui/abc-app-library/panel";
import { pxToDp } from "../../../base-ui/utils/ui-utils";
import { BaseBlocPage } from "../../../base-ui/base-page";
import { GoodsDetailInfoDialogBloc } from "./goods-detail-info-dialog-bloc";
import { ABCUtils } from "../../../base-ui/utils/utils";
import { DialogTitle } from "../views/dialog-title";
import { AbcTextInput } from "../../../base-ui/views/abc-text-input";
import { keyboardListener } from "../../../common-base-module/utils/keyboard-listener";
import { UIUtils } from "../../../base-ui/utils";
import { DeviceUtils } from "@app/utils";
import { AbcTouchableHighlight } from "../../../base-ui/views/abc-touchable-highlight";
import { AbcText } from "@app/abc-mobile-ui";
import { KeyboardListenerView } from "../../../base-ui/views/keyboard-listener-view";

interface GoodsDetailInfoDialogProps {
    formItem: ChargeFormItem;
    keyId: string;
    sourceFormType: number;
    detailData?: ChargeInvoiceDetailData;
}
export class GoodsDetailInfoDialog extends BaseBlocPage<GoodsDetailInfoDialogProps, GoodsDetailInfoDialogBloc> {
    static show(props: GoodsDetailInfoDialogProps): Promise<ChargeInvoiceDetailData> {
        const height = DeviceUtils.isAndroid()
            ? UIUtils.getSafeContentHeight() - pxToDp(566)
            : UIUtils.getSafeContentHeight() - pxToDp(528);
        return showBottomPanel(<GoodsDetailInfoDialog {...props} />, { topMaskHeight: height });
    }
    getAppBar(): JSX.Element {
        return <View />;
    }
    getShowStatusBar(): boolean {
        return false;
    }

    constructor(props: GoodsDetailInfoDialogProps) {
        super(props);
        this.bloc = new GoodsDetailInfoDialogBloc({
            formItem: props.formItem,
            keyId: props.keyId,
            sourceFormType: props.sourceFormType,
            detailData: props.detailData,
        });
        this.addDisposable(this.bloc);
    }

    componentDidMount(): void {
        super.componentDidMount();
        keyboardListener
            .subscribe((visible) => {
                if (!visible.visible) {
                    AbcTextInput.focusInput?.blur();
                }
            })
            .addToDisposableBag(this);
    }

    renderOnlyReadTextView(options: {
        title?: string;
        value?: number;
        color?: string;
        priceColor?: string; // 金额的颜色
        onClick?: () => void;
    }): JSX.Element {
        const { title, value, color, priceColor, onClick } = options || {};
        return (
            <View style={ABCStyles.rowAlignCenter}>
                <Text style={TextStyles.t13NT2.copyWith({ lineHeight: Sizes.dp21, color: color ?? Colors.t2 })}>{title ?? ""}</Text>
                <SizedBox width={Sizes.dp16} />
                <AbcText style={TextStyles.t16NB.copyWith({ lineHeight: Sizes.dp24, color: priceColor ?? Colors.black })} onClick={onClick}>
                    {ABCUtils.formatPrice(value ?? 0)}
                </AbcText>
            </View>
        );
    }

    // 实价
    _renderActivePriceView(): JSX.Element {
        const state = this.bloc.currentState;
        const { currentFormItem, hasSingleBargainPermission } = state;
        if (!currentFormItem) return <View />;
        return (
            <View style={ABCStyles.rowAlignCenter}>
                <CustomInput
                    disable={!(hasSingleBargainPermission && currentFormItem.isCanAdjustment)}
                    style={{ width: Sizes.dp100, textAlign: "right" }}
                    containerStyle={{ height: Sizes.dp34 }}
                    borderType={"boxBorder"}
                    type={"input"}
                    value={currentFormItem.totalPriceRatioPercent > 100 ? "-" : currentFormItem.totalPriceRatioPercent}
                    placeholder={"折扣"}
                    placeholderColor={Colors.T4}
                    formatter={(oldValue, newValue) => {
                        const value = Number(newValue);
                        if (isNaN(value)) return "";
                        if (value < 0 || value > 100) return oldValue;
                        return newValue;
                    }}
                    textStyle={TextStyles.t16NT2.copyWith({ color: Colors.T1 })}
                    alwaysShowUtil={true}
                    unit={"%"}
                    selectTextOnFocus={false}
                    onFocus={() => {
                        this.bloc.updateInputFocus("discount", true);
                    }}
                    onBlur={(value) => {
                        this.bloc.updateInputFocus("discount", false);
                        if (!value || Number(value) == currentFormItem?.totalPriceRatioPercent) return;
                        const discount = Number(value);
                        this.bloc.requestDiscountChanged(discount);
                    }}
                    unitStyle={{
                        marginRight: 0,
                        width: Sizes.dp40,
                        textAlign: "center",
                        color: Colors.t2,
                    }}
                />
                <SizedBox width={Sizes.dp8} />
                <CustomInput
                    // autoFocus={true}
                    disable={!(hasSingleBargainPermission && currentFormItem.isCanAdjustment)}
                    style={{ width: Sizes.dp100, textAlign: "right" }}
                    borderType={"boxBorder"}
                    type={"input"}
                    value={ABCUtils.formatPrice(currentFormItem?._displayUnitPrice)}
                    placeholder={"实价"}
                    placeholderColor={Colors.T4}
                    formatter={PrecisionLimitFormatter(2)}
                    textStyle={TextStyles.t16NT2.copyWith({ color: Colors.T1 })}
                    containerStyle={{
                        ...Sizes.paddingLTRB(Sizes.dp8, 0, Sizes.dp8, 0),
                        height: Sizes.dp34,
                    }}
                    alwaysShowUtil={false}
                    selectTextOnFocus={false}
                    onFocus={() => {
                        this.bloc.updateInputFocus("price", true);
                    }}
                    onBlur={(value) => {
                        this.bloc.updateInputFocus("price", false);
                        if (!value || Number(value) == currentFormItem?._displayUnitPrice) return;
                        const unitPrice = Number(value);
                        this.bloc.requestUnitPriceChanged(unitPrice);
                    }}
                />
            </View>
        );
    }

    // 毛利金额
    _renderProfitAmountView(): JSX.Element {
        const state = this.bloc.currentState;
        const { currentFormItem, disabled, hasSingleBargainPermission } = state;
        if (!currentFormItem) return <View />;
        // 毛利率
        const profit = `${((currentFormItem.grossProfitRate ?? 0) * 100).toFixed(2)}`;
        return (
            <View style={ABCStyles.rowAlignCenter}>
                <Text
                    style={TextStyles.t13NT2.copyWith({
                        color: (currentFormItem.grossProfitRate ?? 0) >= 0 ? Colors.T2 : Colors.retail_Y1,
                        lineHeight: Sizes.dp21,
                    })}
                    numberOfLines={1}
                    ellipsizeMode="tail"
                >{`毛利：${profit}%`}</Text>
                <SizedBox width={Sizes.dp8} />
                <View style={ABCStyles.rowAlignCenter}>
                    <CustomInput
                        // autoFocus={true}
                        borderType={"boxBorder"}
                        type={"input"}
                        disable={disabled || !(hasSingleBargainPermission && currentFormItem.isCanAdjustment)}
                        style={{ width: Sizes.dp100, textAlign: "right" }}
                        value={ABCUtils.formatPrice(currentFormItem?._displayTotalPrice)}
                        formatter={PrecisionLimitFormatter(2)}
                        disableStyle={TextStyles.t16NT2}
                        textStyle={TextStyles.t16NT2.copyWith({ color: Colors.T1 })}
                        containerStyle={{
                            ...Sizes.paddingLTRB(Sizes.dp8, 0, Sizes.dp8, 0),
                            height: Sizes.dp34,
                        }}
                        onFocus={() => {
                            this.bloc.updateInputFocus("totalPrice", true);
                        }}
                        onBlur={() => {
                            this.bloc.updateInputFocus("totalPrice", false);
                        }}
                        onChange={(value) => {
                            if (!value || Number(value) == currentFormItem?._displayTotalPrice) return;
                            const totalPrice = Number(value);
                            this.bloc.requestTotalPriceChanged(totalPrice);
                        }}
                    />
                </View>
            </View>
        );
    }

    renderContent(): JSX.Element {
        const state = this.bloc.currentState;
        const { currentFormItem, isSingleGift, disabledChangeUnitCount, disabledChangeGoods } = state;
        if (!currentFormItem) return <View />;
        const goodsInfo = currentFormItem?.goodsInfo;
        const batchLength = currentFormItem?.chargeFormItemBatchInfos?.length ?? 0;
        const batchNo = batchLength > 0 ? currentFormItem?.chargeFormItemBatchInfos?.[0]?.batchNo : "";
        let batchInfo = "";
        if (batchLength > 1) {
            batchInfo = `${batchNo} 等 ${batchLength} 个批次`;
        } else if (batchLength == 1) {
            batchInfo = batchNo ?? "";
        }

        // 是否有可用优惠
        // const hasAvailableOffers = !!currentFormItem?.singlePromotions?.length;
        let promotionStr = "";
        if (!!currentFormItem?.singlePromotions?.length) {
            promotionStr = !!currentFormItem?.checkedPromotion
                ? currentFormItem?.checkedPromotion.transCalc2promotionStr({
                      displayPackageUnit: currentFormItem.unit,
                      goodsId: currentFormItem.productId,
                  }) ?? currentFormItem?.name
                : "有可用优惠";
        }
        const displayDiscountPrice = currentFormItem?.checkedPromotion?.displayDiscountPrice;
        let discountPriceStr = promotionStr;
        if (!!currentFormItem?.checkedPromotion) {
            discountPriceStr = `${promotionStr}｜${ABCUtils.formatPrice(displayDiscountPrice ?? 0)}`;
        }

        return (
            <View style={{ backgroundColor: Colors.white, flex: 1 }}>
                <View style={{ flex: 1 }}>
                    <DialogTitle
                        title={currentFormItem?.displayName}
                        subTitle={`${goodsInfo?.displaySpec} ${goodsInfo?.manufacturer ?? ""} ${
                            !!goodsInfo?.medicalFeeGrade2Str ? "【" + goodsInfo?.medicalFeeGrade2Str + "】" : ""
                        }`}
                        containerStyle={[Sizes.paddingLTRB(Sizes.dp16, Sizes.dp16, Sizes.dp16, 0)]}
                    />
                    <AbcScrollView style={{ flex: 1, backgroundColor: Colors.white }} showsVerticalScrollIndicator={false}>
                        <View
                            style={[
                                ABCStyles.rowAlignCenterSpaceBetween,
                                ABCStyles.bottomLine,
                                {
                                    paddingVertical: Sizes.dp16,
                                    marginLeft: Sizes.dp16,
                                    marginRight: Sizes.dp20,
                                    borderColor: Colors.border_color_light,
                                    height: Sizes.dp56,
                                },
                            ]}
                        >
                            <Text style={TextStyles.t16NT2}>数量</Text>
                            <View style={ABCStyles.rowAlignCenter}>
                                <CustomInput
                                    borderType={"boxBorder"}
                                    type={"input"}
                                    disable={disabledChangeUnitCount}
                                    style={{ width: Sizes.dp100, textAlign: "right" }}
                                    value={currentFormItem.unitCount ?? 0}
                                    formatter={PrecisionLimitFormatter(2)}
                                    error={!Boolean(currentFormItem.unitCount)}
                                    containerStyle={{
                                        ...Sizes.paddingLTRB(Sizes.dp6, 1, 0, 1),
                                        height: Sizes.dp34,
                                    }}
                                    textStyle={{ ...TextStyles.t16NB, marginLeft: Sizes.dp6 }}
                                    onBlur={(value) => {
                                        this.bloc.requestUpdateUnitCount(
                                            currentFormItem,
                                            !!value || value == "0" ? Number(value) : undefined
                                        );
                                    }}
                                    onChangeUnit={(unit) => this.bloc.requestUpdateUnit?.(currentFormItem, unit)}
                                    alwaysShowUtil={true}
                                    unit={currentFormItem?.unit}
                                    unitList={currentFormItem?.goodsInfo?.sellUnits}
                                    unitStyle={{ color: Colors.t2, marginRight: 0, width: Sizes.dp40, textAlign: "center" }}
                                />
                            </View>
                        </View>
                        <GoodsInfoWithArrow
                            title={"批次"}
                            content={batchInfo}
                            key={"batch"}
                            showPressEffect={!!batchInfo && !isSingleGift && !!currentFormItem?.chargeFormItemBatchInfos?.length}
                            canEdit={!isSingleGift && !!currentFormItem?.chargeFormItemBatchInfos?.length}
                            onClick={() => this.bloc.requestUpdateBatchInfo(currentFormItem)}
                        />
                        <DividerLine lineHeight={Sizes.dp8} color={Colors.bg_grey1} />
                        <GoodsDisplayContent
                            title={"单价"}
                            content={`${ABCUtils.formatPrice(currentFormItem?.sourceUnitPrice ?? 0)}`}
                            key={"unitPrice"}
                        />
                        {!!currentFormItem?.singlePromotions?.length && (
                            <GoodsInfoWithArrow
                                title={"单项优惠"}
                                content={discountPriceStr}
                                key={"promotionItem"}
                                showBottomLine={true}
                                showPressEffect={!!discountPriceStr && !isSingleGift}
                                canEdit={!isSingleGift && !!currentFormItem?.singlePromotions?.length}
                                onClick={() => this.bloc.requestUpdateDiscount(currentFormItem)}
                            />
                        )}
                        <View
                            style={[
                                ABCStyles.rowAlignCenterSpaceBetween,
                                ABCStyles.bottomLine,
                                {
                                    paddingVertical: Sizes.dp16,
                                    marginLeft: Sizes.dp16,
                                    marginRight: Sizes.dp20,
                                    borderColor: Colors.retail_border_light,
                                    height: Sizes.dp56,
                                },
                            ]}
                        >
                            <Text style={TextStyles.t16NT2}>{"实价"}</Text>
                            {this._renderActivePriceView()}
                            {/*注释的是显示是文本，点击是聚焦框的逻辑*/}
                            {/*{!clickedRealPrice &&*/}
                            {/*    this.renderOnlyReadTextView({*/}
                            {/*        title: `折扣率：${*/}
                            {/*            currentFormItem.totalPriceRatioPercent > 100 ? "-" : currentFormItem.totalPriceRatioPercent*/}
                            {/*        }%`,*/}
                            {/*        value: currentFormItem?._displayUnitPrice,*/}
                            {/*        priceColor: disabled ? Colors.t2 : Colors.t1,*/}
                            {/*        onClick: !disabled ? () => this.bloc.requestTriggerRealPrice() : undefined,*/}
                            {/*    })}*/}
                            {/*{clickedRealPrice && this._renderActivePriceView()}*/}
                        </View>
                        <View
                            style={[
                                ABCStyles.rowAlignCenterSpaceBetween,
                                {
                                    paddingVertical: Sizes.dp16,
                                    marginLeft: Sizes.dp16,
                                    marginRight: Sizes.dp20,
                                    height: Sizes.dp56,
                                },
                            ]}
                        >
                            <Text style={TextStyles.t16NT2}>{"金额"}</Text>
                            {this._renderProfitAmountView()}
                            {/*注释的是显示是文本，点击是聚焦框的逻辑*/}
                            {/*{!clickedAmount &&*/}
                            {/*    this.renderOnlyReadTextView({*/}
                            {/*        title: `毛利：${profit}%`,*/}
                            {/*        value: currentFormItem?._displayTotalPrice,*/}
                            {/*        color: (currentFormItem.grossProfitRate ?? 0) >= 0 ? Colors.T2 : Colors.retail_Y1,*/}
                            {/*        priceColor: disabled ? Colors.t2 : Colors.t1,*/}
                            {/*        onClick: !disabled ? () => this.bloc.requestTriggerAmount() : undefined,*/}
                            {/*    })}*/}
                            {/*{clickedAmount && (*/}
                            {/*    <View style={ABCStyles.rowAlignCenter}>*/}
                            {/*        <Text*/}
                            {/*            style={TextStyles.t13NT2.copyWith({*/}
                            {/*                color: (currentFormItem.grossProfitRate ?? 0) >= 0 ? Colors.T2 : Colors.retail_Y1,*/}
                            {/*                lineHeight: Sizes.dp21,*/}
                            {/*            })}*/}
                            {/*            numberOfLines={1}*/}
                            {/*            ellipsizeMode="tail"*/}
                            {/*        >{`毛利：${profit}%`}</Text>*/}
                            {/*        <SizedBox width={Sizes.dp8} />*/}
                            {/*        <View style={ABCStyles.rowAlignCenter}>*/}
                            {/*            <CustomInput*/}
                            {/*                autoFocus={true}*/}
                            {/*                borderType={"boxBorder"}*/}
                            {/*                type={"input"}*/}
                            {/*                disable={disabled}*/}
                            {/*                style={{ width: Sizes.dp100, textAlign: "right", height: Sizes.dp34 }}*/}
                            {/*                value={ABCUtils.formatPrice(currentFormItem?._displayTotalPrice)}*/}
                            {/*                formatter={PrecisionLimitFormatter(2)}*/}
                            {/*                disableStyle={TextStyles.t14NT2}*/}
                            {/*                textStyle={TextStyles.t14NT2.copyWith({ color: Colors.T1 })}*/}
                            {/*                containerStyle={{*/}
                            {/*                    ...Sizes.paddingLTRB(Sizes.dp8, 0, Sizes.dp8, 0),*/}
                            {/*                }}*/}
                            {/*                onFocus={() => {*/}
                            {/*                    this.bloc.updateInputFocus("totalPrice", true);*/}
                            {/*                }}*/}
                            {/*                onBlur={() => {*/}
                            {/*                    this.bloc.updateInputFocus("totalPrice", false);*/}
                            {/*                }}*/}
                            {/*                onChange={(value) => {*/}
                            {/*                    if (!value || Number(value) == currentFormItem?._displayTotalPrice) return;*/}
                            {/*                    const totalPrice = Number(value);*/}
                            {/*                    this.bloc.requestTotalPriceChanged(totalPrice);*/}
                            {/*                }}*/}
                            {/*            />*/}
                            {/*        </View>*/}
                            {/*    </View>*/}
                            {/*)}*/}
                        </View>
                        <DividerLine lineHeight={Sizes.dp8} color={Colors.bg_grey1} />
                        <SetPresentView
                            isPresent={currentFormItem.isGifted}
                            canEdit={state.canEditGiftStatus}
                            onClick={(value) => this.bloc.requestSetPresent(value)}
                        />
                    </AbcScrollView>
                </View>
                {!isSingleGift && (
                    <KeyboardListenerView>
                        <BottomButton
                            showIndicator={state.calculating}
                            canEditDelete={!disabledChangeGoods}
                            onDelete={() => this.bloc.requestDeleteGoods()}
                            onSave={() => this.bloc.requestSaveGoods()}
                        />
                    </KeyboardListenerView>
                )}
            </View>
        );
    }
}

interface GoodsDisplayContentProps {
    title: string;
    count?: number;
    content?: string;
}
const GoodsDisplayContent: React.FC<GoodsDisplayContentProps> = ({ title, count, content }) => {
    return (
        <View
            style={[
                ABCStyles.rowAlignCenterSpaceBetween,
                ABCStyles.bottomLine,
                { paddingVertical: Sizes.dp16, marginLeft: Sizes.dp16, marginRight: Sizes.dp20, borderColor: Colors.retail_border_light },
            ]}
        >
            <Text style={TextStyles.t16NT2}>{title ?? ""}</Text>
            <View style={ABCStyles.rowAlignCenter}>
                <Text style={TextStyles.t16NB}>{count ?? ""}</Text>
                {!!content && <SizedBox width={Sizes.dp8} />}
                {!!content && <Text style={TextStyles.t16NT2}>{content ?? ""}</Text>}
            </View>
        </View>
    );
};

interface GoodsInfoWithArrowProps {
    title: string;
    content: string;
    canEdit?: boolean;
    showBottomLine?: boolean;
    showPressEffect?: boolean; // 是否显示按压效果
    onClick?: () => void;
}
const GoodsInfoWithArrow: React.FC<GoodsInfoWithArrowProps> = ({
    title,
    content,
    onClick,
    canEdit,
    showBottomLine,
    showPressEffect = false,
}) => {
    return (
        <AbcTouchableHighlight underlayColor={showPressEffect ? Colors.cp_pressed : ""}>
            <AbcView onClick={canEdit ? onClick : undefined} style={ABCStyles.rowAlignCenterSpaceBetween}>
                <View
                    style={[
                        ABCStyles.rowAlignCenterSpaceBetween,
                        showBottomLine ? { ...ABCStyles.bottomLine, borderColor: Colors.retail_border_light } : {},
                        {
                            paddingVertical: Sizes.dp16,
                            marginLeft: Sizes.dp16,
                            marginRight: Sizes.dp20,
                            height: Sizes.dp56,
                            flex: 1,
                        },
                    ]}
                >
                    <Text style={TextStyles.t16NT2}>{title ?? ""}</Text>
                    <View style={[ABCStyles.rowAlignCenter, { marginLeft: Sizes.dp14, flex: 1, justifyContent: "flex-end" }]}>
                        <Text
                            style={[TextStyles.t16NB.copyWith({ color: !!content ? Colors.black : Colors.t2 }), { flexShrink: 1 }]}
                            numberOfLines={1}
                        >
                            {!!content ? content : "-"}
                        </Text>
                    </View>
                </View>
                {canEdit && (
                    <View style={{ position: "absolute", right: Sizes.dp10 }}>
                        <IconFontView name={"s-arrowright-right-line-full"} size={Sizes.dp8} color={Colors.retail_T3} />
                    </View>
                )}
            </AbcView>
        </AbcTouchableHighlight>
    );
};

// 设置赠品
const SetPresentView: React.FC<{ isPresent: boolean; canEdit?: boolean; onClick?: (on: boolean) => void }> = ({
    isPresent,
    canEdit,
    onClick,
}) => {
    return (
        <View
            style={[
                ABCStyles.rowAlignCenterSpaceBetween,
                {
                    padding: Sizes.dp16,
                    backgroundColor: Colors.white,
                },
            ]}
        >
            <Text style={TextStyles.t16NT2}>设为赠品</Text>
            <SwitchView
                style={{ width: Sizes.dp40, height: Sizes.dp24 }}
                value={isPresent}
                disabled={!canEdit}
                disableColor={Colors.retail_B7_24}
                onChanged={(value) => onClick?.(value)}
            />
        </View>
    );
};

const BottomButton: React.FC<{
    showIndicator?: boolean;
    canEditDelete?: boolean;
    canEditSave?: boolean;
    onDelete?: () => void;
    onSave?: () => void;
}> = ({ showIndicator, canEditDelete = true, canEditSave = true, onDelete, onSave }) => {
    return (
        <View
            style={{
                backgroundColor: Colors.white,
                ...ABCStyles.rowAlignCenter,
                ...ABCStyles.topLine,
                borderColor: Colors.border_color_normal,
                ...Sizes.paddingLTRB(Sizes.dp16, Sizes.dp12, Sizes.dp16, Sizes.dp8),
            }}
        >
            <AbcToolBarButtonStyle1
                text={"删除"}
                style={{
                    backgroundColor: canEditDelete ? Colors.retail_red3 : Colors.retail_cp_grey,
                    fontColor: canEditDelete ? Colors.retail_R2 : Colors.retail_T3,
                    fontSize: Sizes.dp16,
                    fontWeight: FontWeight.medium,
                    width: Sizes.dp100,
                    height: Sizes.dp40,
                    borderRadius: Sizes.border_radius_small,
                    borderWidth: 0,
                }}
                showIndicator={showIndicator}
                onClick={canEditDelete ? onDelete : undefined}
            />
            <SizedBox width={Sizes.dp8} />
            <AbcToolBarButtonStyle1
                text={"保存"}
                style={{
                    backgroundColor: canEditSave ? Colors.B7 : Colors.retail_cp_grey,
                    fontColor: canEditSave ? Colors.white : Colors.retail_T3,
                    fontSize: Sizes.dp16,
                    fontWeight: FontWeight.medium,
                    height: Sizes.dp40,
                    borderRadius: Sizes.border_radius_small,
                    borderWidth: 0,
                }}
                showIndicator={showIndicator}
                onClick={canEditSave ? onSave : undefined}
            />
        </View>
    );
};
