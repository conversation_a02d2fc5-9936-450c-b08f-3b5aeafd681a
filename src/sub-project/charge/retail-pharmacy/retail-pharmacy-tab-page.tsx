import React from "react";
import { View } from "@hippy/react";
import { Color, Colors, Sizes, TextStyles } from "../../theme";
import { IconFontView, UniqueKey } from "../../base-ui";
import { AbcModuleTabPage, ModuleTabItem } from "../../views/base-tab-page";
import { RetailPharmacyInvoicePage } from "./retail-pharmacy-invoice-page";
import { RetailPharmacyList } from "./retail-pharmacy-list";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { AbcPopMenuV2, MenuItem } from "../../base-ui/views/pop-menu";
import { RetailPharmacyMenuItemValue } from "./data/beans";
import { isNil } from "lodash";
import { userCenter } from "../../user-center";
import { RetailPharmacyDraftManager } from "../data/retail-pharmacy-draft-manager";
import { AbcView } from "@app/abc-mobile-ui";
import { OnlinePropertyConfigProvider } from "../../data/online-property-config-provder";

interface RetailPharmacyTabPageProps {
    tabIndex?: number;
    localDraftId?: string;
    chargeSheetId?: string;
    fromQuickScan?: boolean;
}
interface RetailPharmacyTabPageState {
    localDraftId?: string;
}

export class RetailPharmacyTabPage extends AbcModuleTabPage<RetailPharmacyTabPageProps, RetailPharmacyTabPageState, undefined> {
    invoicePageRef = React.createRef<RetailPharmacyInvoicePage>();
    listPageRef = React.createRef<RetailPharmacyList>();
    tabsViewProps = {
        titleBorderStyle: { alignSelf: "center", width: Sizes.dp16, left: undefined, right: undefined },
        tabStyle: {
            marginRight: Sizes.dp44,
            ...TextStyles.t18NT2.copyWith({ color: Colors.s2Mask75, lineHeight: Sizes.dp26 }),
        },
        tabsStyle: {
            flex: 1,
            justifyContent: "center",
            backgroundColor: Colors.retail_theme_topbar,
            height: Sizes.dp44,
        },
        lineColor: Colors.white,
        currentStyle: {
            ...TextStyles.t18MW.copyWith({ lineHeight: Sizes.dp26 }),
        },
        swipeEnabled: false,
    };

    getStatusBarColor(): Color {
        return Colors.retail_theme_topbar;
    }

    get isRetailPermission(): boolean {
        return !!userCenter.clinic?.isRetail;
    }

    constructor(props: RetailPharmacyTabPageProps) {
        super(props);
        this.currentTabIndex = props.tabIndex ?? 0;
        this.state = {
            localDraftId: props.localDraftId,
        };
    }

    componentDidMount(): void {
        super.componentDidMount();
        // 读取一次收费配置接口，便于零售开单读取议价相关的配置的更新
        OnlinePropertyConfigProvider.instance.getChargeConfig(false);
    }

    async getLocalDraftId(): Promise<string> {
        const data = await RetailPharmacyDraftManager.instance.getAllDrafts();
        let localDraftId;
        if (!!data?.length && data[0]?.status == 1000 && !isNil(data[0]?.localDraftId) && data[0]?.localDraftId != "undefined") {
            localDraftId = data[0]?.localDraftId;
        }
        return !!localDraftId ? localDraftId : "";
    }

    handleChangeTab(index: number): void {
        super.handleChangeTab(index);
        if (this.isRetailPermission) {
            if (index === 0) {
                // 初始化零售页面
                this.getLocalDraftId().then((localDraftId) => {
                    if (!!localDraftId) {
                        this.setState({ localDraftId }, () => {
                            // 在状态更新后调用 init
                            this.invoicePageRef.current?.init?.({
                                localDraftId: this.state.localDraftId,
                                fromQuickScan: this.props?.fromQuickScan,
                            });
                        });
                    } else {
                        this.invoicePageRef.current?.init?.({ fromQuickScan: this.props?.fromQuickScan });
                    }
                });
            } else if (index === 1) {
                // 初始化零售单页面
                this.invoicePageRef.current?.saveDraft?.();
                this.listPageRef.current?.init?.();
            }
        } else {
            this.listPageRef.current?.init?.();
        }
    }

    renderLeftSuffix(hasMR = true): JSX.Element {
        return (
            <AbcView
                style={{
                    ...Sizes.paddingLTRB(Sizes.dp16, Sizes.dp14, Sizes.dp16, Sizes.dp10),
                    marginRight: hasMR ? Sizes.dp27 : 0,
                }}
                onClick={() => {
                    // 如果有零售整个模块权限
                    if (this.isRetailPermission) {
                        if (this.currentTabIndex === 0) {
                            // 优先调用零售页的返回逻辑
                            this.invoicePageRef.current?.onBackClick?.() ?? ABCNavigator.pop();
                        } else if (this.currentTabIndex === 1) {
                            // 零售单页的返回逻辑（如有）
                            this.listPageRef.current?.onBackClick?.() ?? ABCNavigator.pop();
                        } else {
                            ABCNavigator.pop();
                        }
                    } else {
                        ABCNavigator.pop();
                    }
                }}
            >
                <IconFontView name={"s-backto-line"} size={Sizes.dp24} color={Colors.white} />
            </AbcView>
        );
    }

    private _createMenuItemsList(): MenuItem<number>[] {
        const menuItems: MenuItem<number>[] = [];
        menuItems.push(
            new MenuItem({
                value: RetailPharmacyMenuItemValue.extractBill,
                icon: <IconFontView name={"list-fill"} color={Colors.white} size={Sizes.dp24} style={{ bottom: -Sizes.dp1 }} />,
                text: "提取挂单",
                textStyle: { fontSize: Sizes.dp16, fontColor: Colors.white },
            })
        );

        menuItems.push(
            new MenuItem({
                value: RetailPharmacyMenuItemValue.registerPrescription,
                icon: <IconFontView name={"s-paperrx-fill"} color={Colors.white} size={Sizes.dp24} style={{ bottom: -Sizes.dp1 }} />,
                text: "登记纸质处方",
                textStyle: { fontSize: Sizes.dp16, fontColor: Colors.white },
            })
        );

        menuItems.push(
            new MenuItem({
                value: RetailPharmacyMenuItemValue.pharmacyPrescription,
                icon: <IconFontView name={"s-cooprx-fill"} color={Colors.white} size={Sizes.dp24} style={{ bottom: -Sizes.dp1 }} />,
                text: "合作诊所处方",
                textStyle: { fontSize: Sizes.dp16, fontColor: Colors.white },
            })
        );

        return menuItems;
    }

    renderRightSuffix(): JSX.Element {
        // 这个空view的宽度要有，否则只有一个tab的时候，不是居中的
        if (userCenter.clinic?.isOnlyRetail) return <View key={UniqueKey()} style={{ width: Sizes.dp52 }} />;
        const menuItems: MenuItem<number>[] = this._createMenuItemsList();

        if (!menuItems.length) return <View key={UniqueKey()} />;
        // 使用固定尺寸的容器包裹，保持布局稳定
        return (
            <View style={{ width: Sizes.dp52, height: Sizes.dp44, justifyContent: "center", alignItems: "center" }}>
                {!this.currentTabIndex && (
                    <View
                        collapsable={false}
                        onClick={async () => {
                            const select = await AbcPopMenuV2.show(
                                menuItems,
                                { x: Sizes.dp10, y: Sizes.dp42 },
                                { x: Sizes.dp10, y: Sizes.dp42 },
                                { showArrow: true }
                            );
                            switch (select) {
                                case RetailPharmacyMenuItemValue.extractBill: {
                                    this.invoicePageRef.current?.openExtractBill();
                                    break;
                                }
                                case RetailPharmacyMenuItemValue.pharmacyPrescription: {
                                    this.invoicePageRef.current?.openPharmacyPrescription();
                                    break;
                                }
                                case RetailPharmacyMenuItemValue.registerPrescription: {
                                    this.invoicePageRef.current?.openRegisterPrescription();
                                    break;
                                }
                            }
                        }}
                    >
                        <IconFontView name={"import-line"} size={Sizes.dp24} color={Colors.white} />
                    </View>
                )}
            </View>
        );
    }

    createModuleTabList(): ModuleTabItem[] {
        const list = [
            {
                id: 1,
                title: "记录",
                contentRender: () => <RetailPharmacyList ref={this.listPageRef} />,
            },
        ];
        if (this.isRetailPermission) {
            list.unshift({
                id: 0,
                title: "零售开单",
                contentRender: () => (
                    <RetailPharmacyInvoicePage
                        ref={this.invoicePageRef}
                        localDraftId={this.state.localDraftId}
                        chargeSheetId={this.props.chargeSheetId}
                    />
                ),
            });
        }
        return list;
    }
}
