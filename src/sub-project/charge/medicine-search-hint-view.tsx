/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/6/19
 *
 * @description
 */
import { BaseComponent } from "../base-ui/base-component";
import { Text, View } from "@hippy/react";
import React from "react";
import { AssetImageView } from "../base-ui/views/asset-image-view";
import { Colors, Sizes, TextStyles } from "../theme";

interface MedicineSearchHintViewProps {}

export class MedicineSearchHintView extends BaseComponent<MedicineSearchHintViewProps> {
    constructor(props: MedicineSearchHintViewProps) {
        super(props);
    }

    public render(): JSX.Element {
        return (
            <View
                style={{
                    backgroundColor: Colors.contentBgColor,
                    alignItems: "stretch",
                    flex: 1,
                }}
            >
                <Text
                    style={{
                        ...TextStyles.t16NT4,
                        marginTop: Sizes.dp40,
                        marginBottom: Sizes.dp24,
                        textAlign: "center",
                    }}
                >
                    搜索以下内容
                </Text>
                <View
                    style={{
                        flexDirection: "row",
                        justifyContent: "space-between",
                        paddingHorizontal: Sizes.dp60,
                    }}
                >
                    {this._createItem("search_hint_examination", "检查检验")}
                    {this._createItem("search_hint_material", "器械耗材")}
                    {this._createItem("search_hint_other_goods", "其它商品")}
                    {this._createItem("search_hint_treatment", "治疗理疗")}
                </View>
            </View>
        );
    }

    private _createItem(icon: string, text: string): JSX.Element {
        return (
            <View style={{ alignItems: "center" }}>
                <AssetImageView name={icon} style={{ width: Sizes.dp30, height: Sizes.dp30 }} />
                <Text style={{ ...TextStyles.t14NT4, marginTop: Sizes.dp8 }}>{text}</Text>
            </View>
        );
    }
}
